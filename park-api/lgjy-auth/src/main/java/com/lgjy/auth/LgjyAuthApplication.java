package com.lgjy.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import com.lgjy.common.security.annotation.EnableRyFeignClients;

/**
 * 认证授权中心
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@EnableRyFeignClients
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class LgjyAuthApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjyAuthApplication.class, args);
        System.out.println("认证授权中心启动成功");
    }
}
