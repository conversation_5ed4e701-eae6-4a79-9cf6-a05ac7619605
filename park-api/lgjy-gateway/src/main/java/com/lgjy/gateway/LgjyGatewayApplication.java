package com.lgjy.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 网关启动程序
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class LgjyGatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjyGatewayApplication.class, args);
        System.out.println("网关启动成功");
    }
}
