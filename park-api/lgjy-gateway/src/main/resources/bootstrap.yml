# Spring Boot Bootstrap Configuration
# 核心配置：仅包含启动必需的配置，业务配置已迁移到Nacos配置中心

# 服务器配置 - 必须在bootstrap阶段加载
server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    # 应用名称 - 服务标识，不可迁移
    name: ${SPRING_APPLICATION_NAME:park-gateway}
  profiles:
    # 环境配置 - 通过环境变量控制，决定加载哪个Nacos配置
    active: ${SPRING_PROFILES_ACTIVE:dev}
  cloud:
    nacos:
      discovery:
        # 服务注册地址 - Nacos连接配置，不可迁移
        server-addr: ${SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR:127.0.0.1:8848}
        username: ${SPRING_CLOUD_NACOS_DISCOVERY_USERNAME:nacos}
        password: ${SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD:nacos}
      config:
        # 配置中心地址 - Nacos连接配置，不可迁移
        server-addr: ${SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR:127.0.0.1:8848}
        username: ${SPRING_CLOUD_NACOS_CONFIG_USERNAME:nacos}
        password: ${SPRING_CLOUD_NACOS_CONFIG_PASSWORD:nacos}
        # 配置文件格式
        file-extension: yml
        # 配置组
        group: DEFAULT_GROUP
        # 命名空间
        namespace: ${SPRING_CLOUD_NACOS_CONFIG_NAMESPACE:}
        # 是否启用配置中心
        enabled: true
        # 是否启用远程同步配置
        refresh-enabled: true
        # 共享配置
        shared-configs:
          - data-id: common-${spring.profiles.active}.yml
            group: DEFAULT_GROUP
            refresh: true

# 注意：以下配置已迁移到Nacos配置中心进行统一管理
# - server.port (服务器端口)
# - spring.redis.* (Redis配置)
# - spring.cloud.gateway.* (网关路由配置)
# - security.* (安全配置)
# - management.* (管理端点配置)
