package com.lgjy.file;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class LgjyFileApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjyFileApplication.class, args);
        System.out.println("文件服务模块启动成功");
    }
}
