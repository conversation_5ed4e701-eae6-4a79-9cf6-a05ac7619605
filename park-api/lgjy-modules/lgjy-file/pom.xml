<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.lgjy</groupId>
        <artifactId>lgjy-modules</artifactId>
        <version>3.6.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lgjy-modules-file</artifactId>

    <description>
        lgjy-modules-file文件服务
    </description>

    <dependencies>
    	
    	<!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        
        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
		
        <!-- FastDFS -->
        <dependency>
            <groupId>com.github.tobato</groupId>
            <artifactId>fastdfs-client</artifactId>
        </dependency>
        
        <!-- Minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        
        <!-- RuoYi Api System -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-api-system</artifactId>
        </dependency>
        
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Docker Maven Plugin -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <!-- 支持profile控制Docker构建 -->
                    <skip>${docker.skip}</skip>
                    <skipBuild>${docker.skipBuild}</skipBuild>
                    <skipPush>${docker.skipPush}</skipPush>
                    <images>
                        <image>
                            <name>${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                            <build>
                                <from>${docker.base.image}</from>
                                <assembly>
                                    <descriptorRef>artifact-with-dependencies</descriptorRef>
                                    <targetDir>/app</targetDir>
                                </assembly>
                                <cmd>java -jar /app/${project.artifactId}.jar</cmd>
                                <ports>
                                    <port>9202</port>
                                </ports>
                                <env>
                                    <JAVA_OPTS>-Xms256m -Xmx512m -Dspring.profiles.active=prod</JAVA_OPTS>
                                </env>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>
   
</project>