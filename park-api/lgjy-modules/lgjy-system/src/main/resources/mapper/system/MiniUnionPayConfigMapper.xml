<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniUnionPayConfigMapper">

    <resultMap type="MiniUnionPayConfig" id="MiniUnionPayConfigResult">
        <result property="id"    column="id"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="payType"    column="pay_type"    />
        <result property="mid"    column="mid"    />
        <result property="tid"    column="tid"    />
        <result property="remark"    column="remark"    />
        <result property="deleteFlag"    column="delete_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createByName"    column="create_by_name"    />
        <result property="updateByName"    column="update_by_name"    />
    </resultMap>

    <sql id="selectMiniUnionPayConfigVo">
        select u.id, u.warehouse_id, w.warehouse_name, u.pay_type, u.mid, u.tid, u.remark, u.delete_flag,
               u.create_by, u.create_time, u.update_by, u.update_time,
               coalesce(u1.nick_name, u1.user_name, u.create_by) as create_by_name,
               coalesce(u2.nick_name, u2.user_name, u.update_by) as update_by_name
        from mini_union_pay_config u
        left join mini_warehouse w on u.warehouse_id = w.id and w.delete_flag = 0
        left join sys_user u1 on u1.user_id = u.create_by
        left join sys_user u2 on u2.user_id = u.update_by
        where u.delete_flag = 0
    </sql>

    <select id="selectMiniUnionPayConfigList" parameterType="MiniUnionPayConfig" resultMap="MiniUnionPayConfigResult">
        <include refid="selectMiniUnionPayConfigVo"/>
        <if test="warehouseId != null "> and u.warehouse_id = #{warehouseId}</if>
        <if test="warehouseName != null  and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
        <if test="payType != null "> and u.pay_type = #{payType}</if>
        <if test="mid != null  and mid != ''"> and u.mid like concat('%', #{mid}, '%')</if>
        <if test="tid != null  and tid != ''"> and u.tid like concat('%', #{tid}, '%')</if>
        order by u.create_time desc
    </select>

    <select id="selectMiniUnionPayConfigById" parameterType="Long" resultMap="MiniUnionPayConfigResult">
        <include refid="selectMiniUnionPayConfigVo"/>
        and u.id = #{id}
    </select>

    <select id="selectMiniUnionPayConfigByWarehouseId" parameterType="Long" resultMap="MiniUnionPayConfigResult">
        <include refid="selectMiniUnionPayConfigVo"/>
        and u.warehouse_id = #{warehouseId}
    </select>

    <select id="checkMidUnique" resultType="int">
        select count(1) from mini_union_pay_config
        where mid = #{mid} and delete_flag = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="checkTidUnique" resultType="int">
        select count(1) from mini_union_pay_config
        where tid = #{tid} and delete_flag = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertMiniUnionPayConfig" parameterType="MiniUnionPayConfig">
        insert into mini_union_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="payType != null">pay_type,</if>
            <if test="mid != null and mid != ''">mid,</if>
            <if test="tid != null and tid != ''">tid,</if>
            <if test="remark != null">remark,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="payType != null">#{payType},</if>
            <if test="mid != null and mid != ''">#{mid},</if>
            <if test="tid != null and tid != ''">#{tid},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
         </trim>
    </insert>

    <update id="updateMiniUnionPayConfig" parameterType="MiniUnionPayConfig">
        update mini_union_pay_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="mid != null and mid != ''">mid = #{mid},</if>
            <if test="tid != null and tid != ''">tid = #{tid},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMiniUnionPayConfigById" parameterType="Long">
        update mini_union_pay_config set delete_flag = 1 where id = #{id}
    </update>

    <update id="deleteMiniUnionPayConfigByIds" parameterType="String">
        update mini_union_pay_config set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
