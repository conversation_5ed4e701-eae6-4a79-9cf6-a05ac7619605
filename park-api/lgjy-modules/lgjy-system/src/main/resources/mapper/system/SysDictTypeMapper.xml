<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.SysDictTypeMapper">

	<resultMap type="SysDictType" id="SysDictTypeResult">
		<id     property="dictId"     column="dict_id"     />
		<result property="dictName"   column="dict_name"   />
		<result property="dictType"   column="dict_type"   />
		<result property="status"     column="status"      />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>

	<sql id="selectDictTypeVo">
        select t.dict_id, t.dict_name, t.dict_type, t.status,
               t.create_by, coalesce(u1.nick_name, u1.user_name, CAST(t.create_by AS CHAR)) as create_by_name, t.create_time,
               t.update_by, coalesce(u2.nick_name, u2.user_name, CAST(t.update_by AS CHAR)) as update_by_name, t.update_time, t.remark
		from sys_dict_type t
		left join sys_user u1 on u1.user_id = t.create_by and u1.delete_flag = '0'
		left join sys_user u2 on u2.user_id = t.update_by and u2.delete_flag = '0'
    </sql>

	<select id="selectDictTypeList" parameterType="SysDictType" resultMap="SysDictTypeResult">
	    <include refid="selectDictTypeVo"/>
		<where>
		    <if test="dictName != null and dictName != ''">
				AND t.dict_name like concat('%', #{dictName}, '%')
			</if>
			<if test="status != null and status != ''">
				AND t.status = #{status}
			</if>
			<if test="dictType != null and dictType != ''">
				AND t.dict_type like concat('%', #{dictType}, '%')
			</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				and date_format(t.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				and date_format(t.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
			</if>
	    </where>
	</select>

	<select id="selectDictTypeAll" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
	</select>

	<select id="selectDictTypeById" parameterType="Long" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where t.dict_id = #{dictId}
	</select>

	<select id="selectDictTypeByType" parameterType="String" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where t.dict_type = #{dictType}
	</select>

	<select id="checkDictTypeUnique" parameterType="String" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where t.dict_type = #{dictType} limit 1
	</select>

	<delete id="delete_flagictTypeById" parameterType="Long">
 		delete from sys_dict_type where dict_id = #{dictId}
 	</delete>

 	<delete id="delete_flagictTypeByIds" parameterType="Long">
 		delete from sys_dict_type where dict_id in
 		<foreach collection="array" item="dictId" open="(" separator="," close=")">
 			#{dictId}
        </foreach>
 	</delete>

 	<update id="updateDictType" parameterType="SysDictType">
 		update sys_dict_type
 		<set>
 			<if test="dictName != null and dictName != ''">dict_name = #{dictName},</if>
 			<if test="dictType != null and dictType != ''">dict_type = #{dictType},</if>
 			<if test="status != null">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where dict_id = #{dictId}
	</update>

 	<insert id="insertDictType" parameterType="SysDictType">
 		insert into sys_dict_type(
 			<if test="dictName != null and dictName != ''">dict_name,</if>
 			<if test="dictType != null and dictType != ''">dict_type,</if>
 			<if test="status != null">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="dictName != null and dictName != ''">#{dictName},</if>
 			<if test="dictType != null and dictType != ''">#{dictType},</if>
 			<if test="status != null">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

</mapper>