<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.DashboardMapper">

    <!-- 获取总场库数量 -->
    <select id="getTotalWarehouses" resultType="java.lang.Long">
        <choose>
            <when test="warehouseId != null">
                <!-- 如果指定了场库ID，返回1（表示当前选中的场库） -->
                SELECT 1
            </when>
            <otherwise>
                <!-- 如果没有指定场库ID，返回总场库数量 -->
                SELECT COUNT(*)
                FROM mini_warehouse
                WHERE delete_flag = 0
            </otherwise>
        </choose>
    </select>

    <!-- 获取总车位数量 -->
    <select id="getTotalParkingSpaces" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_parking), 0)
        FROM mini_warehouse
        WHERE delete_flag = 0
          AND total_parking IS NOT NULL
        <if test="warehouseId != null">
            AND id = #{warehouseId}
        </if>
    </select>

    <!-- 获取会员总数量 -->
    <select id="getTotalMembers" resultType="java.lang.Long">
        <choose>
            <when test="warehouseId != null">
                <!-- 获取特定场库的会员数量，通过mini_special_user_car表关联 -->
                SELECT COUNT(DISTINCT msu.id)
                FROM mini_special_user msu
                INNER JOIN mini_special_user_car msuc ON msu.id = msuc.special_user_id
                WHERE msu.delete_flag = 0
                  AND msuc.delete_flag = 0
                  AND msuc.warehouse_id = #{warehouseId}
            </when>
            <otherwise>
                <!-- 获取全系统会员数量 -->
                SELECT COUNT(*)
                FROM mini_special_user
                WHERE delete_flag = 0
            </otherwise>
        </choose>
    </select>

    <!-- 获取小程序用户总数 -->
    <select id="getTotalAppUsers" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM wx_user
        WHERE delete_flag = 0
        <if test="warehouseId != null">
            <!-- TODO: 根据实际业务需求添加场库关联条件，如通过停车记录关联 -->
            AND EXISTS (
                SELECT 1 FROM mini_parking_order mpo
                WHERE mpo.user_id = wx_user.id
                AND mpo.warehouse_id = #{warehouseId}
                AND mpo.delete_flag = 0
            )
        </if>
    </select>

    <!-- 获取总收入 -->
    <select id="getTotalRevenue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(actual_payment), 0) 
        FROM mini_parking_order 
        WHERE delete_flag = 0 
          AND pay_status = 2
          AND create_time BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
    </select>

    <!-- 获取收入趋势数据 -->
    <select id="getRevenueTrendData" resultType="java.util.Map">
        SELECT 
            ${groupBy} as period,
            COALESCE(SUM(actual_payment), 0) as revenue
        FROM mini_parking_order 
        WHERE delete_flag = 0 
          AND pay_status = 2
          AND create_time BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        GROUP BY ${groupBy}
        ORDER BY period
    </select>

    <!-- 获取会员增长趋势数据 -->
    <select id="getMemberTrendData" resultType="java.util.Map">
        SELECT 
            ${groupBy} as period,
            COUNT(*) as count
        FROM mini_special_user 
        WHERE delete_flag = 0
          AND create_time BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            <!-- 这里可以根据实际业务逻辑添加场库关联条件 -->
        </if>
        GROUP BY ${groupBy}
        ORDER BY period
    </select>

    <!-- 获取小程序用户增长趋势数据 -->
    <select id="getAppUserTrendData" resultType="java.util.Map">
        SELECT 
            ${groupBy} as period,
            COUNT(*) as count
        FROM wx_user 
        WHERE delete_flag = 0
          AND create_time BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            <!-- 这里可以根据实际业务逻辑添加场库关联条件 -->
        </if>
        GROUP BY ${groupBy}
        ORDER BY period
    </select>

    <!-- 获取场库列表 -->
    <select id="getWarehouseList" resultType="com.lgjy.system.domain.MiniWarehouse">
        SELECT
            id,
            warehouse_name as warehouseName,
            parent_id as parentId,
            total_parking as totalParking,
            address
        FROM mini_warehouse
        WHERE delete_flag = 0
          AND status = 1
        ORDER BY parent_id, create_time DESC
    </select>

    <!-- 获取今日新增订单数量 -->
    <select id="getTodayNewOrders" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM mini_parking_order 
        WHERE delete_flag = 0 
          AND DATE(create_time) = CURDATE()
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
    </select>

    <!-- 获取本月新增订单数量 -->
    <select id="getMonthNewOrders" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM mini_parking_order 
        WHERE delete_flag = 0 
          AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
    </select>

    <!-- 获取今日新增用户数量 -->
    <select id="getTodayNewUsers" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM wx_user 
        WHERE delete_flag = 0 
          AND DATE(create_time) = CURDATE()
        <if test="warehouseId != null">
            <!-- 这里可以根据实际业务逻辑添加场库关联条件 -->
        </if>
    </select>

    <!-- 获取本月新增用户数量 -->
    <select id="getMonthNewUsers" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM wx_user 
        WHERE delete_flag = 0 
          AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
        <if test="warehouseId != null">
            <!-- 这里可以根据实际业务逻辑添加场库关联条件 -->
        </if>
    </select>

    <!-- 获取订单状态统计 -->
    <select id="getOrderStatusStats" resultType="java.util.Map">
        SELECT
            pay_status as status,
            COUNT(*) as count
        FROM mini_parking_order
        WHERE delete_flag = 0
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        GROUP BY pay_status
    </select>

    <!-- 批量获取基础统计数据 - 优化性能 -->
    <select id="getBatchStatistics" resultType="java.util.Map">
        SELECT
            'warehouses' as stat_type,
            <choose>
                <when test="warehouseId != null">1</when>
                <otherwise>(SELECT COUNT(*) FROM mini_warehouse WHERE delete_flag = 0)</otherwise>
            </choose> as stat_value
        UNION ALL
        SELECT
            'parking_spaces' as stat_type,
            COALESCE((
                SELECT SUM(total_parking)
                FROM mini_warehouse
                WHERE delete_flag = 0
                  AND total_parking IS NOT NULL
                <if test="warehouseId != null">
                  AND id = #{warehouseId}
                </if>
            ), 0) as stat_value
        UNION ALL
        SELECT
            'members' as stat_type,
            <choose>
                <when test="warehouseId != null">
                    (SELECT COUNT(DISTINCT msu.id)
                     FROM mini_special_user msu
                     INNER JOIN mini_special_user_car msuc ON msu.id = msuc.special_user_id
                     WHERE msu.delete_flag = 0
                       AND msuc.delete_flag = 0
                       AND msuc.warehouse_id = #{warehouseId}
                    )
                </when>
                <otherwise>
                    (SELECT COUNT(*)
                     FROM mini_special_user
                     WHERE delete_flag = 0
                    )
                </otherwise>
            </choose> as stat_value
        UNION ALL
        SELECT
            'app_users' as stat_type,
            (SELECT COUNT(*)
             FROM wx_user
             WHERE delete_flag = 0
             <if test="warehouseId != null">
                 AND EXISTS (
                     SELECT 1 FROM mini_parking_order mpo
                     WHERE mpo.user_id = wx_user.id
                     AND mpo.warehouse_id = #{warehouseId}
                     AND mpo.delete_flag = 0
                 )
             </if>
            ) as stat_value
    </select>

    <!-- 获取支付方式统计 -->
    <select id="getPaymentMethodStats" resultType="java.util.Map">
        SELECT
            pay_type as paymentMethod,
            COUNT(*) as count,
            COALESCE(SUM(actual_payment), 0) as amount
        FROM mini_parking_order
        WHERE delete_flag = 0
          AND pay_status = 2
          AND create_time BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        GROUP BY pay_type
    </select>

    <!-- 按日期获取收入趋势数据（真实数据） -->
    <select id="getRevenueTrendByDate" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COALESCE(SUM(actual_payment), 0) as revenue
        FROM mini_parking_order
        WHERE delete_flag = 0
          AND pay_status = 2
          AND DATE(create_time) BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date
    </select>

    <!-- 按日期获取会员增长数据（真实数据） -->
    <select id="getMemberGrowthByDate" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COUNT(*) as count
        FROM mini_special_user
        WHERE delete_flag = 0
          AND DATE(create_time) BETWEEN #{startDate} AND #{endDate}
        <if test="warehouseId != null">
            AND EXISTS (
                SELECT 1 FROM mini_special_user_car msuc
                WHERE msuc.special_user_id = mini_special_user.id
                AND msuc.warehouse_id = #{warehouseId}
                AND msuc.delete_flag = 0
            )
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date
    </select>

    <!-- 按日期获取小程序用户增长数据（真实数据） -->
    <select id="getAppUserGrowthByDate" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COUNT(*) as count
        FROM wx_user
        WHERE delete_flag = 0
          AND DATE(create_time) BETWEEN #{startDate} AND #{endDate}
        GROUP BY DATE(create_time)
        ORDER BY date
    </select>

    <!-- 获取历史会员数量（用于计算增长率） -->
    <select id="getTotalMembersHistorical" resultType="java.lang.Long">
        <choose>
            <when test="warehouseId != null">
                <!-- 获取特定场库的历史会员数量 -->
                SELECT COUNT(DISTINCT msu.id)
                FROM mini_special_user msu
                INNER JOIN mini_special_user_car msuc ON msu.id = msuc.special_user_id
                WHERE msu.delete_flag = 0
                  AND msuc.delete_flag = 0
                  AND msuc.warehouse_id = #{warehouseId}
                  AND msu.create_time &lt;= #{endDate}
            </when>
            <otherwise>
                <!-- 获取全系统历史会员数量 -->
                SELECT COUNT(*)
                FROM mini_special_user
                WHERE delete_flag = 0
                  AND create_time &lt;= #{endDate}
            </otherwise>
        </choose>
    </select>

    <!-- 获取历史小程序用户数量（用于计算增长率） -->
    <select id="getTotalAppUsersHistorical" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM wx_user
        WHERE delete_flag = 0
          AND create_time &lt;= #{endDate}
        <if test="warehouseId != null">
            AND EXISTS (
                SELECT 1 FROM mini_parking_order mpo
                WHERE mpo.user_id = wx_user.id
                AND mpo.warehouse_id = #{warehouseId}
                AND mpo.delete_flag = 0
                AND mpo.create_time &lt;= #{endDate}
            )
        </if>
    </select>

</mapper>
