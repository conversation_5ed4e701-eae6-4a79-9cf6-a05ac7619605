<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniWarehouseManagerMapper">

    <resultMap type="com.lgjy.system.domain.MiniWarehouseManager" id="MiniWarehouseManagerResult">
        <result property="id" column="id" />
        <result property="operatorId" column="operator_id" />
        <result property="operatorName" column="operator_name" />
        <result property="warehouseId" column="warehouse_id" />
        <result property="warehouseName" column="warehouse_name" />
        <result property="managerName" column="manager_name" />
        <result property="managerPhone" column="manager_phone" />
        <result property="remark" column="remark" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="createdBy" column="created_by" />
        <result property="updatedBy" column="updated_by" />
    </resultMap>

    <sql id="selectMiniWarehouseManagerVo">
        select m.id, m.operator_id, o.company_name as operator_name, m.warehouse_id, w.warehouse_name,
               m.manager_name, m.manager_phone, m.remark, m.delete_flag,
               m.create_by, m.create_time, m.update_by, m.update_time,
               coalesce(cu.nick_name, cu.user_name, m.create_by) as created_by,
               coalesce(uu.nick_name, uu.user_name, m.update_by) as updated_by
        from mini_warehouse_manager m
        left join mini_operator o on m.operator_id = o.id and o.delete_flag = 0
        left join mini_warehouse w on m.warehouse_id = w.id and w.delete_flag = 0
        left join sys_user cu on m.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on m.update_by = uu.user_id and uu.delete_flag = '0'
    </sql>

    <select id="selectMiniWarehouseManagerList" parameterType="com.lgjy.system.domain.MiniWarehouseManager" resultMap="MiniWarehouseManagerResult">
        <include refid="selectMiniWarehouseManagerVo"/>
        <where>
            m.delete_flag = 0
            <if test="operatorId != null"> and m.operator_id = #{operatorId}</if>
            <if test="operatorName != null and operatorName != ''"> and o.company_name like concat('%', #{operatorName}, '%')</if>
            <if test="warehouseId != null"> and m.warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="managerName != null and managerName != ''"> and m.manager_name like concat('%', #{managerName}, '%')</if>
            <if test="managerPhone != null and managerPhone != ''"> and m.manager_phone like concat('%', #{managerPhone}, '%')</if>
        </where>
        order by m.create_time desc
    </select>

    <select id="selectMiniWarehouseManagerById" parameterType="Long" resultMap="MiniWarehouseManagerResult">
        <include refid="selectMiniWarehouseManagerVo"/>
        where m.id = #{id} and m.delete_flag = 0
    </select>



    <select id="countManagerByWarehouseId" parameterType="Long" resultType="int">
        select count(*) from mini_warehouse_manager
        where warehouse_id = #{warehouseId} and delete_flag = 0
    </select>

    <select id="countManagerByOperatorId" parameterType="Long" resultType="int">
        select count(*) from mini_warehouse_manager
        where operator_id = #{operatorId} and delete_flag = 0
    </select>

    <insert id="insertMiniWarehouseManager" parameterType="com.lgjy.system.domain.MiniWarehouseManager" useGeneratedKeys="true" keyProperty="id">
        insert into mini_warehouse_manager
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operatorId != null">operator_id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="managerName != null and managerName != ''">manager_name,</if>
            <if test="managerPhone != null and managerPhone != ''">manager_phone,</if>
            <if test="remark != null">remark,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operatorId != null">#{operatorId},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="managerName != null and managerName != ''">#{managerName},</if>
            <if test="managerPhone != null and managerPhone != ''">#{managerPhone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            #{createBy},
            now(),
            #{updateBy},
            now()
        </trim>
    </insert>

    <update id="updateMiniWarehouseManager" parameterType="com.lgjy.system.domain.MiniWarehouseManager">
        update mini_warehouse_manager
        <trim prefix="SET" suffixOverrides=",">
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="managerName != null and managerName != ''">manager_name = #{managerName},</if>
            <if test="managerPhone != null and managerPhone != ''">manager_phone = #{managerPhone},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_by = #{updateBy},
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniWarehouseManagerById" parameterType="Long">
        update mini_warehouse_manager set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniWarehouseManagerByIds" parameterType="String">
        update mini_warehouse_manager set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
