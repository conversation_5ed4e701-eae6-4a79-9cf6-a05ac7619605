<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniWhitelistMapper">
    
    <resultMap type="MiniWhitelist" id="MiniWhitelistResult">
        <result property="id"    column="id"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="name"    column="name"    />
        <result property="dlySystemId"    column="dly_system_id"    />
        <result property="parkType"    column="park_type"    />
        <result property="whiteType"    column="white_type"    />
        <result property="remark"    column="remark"    />
        <result property="deleteFlag"    column="delete_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
    </resultMap>

    <sql id="selectMiniWhitelistVo">
        select w.id, w.warehouse_id, w.phone_number, w.begin_time, w.end_time, w.plate_no, w.name,
               w.dly_system_id, w.park_type, w.white_type, w.remark, w.delete_flag, w.create_by,
               w.create_time, w.update_by, w.update_time, wh.warehouse_name, wh.operator_id, op.company_name as operator_name
        from mini_whitelist w
        left join mini_warehouse wh on w.warehouse_id = wh.id
        left join mini_operator op on wh.operator_id = op.id
    </sql>

    <select id="selectMiniWhitelistList" parameterType="MiniWhitelist" resultMap="MiniWhitelistResult">
        <include refid="selectMiniWhitelistVo"/>
        <where>
            w.delete_flag = 0
            <if test="operatorId != null "> and wh.operator_id = #{operatorId}</if>
            <if test="warehouseId != null "> and w.warehouse_id = #{warehouseId}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and w.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and w.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="name != null  and name != ''"> and w.name like concat('%', #{name}, '%')</if>
            <if test="parkType != null "> and w.park_type = #{parkType}</if>
            <if test="whiteType != null  and whiteType != ''"> and w.white_type = #{whiteType}</if>
        </where>
        order by w.create_time desc
    </select>
    
    <select id="selectMiniWhitelistById" parameterType="Long" resultMap="MiniWhitelistResult">
        <include refid="selectMiniWhitelistVo"/>
        where w.id = #{id} and w.delete_flag = 0
    </select>

    <select id="selectMiniWhitelistByPlateNo" parameterType="String" resultMap="MiniWhitelistResult">
        <include refid="selectMiniWhitelistVo"/>
        where w.plate_no = #{plateNo} and w.delete_flag = 0
    </select>

    <select id="selectMiniWhitelistByPlateNoAndWarehouse" resultMap="MiniWhitelistResult">
        <include refid="selectMiniWhitelistVo"/>
        where w.plate_no = #{plateNo} and w.warehouse_id = #{warehouseId} and w.delete_flag = 0
    </select>

    <select id="checkPlateNoUnique" resultMap="MiniWhitelistResult">
        <include refid="selectMiniWhitelistVo"/>
        where w.plate_no = #{plateNo} and w.warehouse_id = #{warehouseId} and w.delete_flag = 0
        limit 1
    </select>
        
    <insert id="insertMiniWhitelist" parameterType="MiniWhitelist" useGeneratedKeys="true" keyProperty="id">
        insert into mini_whitelist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="name != null">name,</if>
            <if test="dlySystemId != null">dly_system_id,</if>
            <if test="parkType != null">park_type,</if>
            <if test="whiteType != null">white_type,</if>
            <if test="remark != null">remark,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="name != null">#{name},</if>
            <if test="dlySystemId != null">#{dlySystemId},</if>
            <if test="parkType != null">#{parkType},</if>
            <if test="whiteType != null">#{whiteType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMiniWhitelist" parameterType="MiniWhitelist">
        update mini_whitelist
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="dlySystemId != null">dly_system_id = #{dlySystemId},</if>
            <if test="parkType != null">park_type = #{parkType},</if>
            <if test="whiteType != null">white_type = #{whiteType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMiniWhitelistById" parameterType="Long">
        update mini_whitelist set delete_flag = 1 where id = #{id}
    </update>

    <update id="deleteMiniWhitelistByIds" parameterType="String">
        update mini_whitelist set delete_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
