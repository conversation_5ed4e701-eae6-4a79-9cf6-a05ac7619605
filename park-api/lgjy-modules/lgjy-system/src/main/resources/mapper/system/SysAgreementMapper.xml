<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.SysAgreementMapper">
    
    <resultMap type="SysAgreement" id="SysAgreementResult">
        <result property="id"    column="id"    />
        <result property="agreementType"    column="agreement_type"    />
        <result property="agreementTitle"    column="agreement_title"    />
        <result property="agreementContent"    column="agreement_content"    />
        <result property="deleteFlag"    column="delete_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysAgreementVo">
        select id, agreement_type, agreement_title, agreement_content, delete_flag, create_time, update_time from sys_agreement
    </sql>

    <select id="selectSysAgreementList" parameterType="SysAgreement" resultMap="SysAgreementResult">
        <include refid="selectSysAgreementVo"/>
        <where>  
            <if test="agreementType != null "> and agreement_type = #{agreementType}</if>
            <if test="agreementTitle != null  and agreementTitle != ''"> and agreement_title like concat('%', #{agreementTitle}, '%')</if>
            <if test="deleteFlag != null "> and delete_flag = #{deleteFlag}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSysAgreementById" parameterType="Long" resultMap="SysAgreementResult">
        <include refid="selectSysAgreementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSysAgreement" parameterType="SysAgreement" useGeneratedKeys="true" keyProperty="id">
        insert into sys_agreement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agreementType != null">agreement_type,</if>
            <if test="agreementTitle != null and agreementTitle != ''">agreement_title,</if>
            <if test="agreementContent != null">agreement_content,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agreementType != null">#{agreementType},</if>
            <if test="agreementTitle != null and agreementTitle != ''">#{agreementTitle},</if>
            <if test="agreementContent != null">#{agreementContent},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysAgreement" parameterType="SysAgreement">
        update sys_agreement
        <trim prefix="SET" suffixOverrides=",">
            <if test="agreementType != null">agreement_type = #{agreementType},</if>
            <if test="agreementTitle != null and agreementTitle != ''">agreement_title = #{agreementTitle},</if>
            <if test="agreementContent != null">agreement_content = #{agreementContent},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAgreementById" parameterType="Long">
        update sys_agreement set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteSysAgreementByIds" parameterType="String">
        update sys_agreement set delete_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
