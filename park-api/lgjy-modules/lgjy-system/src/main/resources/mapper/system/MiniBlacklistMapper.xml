<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniBlacklistMapper">
    
    <resultMap type="MiniBlacklist" id="MiniBlacklistResult">
        <result property="id"    column="id"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="name"    column="name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="deleteFlag"    column="delete_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="operatorName"    column="operator_name"    />
    </resultMap>

    <sql id="selectMiniBlacklistVo">
        select b.id, b.plate_no, b.begin_time, b.end_time, b.warehouse_id, b.name,
               b.phone_number, b.img_url, b.delete_flag, b.create_by,
               b.create_time, b.update_by, b.update_time, wh.warehouse_name, op.company_name as operator_name
        from mini_blacklist b
        left join mini_warehouse wh on b.warehouse_id = wh.id
        left join mini_operator op on wh.operator_id = op.id
    </sql>

    <select id="selectMiniBlacklistList" parameterType="MiniBlacklist" resultMap="MiniBlacklistResult">
        <include refid="selectMiniBlacklistVo"/>
        <where>  
            b.delete_flag = 0
            <if test="warehouseId != null "> and b.warehouse_id = #{warehouseId}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and b.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and b.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="name != null  and name != ''"> and b.name like concat('%', #{name}, '%')</if>
        </where>
        order by b.create_time desc
    </select>
    
    <select id="selectMiniBlacklistById" parameterType="Long" resultMap="MiniBlacklistResult">
        <include refid="selectMiniBlacklistVo"/>
        where b.id = #{id} and b.delete_flag = 0
    </select>

    <select id="selectMiniBlacklistByPlateNo" parameterType="String" resultMap="MiniBlacklistResult">
        <include refid="selectMiniBlacklistVo"/>
        where b.plate_no = #{plateNo} and b.delete_flag = 0
    </select>

    <select id="selectMiniBlacklistByPlateNoAndWarehouse" resultMap="MiniBlacklistResult">
        <include refid="selectMiniBlacklistVo"/>
        where b.plate_no = #{plateNo} and b.warehouse_id = #{warehouseId} and b.delete_flag = 0
    </select>

    <select id="checkPlateNoUnique" resultMap="MiniBlacklistResult">
        <include refid="selectMiniBlacklistVo"/>
        where b.plate_no = #{plateNo} and b.warehouse_id = #{warehouseId} and b.delete_flag = 0
        limit 1
    </select>
        
    <insert id="insertMiniBlacklist" parameterType="MiniBlacklist" useGeneratedKeys="true" keyProperty="id">
        insert into mini_blacklist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="plateNo != null">plate_no,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="name != null">name,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="plateNo != null">#{plateNo},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="name != null">#{name},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMiniBlacklist" parameterType="MiniBlacklist">
        update mini_blacklist
        <trim prefix="SET" suffixOverrides=",">
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMiniBlacklistById" parameterType="Long">
        update mini_blacklist set delete_flag = 1 where id = #{id}
    </update>

    <update id="deleteMiniBlacklistByIds" parameterType="String">
        update mini_blacklist set delete_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
