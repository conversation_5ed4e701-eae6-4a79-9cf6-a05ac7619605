<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniVipMemberMapper">

    <resultMap type="com.lgjy.system.domain.MiniVipMember" id="MiniVipMemberResult">
        <result property="id" column="id" />
        <result property="warehouseId" column="warehouse_id" />
        <result property="warehouseName" column="warehouse_name" />
        <result property="operatorName" column="operator_name" />
        <result property="packageName" column="package_name" />
        <result property="userId" column="user_id" />
        <result property="phoneNumber" column="phone_number" />
        <result property="plateNo" column="plate_no" />
        <result property="beginVipTime" column="begin_vip_time" />
        <result property="endVipTime" column="end_vip_time" />
        <result property="dlySystemId" column="dly_system_id" />
        <result property="vipType" column="vip_type" />
        <result property="remark" column="remark" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="parkingSpaceNo" column="parking_space_no" />
    </resultMap>

    <sql id="selectMiniVipMemberVo">
        select vm.id, vm.warehouse_id, vm.user_id, vm.phone_number, vm.plate_no, vm.begin_vip_time,
               vm.end_vip_time, vm.dly_system_id, vm.vip_type, vm.remark, vm.parking_space_no, vm.delete_flag,
               vm.create_by, coalesce(cu.nick_name, cu.user_name, vm.create_by) as create_by_name, vm.create_time,
               vm.update_by, coalesce(uu.nick_name, uu.user_name, vm.update_by) as update_by_name, vm.update_time,
               w.warehouse_name, op.company_name as operator_name,
               '' as package_name
        from mini_vip_package_user_detail vm
        left join sys_user cu on vm.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on vm.update_by = uu.user_id and uu.delete_flag = '0'
        left join mini_warehouse w on vm.warehouse_id = w.id and w.delete_flag = 0
        left join mini_operator op on w.operator_id = op.id and op.delete_flag = 0
    </sql>

    <select id="selectMiniVipMemberList" parameterType="com.lgjy.system.domain.MiniVipMember" resultMap="MiniVipMemberResult">
        <include refid="selectMiniVipMemberVo"/>
        <where>
            vm.delete_flag = 0
            <if test="operatorId != null"> and w.operator_id = #{operatorId}</if>
            <if test="warehouseId != null"> and vm.warehouse_id = #{warehouseId}</if>
            <if test="phoneNumber != null and phoneNumber != ''"> and vm.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="plateNo != null and plateNo != ''"> and vm.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="vipType != null"> and vm.vip_type = #{vipType}</if>
        </where>
        order by vm.create_time desc
    </select>

    <select id="selectMiniVipMemberById" parameterType="Long" resultMap="MiniVipMemberResult">
        <include refid="selectMiniVipMemberVo"/>
        where vm.id = #{id} and vm.delete_flag = 0
    </select>

    <select id="selectMemberByPhoneNumber" parameterType="String" resultMap="MiniVipMemberResult">
        <include refid="selectMiniVipMemberVo"/>
        where vm.phone_number = #{phoneNumber} and vm.delete_flag = 0 limit 1
    </select>

    <select id="selectMemberByPlateNoAndWarehouse" resultMap="MiniVipMemberResult">
        <include refid="selectMiniVipMemberVo"/>
        where vm.plate_no = #{plateNo} and vm.warehouse_id = #{warehouseId} and vm.delete_flag = 0
        and vm.end_vip_time > NOW()
        order by vm.end_vip_time desc limit 1
    </select>

    <select id="selectMembersByWarehouseId" parameterType="Long" resultMap="MiniVipMemberResult">
        <include refid="selectMiniVipMemberVo"/>
        where vm.warehouse_id = #{warehouseId} and vm.delete_flag = 0
        order by vm.create_time desc
    </select>

    <select id="selectExpiringMembers" parameterType="Integer" resultMap="MiniVipMemberResult">
        <include refid="selectMiniVipMemberVo"/>
        where vm.delete_flag = 0
        and vm.end_vip_time is not null
        and vm.end_vip_time between now() and date_add(now(), interval #{days} day)
        order by vm.end_vip_time asc
    </select>

    <select id="countMembersByType" parameterType="Long" resultType="java.util.Map">
        select
            case vm.vip_type
                when 0 then '普通会员'
                when 1 then '集团客户'
                when 2 then 'VIP客户'
                else '未知类型'
            end as packageName,
            count(*) as count
        from mini_vip_package_user_detail vm
        where vm.delete_flag = 0
        <if test="warehouseId != null"> and vm.warehouse_id = #{warehouseId}</if>
        group by vm.vip_type
        order by vm.vip_type
    </select>

    <insert id="insertMiniVipMember" parameterType="com.lgjy.system.domain.MiniVipMember">
        insert into mini_vip_package_user_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="plateNo != null and plateNo != ''">plate_no,</if>
            <if test="beginVipTime != null">begin_vip_time,</if>
            <if test="endVipTime != null">end_vip_time,</if>
            <if test="dlySystemId != null">dly_system_id,</if>
            <if test="vipType != null">vip_type,</if>
            <if test="remark != null">remark,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">#{plateNo},</if>
            <if test="beginVipTime != null">#{beginVipTime},</if>
            <if test="endVipTime != null">#{endVipTime},</if>
            <if test="dlySystemId != null">#{dlySystemId},</if>
            <if test="vipType != null">#{vipType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateMiniVipMember" parameterType="com.lgjy.system.domain.MiniVipMember">
        update mini_vip_package_user_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">plate_no = #{plateNo},</if>
            <if test="beginVipTime != null">begin_vip_time = #{beginVipTime},</if>
            <if test="endVipTime != null">end_vip_time = #{endVipTime},</if>
            <if test="dlySystemId != null">dly_system_id = #{dlySystemId},</if>
            <if test="vipType != null">vip_type = #{vipType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniVipMemberById" parameterType="Long">
        update mini_vip_package_user_detail set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniVipMemberByIds" parameterType="String">
        update mini_vip_package_user_detail set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
