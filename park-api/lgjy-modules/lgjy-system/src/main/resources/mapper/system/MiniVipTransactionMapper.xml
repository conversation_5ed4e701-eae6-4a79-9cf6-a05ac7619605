<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniVipTransactionMapper">

    <resultMap type="com.lgjy.system.domain.MiniVipTransaction" id="MiniVipTransactionResult">
        <result property="id" column="id" />
        <result property="warehouseId" column="warehouse_id" />
        <result property="packageId" column="package_id" />
        <result property="userId" column="user_id" />
        <result property="phoneNumber" column="phone_number" />
        <result property="plateNo" column="plate_no" />
        <result property="operateType" column="operate_type" />
        <result property="transactTime" column="transact_time" />
        <result property="beginVipTime" column="begin_vip_time" />
        <result property="expirationTime" column="expiration_time" />
        <result property="paymentAmount" column="payment_amount" />
        <result property="discountAmount" column="discount_amount" />
        <result property="actualPayment" column="actual_payment" />
        <result property="tradeId" column="trade_id" />
        <result property="payStatus" column="pay_status" />
        <result property="invoiceId" column="invoice_id" />
        <result property="parkingSpaceNo" column="parking_space_no" />
        <result property="groupBuyRecordId" column="group_buy_record_id" />
        <result property="chooseTime" column="choose_time" />
        <result property="vipType" column="vip_type" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="warehouseName" column="warehouse_name" />
        <result property="packageName" column="package_name" />
    </resultMap>

    <sql id="selectMiniVipTransactionVo">
        select t.id, t.warehouse_id, t.package_id, t.user_id, t.phone_number, t.plate_no,
               t.operate_type, t.transact_time, t.begin_vip_time, t.expiration_time, t.payment_amount, t.discount_amount, t.actual_payment,
               t.trade_id, t.pay_status, t.invoice_id, t.parking_space_no, t.group_buy_record_id,
               t.choose_time, t.vip_type, t.delete_flag,
               t.create_by, coalesce(cu.nick_name, cu.user_name, t.create_by) as create_by_name, t.create_time,
               t.update_by, coalesce(uu.nick_name, uu.user_name, t.update_by) as update_by_name, t.update_time,
               w.warehouse_name, p.package_name
        from mini_vip_transact_record t
        left join sys_user cu on t.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on t.update_by = uu.user_id and uu.delete_flag = '0'
        left join mini_warehouse w on t.warehouse_id = w.id and w.delete_flag = 0
        left join mini_vip_package p on t.package_id = p.id and p.delete_flag = 0
    </sql>

    <select id="selectMiniVipTransactionList" parameterType="com.lgjy.system.domain.MiniVipTransaction" resultMap="MiniVipTransactionResult">
        <include refid="selectMiniVipTransactionVo"/>
        <where>
            t.delete_flag = 0
            <if test="warehouseId != null"> and t.warehouse_id = #{warehouseId}</if>
            <if test="packageId != null"> and t.package_id = #{packageId}</if>
            <if test="phoneNumber != null and phoneNumber != ''"> and t.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="plateNo != null and plateNo != ''"> and t.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="payStatus != null"> and t.pay_status = #{payStatus}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectMiniVipTransactionById" parameterType="Long" resultMap="MiniVipTransactionResult">
        <include refid="selectMiniVipTransactionVo"/>
        where t.id = #{id} and t.delete_flag = 0
    </select>

    <select id="checkTransactionNoUnique" parameterType="String" resultMap="MiniVipTransactionResult">
        <include refid="selectMiniVipTransactionVo"/>
        where t.trade_id = #{tradeId} and t.delete_flag = 0 limit 1
    </select>

    <select id="selectTransactionsByMemberId" parameterType="Long" resultMap="MiniVipTransactionResult">
        <include refid="selectMiniVipTransactionVo"/>
        where t.user_id = #{memberId} and t.delete_flag = 0
        order by t.create_time desc
    </select>

    <select id="selectTransactionsByPackageId" parameterType="Long" resultMap="MiniVipTransactionResult">
        <include refid="selectMiniVipTransactionVo"/>
        where t.package_id = #{packageId} and t.delete_flag = 0
        order by t.create_time desc
    </select>

    <select id="selectTransactionsByTimeRange" resultMap="MiniVipTransactionResult">
        <include refid="selectMiniVipTransactionVo"/>
        where t.delete_flag = 0
        and t.create_time between #{startTime} and #{endTime}
        order by t.create_time desc
    </select>

    <select id="sumAmountByStatus" parameterType="java.util.Map" resultType="java.util.Map">
        select pay_status, sum(actual_payment) as total_amount, count(*) as count
        from mini_vip_transact_record
        where delete_flag = 0
        <if test="warehouseId != null"> and warehouse_id = #{warehouseId}</if>
        group by pay_status
        order by pay_status
    </select>

    <select id="countTransactionsByType" parameterType="java.util.Map" resultType="java.util.Map">
        select vip_type, count(*) as count
        from mini_vip_transact_record
        where delete_flag = 0
        <if test="warehouseId != null"> and warehouse_id = #{warehouseId}</if>
        group by vip_type
        order by vip_type
    </select>

    <insert id="insertMiniVipTransaction" parameterType="com.lgjy.system.domain.MiniVipTransaction" useGeneratedKeys="true" keyProperty="id">
        insert into mini_vip_transact_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="plateNo != null and plateNo != ''">plate_no,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="transactTime != null">transact_time,</if>
            <if test="beginVipTime != null">begin_vip_time,</if>
            <if test="expirationTime != null">expiration_time,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="actualPayment != null">actual_payment,</if>
            <if test="tradeId != null and tradeId != ''">trade_id,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="parkingSpaceNo != null">parking_space_no,</if>
            <if test="groupBuyRecordId != null">group_buy_record_id,</if>
            <if test="chooseTime != null">choose_time,</if>
            <if test="vipType != null">vip_type,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">#{plateNo},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="transactTime != null">#{transactTime},</if>
            <if test="beginVipTime != null">#{beginVipTime},</if>
            <if test="expirationTime != null">#{expirationTime},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="discountAmount != null">#{discountAmount},</if>
            <if test="actualPayment != null">#{actualPayment},</if>
            <if test="tradeId != null and tradeId != ''">#{tradeId},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="parkingSpaceNo != null">#{parkingSpaceNo},</if>
            <if test="groupBuyRecordId != null">#{groupBuyRecordId},</if>
            <if test="chooseTime != null">#{chooseTime},</if>
            <if test="vipType != null">#{vipType},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateMiniVipTransaction" parameterType="com.lgjy.system.domain.MiniVipTransaction">
        update mini_vip_transact_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">plate_no = #{plateNo},</if>
            <if test="operateType != null">operate_type = #{operateType},</if>
            <if test="transactTime != null">transact_time = #{transactTime},</if>
            <if test="beginVipTime != null">begin_vip_time = #{beginVipTime},</if>
            <if test="expirationTime != null">expiration_time = #{expirationTime},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="actualPayment != null">actual_payment = #{actualPayment},</if>
            <if test="tradeId != null and tradeId != ''">trade_id = #{tradeId},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
            <if test="parkingSpaceNo != null">parking_space_no = #{parkingSpaceNo},</if>
            <if test="groupBuyRecordId != null">group_buy_record_id = #{groupBuyRecordId},</if>
            <if test="chooseTime != null">choose_time = #{chooseTime},</if>
            <if test="vipType != null">vip_type = #{vipType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniVipTransactionById" parameterType="Long">
        update mini_vip_transact_record set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniVipTransactionByIds" parameterType="String">
        update mini_vip_transact_record set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="calculateMemberTotalConsumption" parameterType="Long" resultType="java.math.BigDecimal">
        select coalesce(sum(actual_payment), 0)
        from mini_vip_transact_record
        where user_id = #{memberId} and delete_flag = 0 and pay_status = 5
    </select>
</mapper>
