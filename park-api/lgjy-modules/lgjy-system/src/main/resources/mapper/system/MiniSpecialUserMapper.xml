<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniSpecialUserMapper">

    <resultMap type="com.lgjy.system.domain.MiniSpecialUser" id="MiniSpecialUserResult">
        <result property="id" column="id" />
        <result property="nickName" column="nick_name" />
        <result property="phoneNumber" column="phone_number" />
        <result property="plateNo" column="plate_no" />
        <result property="userType" column="user_type" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createBy" column="create_by" />
        <result property="createByName" column="create_by_name" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateByName" column="update_by_name" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectMiniSpecialUserVo">
        select su.id, su.nick_name, su.phone_number, su.plate_no, su.user_type, su.delete_flag,
               su.create_by, coalesce(cu.nick_name, cu.user_name, su.create_by) as create_by_name, su.create_time,
               su.update_by, coalesce(uu.nick_name, uu.user_name, su.update_by) as update_by_name, su.update_time
        from mini_special_user su
        left join sys_user cu on su.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on su.update_by = uu.user_id and uu.delete_flag = '0'
    </sql>

    <select id="selectMiniSpecialUserList" parameterType="com.lgjy.system.domain.MiniSpecialUser" resultMap="MiniSpecialUserResult">
        <include refid="selectMiniSpecialUserVo"/>
        <where>
            su.delete_flag = 0
            <if test="nickName != null and nickName != ''"> and su.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''"> and su.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="plateNo != null and plateNo != ''"> and su.plate_no like concat('%', #{plateNo}, '%')</if>
            <if test="userType != null and userType != ''"> and su.user_type = #{userType}</if>
        </where>
        order by su.create_time desc
    </select>

    <select id="selectMiniSpecialUserById" parameterType="Long" resultMap="MiniSpecialUserResult">
        <include refid="selectMiniSpecialUserVo"/>
        where su.id = #{id} and su.delete_flag = 0
    </select>



    <select id="checkPhoneNumberUnique" parameterType="String" resultMap="MiniSpecialUserResult">
        <include refid="selectMiniSpecialUserVo"/>
        where su.phone_number = #{phoneNumber} and su.delete_flag = 0 limit 1
    </select>

    <select id="checkPlateNoUnique" parameterType="String" resultMap="MiniSpecialUserResult">
        <include refid="selectMiniSpecialUserVo"/>
        where su.plate_no = #{plateNo} and su.delete_flag = 0 limit 1
    </select>



    <insert id="insertMiniSpecialUser" parameterType="com.lgjy.system.domain.MiniSpecialUser">
        insert into mini_special_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="plateNo != null and plateNo != ''">plate_no,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">#{plateNo},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateMiniSpecialUser" parameterType="com.lgjy.system.domain.MiniSpecialUser">
        update mini_special_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">plate_no = #{plateNo},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniSpecialUserByIds" parameterType="String">
        update mini_special_user set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countSpecialUsersByType" resultType="java.util.Map">
        SELECT user_type as userType, COUNT(*) as count
        FROM mini_special_user
        WHERE delete_flag = 0
        GROUP BY user_type
        ORDER BY count DESC
    </select>

    <update id="updateSpecialUserCarPhoneNumbers">
        UPDATE mini_special_user_car
        SET phone_number = CONCAT(LEFT(phone_number, 1), #{newPhoneSuffix}), update_time = NOW()
        WHERE RIGHT(phone_number, 10) = #{oldPhoneSuffix} AND delete_flag = 0
    </update>

    <select id="countSpecialUserCarsByUserId" resultType="int">
        SELECT COUNT(*) FROM mini_special_user_car
        WHERE special_user_id = #{specialUserId} AND delete_flag = 0
    </select>

    <select id="countSpecialUserCarsByPhoneNumber" resultType="int">
        SELECT COUNT(*) FROM mini_special_user_car
        WHERE RIGHT(phone_number, 10) = RIGHT(#{phoneNumber}, 10) AND delete_flag = 0
    </select>

</mapper>
