<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.WxUserMapper">
    
    <resultMap type="WxUser" id="WxUserResult">
        <result property="id"           column="id"           />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="phoneNumber"  column="phone_number" />
        <result property="password"     column="password"     />
        <result property="openId"       column="open_id"      />
        <result property="img"          column="img"          />
        <result property="status"       column="status"       />
        <result property="userType"     column="user_type"    />
        <result property="otherId"      column="other_id"     />
        <result property="weChatBalance"    column="we_chat_balance"    />
        <result property="platformBalance"  column="platform_balance"  />
        <result property="donateBalance"    column="donate_balance"     />
        <result property="deleteFlag"   column="delete_flag"  />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
    </resultMap>

    <resultMap type="WxUserCar" id="WxUserCarResult">
        <result property="id"           column="id"           />
        <result property="plateNo"      column="plate_no"     />
        <result property="carBrand"     column="car_brand"    />
        <result property="carType"      column="car_type"     />
        <result property="isDefault"    column="is_default"   />
        <result property="userId"       column="user_id"      />
        <result property="energyType"   column="energy_type"  />
        <result property="deleteFlag"   column="delete_flag"  />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
    </resultMap>

    <sql id="selectWxUserVo">
        select id, user_name, nick_name, phone_number, password, open_id, img, status, user_type,
               other_id, we_chat_balance, platform_balance, donate_balance, delete_flag,
               create_by, create_time, update_by, update_time
        from wx_user
    </sql>

    <select id="selectWxUserList" parameterType="WxUser" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        <where>
            delete_flag = 0
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="userType != null "> and user_type = #{userType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWxUserById" parameterType="Long" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where id = #{id} and delete_flag = 0
    </select>
        
    <insert id="insertWxUser" parameterType="WxUser" useGeneratedKeys="true" keyProperty="id">
        insert into wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="img != null and img != ''">img,</if>
            <if test="status != null">status,</if>

            <if test="otherId != null and otherId != ''">other_id,</if>
            <if test="weChatBalance != null">we_chat_balance,</if>
            <if test="platformBalance != null">platform_balance,</if>
            <if test="donateBalance != null">donate_balance,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            delete_flag
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="img != null and img != ''">#{img},</if>
            <if test="status != null">#{status},</if>

            <if test="otherId != null and otherId != ''">#{otherId},</if>
            <if test="weChatBalance != null">#{weChatBalance},</if>
            <if test="platformBalance != null">#{platformBalance},</if>
            <if test="donateBalance != null">#{donateBalance},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            0
         </trim>
    </insert>

    <update id="updateWxUser" parameterType="WxUser">
        update wx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="img != null and img != ''">img = #{img},</if>
            <if test="status != null">status = #{status},</if>

            <if test="otherId != null and otherId != ''">other_id = #{otherId},</if>
            <if test="weChatBalance != null">we_chat_balance = #{weChatBalance},</if>
            <if test="platformBalance != null">platform_balance = #{platformBalance},</if>
            <if test="donateBalance != null">donate_balance = #{donateBalance},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxUserById" parameterType="Long">
        update wx_user set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteWxUserByIds" parameterType="String">
        update wx_user set delete_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="batchUpdateStatus">
        update wx_user set status = #{status}, update_by = #{updateBy}, update_time = #{updateTime}
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="checkUserNameUnique" parameterType="String" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where user_name = #{userName} and delete_flag = 0 limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where phone_number = #{phoneNumber} and delete_flag = 0 limit 1
    </select>

    <select id="selectWxUserCarsByUserId" parameterType="Long" resultMap="WxUserCarResult">
        select id, plate_no, car_brand, car_type, is_default, user_id, energy_type, delete_flag,
               create_by, create_time, update_by, update_time
        from wx_user_car
        where user_id = #{userId} and delete_flag = 0
        order by is_default desc, create_time desc
    </select>

    <!-- 根据手机号更新用户类型 -->
    <update id="updateUserTypeByPhoneNumber">
        update wx_user
        set user_type = #{userType}, update_time = now()
        where phone_number = #{phoneNumber} and delete_flag = 0
    </update>

    <!-- 根据旧手机号更新为新手机号 -->
    <update id="updatePhoneNumberByOldPhone">
        update wx_user
        set phone_number = #{newPhoneNumber}, update_time = now()
        where phone_number = #{oldPhoneNumber} and delete_flag = 0
    </update>

    <!-- 根据手机号查询用户信息 -->
    <select id="selectUserByPhoneNumber" parameterType="String" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where phone_number = #{phoneNumber} and delete_flag = 0
    </select>

</mapper>
