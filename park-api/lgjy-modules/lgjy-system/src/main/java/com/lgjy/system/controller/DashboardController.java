package com.lgjy.system.controller;

import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.system.service.IDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理后台首页数据统计Controller
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController extends BaseController {

    @Autowired
    private IDashboardService dashboardService;

    /**
     * 获取首页统计数据
     *
     * @param period      时间周期：week-近7天, month-近一月, halfYear-近半年, year-近一年
     * @param warehouseId 场库ID，可选，为空则统计全部
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics(
            @RequestParam(value = "period", defaultValue = "week") String period,
            @RequestParam(value = "warehouseId", required = false) Long warehouseId) {

        Map<String, Object> statistics = dashboardService.getDashboardStatistics(period, warehouseId);
        return success(statistics);
    }

    /**
     * 获取全系统统计数据（不受场库选择影响）
     *
     * @param period 时间周期：week-近7天, month-近一月, halfYear-近半年, year-近一年
     * @return 全系统统计数据
     */
    @GetMapping("/system-statistics")
    public AjaxResult getSystemStatistics(
            @RequestParam(value = "period", defaultValue = "week") String period) {

        Map<String, Object> systemStats = dashboardService.getSystemStatistics(period);
        return success(systemStats);
    }

    /**
     * 获取收入趋势数据
     *
     * @param period      时间周期
     * @param warehouseId 场库ID，可选
     * @return 趋势数据
     */
    @GetMapping("/revenue-trend")
    public AjaxResult getRevenueTrend(
            @RequestParam(value = "period", defaultValue = "week") String period,
            @RequestParam(value = "warehouseId", required = false) Long warehouseId) {

        Map<String, Object> trendData = dashboardService.getRevenueTrend(period, warehouseId);
        return success(trendData);
    }

    /**
     * 获取用户增长趋势数据
     *
     * @param period      时间周期
     * @param warehouseId 场库ID，可选
     * @return 用户趋势数据
     */
    @GetMapping("/user-trend")
    public AjaxResult getUserTrend(
            @RequestParam(value = "period", defaultValue = "week") String period,
            @RequestParam(value = "warehouseId", required = false) Long warehouseId) {

        Map<String, Object> userTrendData = dashboardService.getUserTrend(period, warehouseId);
        return success(userTrendData);
    }

    /**
     * 获取场库列表（用于下拉选择）
     *
     * @return 场库列表
     */
    @GetMapping("/warehouses")
    public AjaxResult getWarehouses() {
        return success(dashboardService.getWarehouseList());
    }

    /**
     * 获取指定场库的详细统计
     *
     * @param warehouseId 场库ID
     * @return 场库统计数据
     */
    @GetMapping("/warehouse/{warehouseId}")
    public AjaxResult getWarehouseStats(@PathVariable Long warehouseId) {
        Map<String, Object> warehouseStats = dashboardService.getWarehouseStatistics(warehouseId);
        return success(warehouseStats);
    }

    /**
     * 刷新统计数据缓存
     *
     * @return 操作结果
     */
    @PostMapping("/refresh-cache")
    public AjaxResult refreshCache() {
        dashboardService.refreshStatisticsCache();
        return success("缓存刷新成功");
    }
}
