package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.mapper.MiniWarehouseManagerMapper;
import com.lgjy.system.domain.MiniWarehouseManager;
import com.lgjy.system.service.IMiniWarehouseManagerService;
import com.lgjy.common.core.exception.ServiceException;

/**
 * 场库管理人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniWarehouseManagerServiceImpl implements IMiniWarehouseManagerService {
    @Autowired
    private MiniWarehouseManagerMapper miniWarehouseManagerMapper;

    /**
     * 查询场库管理人员信息
     *
     * @param id 场库管理人员信息主键
     * @return 场库管理人员信息
     */
    @Override
    public MiniWarehouseManager selectMiniWarehouseManagerById(Long id) {
        return miniWarehouseManagerMapper.selectMiniWarehouseManagerById(id);
    }

    /**
     * 查询场库管理人员信息列表
     *
     * @param miniWarehouseManager 场库管理人员信息
     * @return 场库管理人员信息
     */
    @Override
    public List<MiniWarehouseManager> selectMiniWarehouseManagerList(MiniWarehouseManager miniWarehouseManager) {
        return miniWarehouseManagerMapper.selectMiniWarehouseManagerList(miniWarehouseManager);
    }

    /**
     * 新增场库管理人员信息
     *
     * @param miniWarehouseManager 场库管理人员信息
     * @return 结果
     */
    @Override
    public int insertMiniWarehouseManager(MiniWarehouseManager miniWarehouseManager) {
        miniWarehouseManager.setDeleteFlag(0);
        return miniWarehouseManagerMapper.insertMiniWarehouseManager(miniWarehouseManager);
    }

    /**
     * 修改场库管理人员信息
     *
     * @param miniWarehouseManager 场库管理人员信息
     * @return 结果
     */
    @Override
    public int updateMiniWarehouseManager(MiniWarehouseManager miniWarehouseManager) {
        return miniWarehouseManagerMapper.updateMiniWarehouseManager(miniWarehouseManager);
    }

    /**
     * 批量删除场库管理人员信息
     *
     * @param ids 需要删除的场库管理人员信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniWarehouseManagerByIds(Long[] ids) {
        return miniWarehouseManagerMapper.deleteMiniWarehouseManagerByIds(ids);
    }

    /**
     * 删除场库管理人员信息信息
     *
     * @param id 场库管理人员信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniWarehouseManagerById(Long id) {
        return miniWarehouseManagerMapper.deleteMiniWarehouseManagerById(id);
    }
}
