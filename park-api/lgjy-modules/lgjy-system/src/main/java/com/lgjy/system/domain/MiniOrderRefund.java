package com.lgjy.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;

/**
 * 订单退款记录对象 mini_order_refund
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public class MiniOrderRefund extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 订单号 */
    @Excel(name = "订单号")
    private String tradeId;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String refundReason;

    /** 退款类型（1-临停订单，2-VIP会员） */
    @Excel(name = "退款类型", readConverterExp = "1=临停订单,2=VIP会员")
    private Integer refundType;

    /** 原订单金额 */
    @Excel(name = "原订单金额")
    private BigDecimal originalAmount;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTradeId(String tradeId) 
    {
        this.tradeId = tradeId;
    }

    public String getTradeId() 
    {
        return tradeId;
    }
    public void setRefundReason(String refundReason) 
    {
        this.refundReason = refundReason;
    }

    public String getRefundReason() 
    {
        return refundReason;
    }
    public void setRefundType(Integer refundType) 
    {
        this.refundType = refundType;
    }

    public Integer getRefundType() 
    {
        return refundType;
    }
    public void setOriginalAmount(BigDecimal originalAmount) 
    {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getOriginalAmount() 
    {
        return originalAmount;
    }
    public void setRefundAmount(BigDecimal refundAmount) 
    {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getRefundAmount() 
    {
        return refundAmount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tradeId", getTradeId())
            .append("refundReason", getRefundReason())
            .append("refundType", getRefundType())
            .append("originalAmount", getOriginalAmount())
            .append("refundAmount", getRefundAmount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
