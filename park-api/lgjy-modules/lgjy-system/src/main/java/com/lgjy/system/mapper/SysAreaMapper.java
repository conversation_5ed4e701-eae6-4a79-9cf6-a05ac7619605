package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.SysArea;

/**
 * 行政区域Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface SysAreaMapper 
{
    /**
     * 查询行政区域
     * 
     * @param areaCode 行政区域主键
     * @return 行政区域
     */
    public SysArea selectSysAreaByAreaCode(String areaCode);

    /**
     * 查询行政区域列表
     * 
     * @param sysArea 行政区域
     * @return 行政区域集合
     */
    public List<SysArea> selectSysAreaList(SysArea sysArea);

    /**
     * 根据父级代码查询子区域列表
     * 
     * @param parentCode 父级代码
     * @return 子区域集合
     */
    public List<SysArea> selectSysAreaByParentCode(String parentCode);

    /**
     * 根据区域级别查询区域列表
     * 
     * @param areaLevel 区域级别
     * @return 区域集合
     */
    public List<SysArea> selectSysAreaByLevel(Integer areaLevel);

    /**
     * 新增行政区域
     * 
     * @param sysArea 行政区域
     * @return 结果
     */
    public int insertSysArea(SysArea sysArea);

    /**
     * 修改行政区域
     * 
     * @param sysArea 行政区域
     * @return 结果
     */
    public int updateSysArea(SysArea sysArea);

    /**
     * 删除行政区域
     * 
     * @param areaCode 行政区域主键
     * @return 结果
     */
    public int deleteSysAreaByAreaCode(String areaCode);

    /**
     * 批量删除行政区域
     * 
     * @param areaCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAreaByAreaCodes(String[] areaCodes);
}
