package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniBlacklist;
import com.lgjy.system.service.IMiniBlacklistService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 黑名单管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/owner/blacklist")
public class MiniBlacklistController extends BaseController {
    @Autowired
    private IMiniBlacklistService miniBlacklistService;

    /**
     * 查询黑名单管理列表
     */
    @RequiresPermissions("owner:blacklist:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniBlacklist miniBlacklist) {
        startPage();
        List<MiniBlacklist> list = miniBlacklistService.selectMiniBlacklistList(miniBlacklist);
        return getDataTable(list);
    }

    /**
     * 导出黑名单管理列表
     */
    @RequiresPermissions("owner:blacklist:export")
    @Log(title = "黑名单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniBlacklist miniBlacklist) {
        List<MiniBlacklist> list = miniBlacklistService.selectMiniBlacklistList(miniBlacklist);
        ExcelUtil<MiniBlacklist> util = new ExcelUtil<MiniBlacklist>(MiniBlacklist.class);
        util.exportExcel(response, list, "黑名单管理数据");
    }

    /**
     * 获取黑名单管理详细信息
     */
    @RequiresPermissions("owner:blacklist:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(miniBlacklistService.selectMiniBlacklistById(id));
    }

    /**
     * 新增黑名单管理
     */
    @RequiresPermissions("owner:blacklist:add")
    @Log(title = "黑名单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniBlacklist miniBlacklist) {
        return toAjax(miniBlacklistService.addBlacklistWithPush(miniBlacklist));
    }

    /**
     * 修改黑名单管理
     */
    @RequiresPermissions("owner:blacklist:edit")
    @Log(title = "黑名单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniBlacklist miniBlacklist) {
        return toAjax(miniBlacklistService.updateMiniBlacklist(miniBlacklist));
    }

    /**
     * 删除黑名单管理
     */
    @RequiresPermissions("owner:blacklist:remove")
    @Log(title = "黑名单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(miniBlacklistService.removeBlacklistWithPush(id));
    }


}
