package com.lgjy.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniVipTransaction;
import com.lgjy.system.service.IMiniVipTransactionService;

/**
 * 会员交易记录Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/vip/transaction")
public class MiniVipTransactionController extends BaseController {
    @Autowired
    private IMiniVipTransactionService miniVipTransactionService;

    /**
     * 查询会员交易记录列表
     */
    @RequiresPermissions("vip:transaction:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniVipTransaction miniVipTransaction) {
        startPage();
        List<MiniVipTransaction> list = miniVipTransactionService.selectMiniVipTransactionList(miniVipTransaction);
        return getDataTable(list);
    }

    /**
     * 导出会员交易记录列表
     */
    @RequiresPermissions("vip:transaction:export")
    @Log(title = "会员交易记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniVipTransaction miniVipTransaction) {
        List<MiniVipTransaction> list = miniVipTransactionService.selectMiniVipTransactionList(miniVipTransaction);
        ExcelUtil<MiniVipTransaction> util = new ExcelUtil<MiniVipTransaction>(MiniVipTransaction.class);
        util.exportExcel(response, list, "会员交易记录数据");
    }

    /**
     * 获取会员交易记录详细信息
     */
    @RequiresPermissions("vip:transaction:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(miniVipTransactionService.selectMiniVipTransactionById(id));
    }

    /**
     * 新增会员交易记录
     */
    @RequiresPermissions("vip:transaction:add")
    @Log(title = "会员交易记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniVipTransaction miniVipTransaction) {
        if (!miniVipTransactionService.checkTransactionNoUnique(miniVipTransaction)) {
            return error("新增交易记录失败，交易流水号已存在");
        }
        return toAjax(miniVipTransactionService.insertMiniVipTransaction(miniVipTransaction));
    }

    /**
     * 修改会员交易记录
     */
    @RequiresPermissions("vip:transaction:edit")
    @Log(title = "会员交易记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniVipTransaction miniVipTransaction) {
        return toAjax(miniVipTransactionService.updateMiniVipTransaction(miniVipTransaction));
    }

    /**
     * 删除会员交易记录
     */
    @RequiresPermissions("vip:transaction:remove")
    @Log(title = "会员交易记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniVipTransactionService.deleteMiniVipTransactionByIds(ids));
    }

    /**
     * 根据会员ID获取交易记录
     */
    @GetMapping("/member/{memberId}")
    public AjaxResult getTransactionsByMember(@PathVariable Long memberId) {
        List<MiniVipTransaction> transactions = miniVipTransactionService.selectTransactionsByMemberId(memberId);
        return success(transactions);
    }

    /**
     * 根据套餐ID获取交易记录
     */
    @GetMapping("/package/{packageId}")
    public AjaxResult getTransactionsByPackage(@PathVariable Long packageId) {
        List<MiniVipTransaction> transactions = miniVipTransactionService.selectTransactionsByPackageId(packageId);
        return success(transactions);
    }

    /**
     * 查询指定时间范围内的交易记录
     */
    @GetMapping("/timeRange")
    public AjaxResult getTransactionsByTimeRange(@RequestParam String startTime, @RequestParam String endTime) {
        List<MiniVipTransaction> transactions = miniVipTransactionService.selectTransactionsByTimeRange(startTime,
                endTime);
        return success(transactions);
    }

    /**
     * 处理退款
     */
    @RequiresPermissions("vip:transaction:refund")
    @Log(title = "会员交易记录", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public AjaxResult processRefund(@Validated @RequestBody MiniVipTransaction miniVipTransaction) {
        // 这里可以添加退款业务逻辑
        // 例如：更新支付状态为已退款，记录退款金额等
        miniVipTransaction.setPayStatus(4); // 设置为已退款状态
        return toAjax(miniVipTransactionService.updateMiniVipTransaction(miniVipTransaction));
    }

    /**
     * 统计交易金额按状态
     */
    @GetMapping("/stats/amountByStatus")
    public AjaxResult getAmountStatsByStatus(@RequestParam(required = false) Long warehouseId) {
        Map<String, Object> params = new java.util.HashMap<>();
        if (warehouseId != null) {
            params.put("warehouseId", warehouseId);
        }
        List<Map<String, Object>> stats = miniVipTransactionService.sumAmountByStatus(params);
        return success(stats);
    }

    /**
     * 统计交易数量按类型
     */
    @GetMapping("/stats/countByType")
    public AjaxResult getCountStatsByType(@RequestParam(required = false) Long warehouseId) {
        Map<String, Object> params = new java.util.HashMap<>();
        if (warehouseId != null) {
            params.put("warehouseId", warehouseId);
        }
        List<Map<String, Object>> stats = miniVipTransactionService.countTransactionsByType(params);
        return success(stats);
    }

    /**
     * 获取子场库选项
     */
    @GetMapping("/childWarehouseOptions")
    public AjaxResult getChildWarehouseOptions() {
        return success(miniVipTransactionService.selectChildWarehouseOptions());
    }

    /**
     * 根据父场库ID获取子场库选项
     */
    @GetMapping("/childWarehouseOptions/{parentId}")
    public AjaxResult getChildWarehouseOptionsByParentId(@PathVariable Long parentId) {
        return success(miniVipTransactionService.selectChildWarehouseOptionsByParentId(parentId));
    }
}
