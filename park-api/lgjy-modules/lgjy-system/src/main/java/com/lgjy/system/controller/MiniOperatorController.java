package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniOperator;
import com.lgjy.system.service.IMiniOperatorService;

/**
 * 运营商信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/platform/operator")
public class MiniOperatorController extends BaseController
{
    @Autowired
    private IMiniOperatorService miniOperatorService;

    /**
     * 查询运营商信息列表
     */
    @RequiresPermissions("platform:operator:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniOperator miniOperator)
    {
        startPage();
        List<MiniOperator> list = miniOperatorService.selectMiniOperatorList(miniOperator);
        return getDataTable(list);
    }

    /**
     * 导出运营商信息列表
     */
    @RequiresPermissions("platform:operator:export")
    @Log(title = "运营商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniOperator miniOperator)
    {
        List<MiniOperator> list = miniOperatorService.selectMiniOperatorList(miniOperator);
        ExcelUtil<MiniOperator> util = new ExcelUtil<MiniOperator>(MiniOperator.class);
        util.exportExcel(response, list, "运营商信息数据");
    }

    /**
     * 获取运营商信息详细信息
     */
    @RequiresPermissions("platform:operator:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(miniOperatorService.selectMiniOperatorById(id));
    }

    /**
     * 新增运营商信息
     */
    @RequiresPermissions("platform:operator:add")
    @Log(title = "运营商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniOperator miniOperator)
    {
        if (!miniOperatorService.checkCompanyNameUnique(miniOperator))
        {
            return error("新增运营商'" + miniOperator.getCompanyName() + "'失败，公司名称已存在");
        }
        return toAjax(miniOperatorService.insertMiniOperator(miniOperator));
    }

    /**
     * 修改运营商信息
     */
    @RequiresPermissions("platform:operator:edit")
    @Log(title = "运营商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniOperator miniOperator)
    {
        if (!miniOperatorService.checkCompanyNameUnique(miniOperator))
        {
            return error("修改运营商'" + miniOperator.getCompanyName() + "'失败，公司名称已存在");
        }
        return toAjax(miniOperatorService.updateMiniOperator(miniOperator));
    }

    /**
     * 删除运营商信息
     */
    @RequiresPermissions("platform:operator:remove")
    @Log(title = "运营商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(miniOperatorService.deleteMiniOperatorByIds(ids));
    }

    /**
     * 获取运营商下拉列表
     */
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect()
    {
        List<MiniOperator> operators = miniOperatorService.selectMiniOperatorAll();
        return success(operators);
    }
}
