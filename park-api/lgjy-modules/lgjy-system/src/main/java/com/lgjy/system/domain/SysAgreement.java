package com.lgjy.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 系统协议对象 sys_agreement
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
public class SysAgreement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 协议ID */
    private Long id;

    /** 协议类型（0=用户服务条款 1=隐私政策 2=发票抬头协议） */
    @Excel(name = "协议类型", readConverterExp = "0=用户服务条款,1=隐私政策,2=发票抬头协议")
    private Integer agreementType;

    /** 协议标题 */
    @Excel(name = "协议标题")
    private String agreementTitle;

    /** 协议内容 */
    @Excel(name = "协议内容")
    private String agreementContent;

    /** 删除标志（0代表存在 1代表删除） */
    private Integer deleteFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAgreementType(Integer agreementType) 
    {
        this.agreementType = agreementType;
    }

    public Integer getAgreementType() 
    {
        return agreementType;
    }
    public void setAgreementTitle(String agreementTitle) 
    {
        this.agreementTitle = agreementTitle;
    }

    public String getAgreementTitle() 
    {
        return agreementTitle;
    }
    public void setAgreementContent(String agreementContent) 
    {
        this.agreementContent = agreementContent;
    }

    public String getAgreementContent() 
    {
        return agreementContent;
    }
    public void setDeleteFlag(Integer deleteFlag) 
    {
        this.deleteFlag = deleteFlag;
    }

    public Integer getDeleteFlag() 
    {
        return deleteFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agreementType", getAgreementType())
            .append("agreementTitle", getAgreementTitle())
            .append("agreementContent", getAgreementContent())
            .append("deleteFlag", getDeleteFlag())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
