package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniVipMember;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.service.IMiniVipMemberService;
import com.lgjy.system.service.IMiniWarehouseService;

/**
 * 会员信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/vip/member")
public class MiniVipMemberController extends BaseController {
    @Autowired
    private IMiniVipMemberService miniVipMemberService;

    @Autowired
    private IMiniWarehouseService miniWarehouseService;

    /**
     * 查询会员信息列表
     */
    @RequiresPermissions("vip:member:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniVipMember miniVipMember) {
        startPage();
        List<MiniVipMember> list = miniVipMemberService.selectMiniVipMemberList(miniVipMember);
        return getDataTable(list);
    }

    /**
     * 导出会员信息列表
     */
    @RequiresPermissions("vip:member:export")
    @Log(title = "会员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniVipMember miniVipMember) {
        List<MiniVipMember> list = miniVipMemberService.selectMiniVipMemberList(miniVipMember);
        ExcelUtil<MiniVipMember> util = new ExcelUtil<MiniVipMember>(MiniVipMember.class);
        util.exportExcel(response, list, "会员信息数据");
    }

    /**
     * 获取会员信息详细信息
     */
    @RequiresPermissions("vip:member:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(miniVipMemberService.selectMiniVipMemberById(id));
    }

    /**
     * 新增会员信息
     */
    @RequiresPermissions("vip:member:add")
    @Log(title = "会员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniVipMember miniVipMember) {
        return toAjax(miniVipMemberService.insertMiniVipMember(miniVipMember));
    }

    /**
     * 修改会员信息
     */
    @RequiresPermissions("vip:member:edit")
    @Log(title = "会员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniVipMember miniVipMember) {
        return toAjax(miniVipMemberService.updateMiniVipMember(miniVipMember));
    }

    /**
     * 删除会员信息
     */
    @RequiresPermissions("vip:member:remove")
    @Log(title = "会员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniVipMemberService.deleteMiniVipMemberByIds(ids));
    }

    /**
     * 根据手机号查询会员信息
     */
    @GetMapping("/phone/{phoneNumber}")
    public AjaxResult getMemberByPhone(@PathVariable String phoneNumber) {
        MiniVipMember member = miniVipMemberService.selectMemberByPhoneNumber(phoneNumber);
        return success(member);
    }

    /**
     * 根据场库ID获取会员列表
     */
    @GetMapping("/warehouse/{warehouseId}")
    public AjaxResult getMembersByWarehouse(@PathVariable Long warehouseId) {
        List<MiniVipMember> members = miniVipMemberService.selectMembersByWarehouseId(warehouseId);
        return success(members);
    }

    /**
     * 获取即将到期的会员列表
     */
    @GetMapping("/expiring")
    public AjaxResult getExpiringMembers(@RequestParam(defaultValue = "7") Integer days) {
        List<MiniVipMember> members = miniVipMemberService.selectExpiringMembers(days);
        return success(members);
    }

    /**
     * 获取子场库选项
     */
    @GetMapping("/childWarehouseOptions")
    public AjaxResult getChildWarehouseOptions() {
        return success(miniVipMemberService.selectChildWarehouseOptions());
    }

    /**
     * 根据父场库ID获取子场库选项
     */
    @GetMapping("/childWarehouseOptions/{parentId}")
    public AjaxResult getChildWarehouseOptionsByParentId(@PathVariable Long parentId) {
        return success(miniVipMemberService.selectChildWarehouseOptionsByParentId(parentId));
    }

    /**
     * 根据停车场ID获取套餐选项
     */
    @GetMapping("/packageOptions/{parkingLotId}")
    public AjaxResult getPackageOptionsByParkingLotId(@PathVariable Long parkingLotId) {
        return success(miniVipMemberService.getPackageOptionsByParkingLotId(parkingLotId));
    }

    /**
     * 获取场库选项（包含层级关系）
     */
    @GetMapping("/warehouseOptions")
    public AjaxResult getWarehouseOptions() {
        // 修复Bug：查询所有场库（包含主场库和子场库），与会员套餐配置页面保持一致
        List<MiniWarehouse> warehouses = miniVipMemberService.selectAllWarehouseOptions();
        return success(warehouses);
    }
}
