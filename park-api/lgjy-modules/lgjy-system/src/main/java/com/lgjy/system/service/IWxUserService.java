package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.system.domain.WxUserCar;

/**
 * 小程序用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IWxUserService 
{
    /**
     * 查询小程序用户
     * 
     * @param id 小程序用户主键
     * @return 小程序用户
     */
    public WxUser selectWxUserById(Long id);

    /**
     * 查询小程序用户列表
     * 
     * @param wxUser 小程序用户
     * @return 小程序用户集合
     */
    public List<WxUser> selectWxUserList(WxUser wxUser);

    /**
     * 新增小程序用户
     * 
     * @param wxUser 小程序用户
     * @return 结果
     */
    public int insertWxUser(WxUser wxUser);

    /**
     * 修改小程序用户
     * 
     * @param wxUser 小程序用户
     * @return 结果
     */
    public int updateWxUser(WxUser wxUser);

    /**
     * 批量删除小程序用户
     * 
     * @param ids 需要删除的小程序用户主键集合
     * @return 结果
     */
    public int deleteWxUserByIds(Long[] ids);

    /**
     * 删除小程序用户信息
     * 
     * @param id 小程序用户主键
     * @return 结果
     */
    public int deleteWxUserById(Long id);

    /**
     * 修改用户状态
     * 
     * @param wxUser 用户信息
     * @return 结果
     */
    public int updateWxUserStatus(WxUser wxUser);

    /**
     * 批量修改用户状态
     * 
     * @param ids 用户ID数组
     * @param status 状态值
     * @return 结果
     */
    public int batchUpdateStatus(Long[] ids, Integer status);

    /**
     * 校验用户账号是否唯一
     * 
     * @param wxUser 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(WxUser wxUser);

    /**
     * 校验手机号码是否唯一
     * 
     * @param wxUser 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(WxUser wxUser);

    /**
     * 查询用户车辆信息
     * 
     * @param userId 用户ID
     * @return 车辆信息集合
     */
    public List<WxUserCar> selectWxUserCarsByUserId(Long userId);
}
