package com.lgjy.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 错误数据日志对象 error_data_log
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ErrorDataLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Excel(name = "记录ID")
    private String id;

    /** 错误码（0未识别车牌，1入场时无出场记录，2出场时无入场记录，3收费异常） */
    @Excel(name = "错误类型", readConverterExp = "0=未识别车牌,1=入场时无出场记录,2=出场时无入场记录,3=收费异常")
    @NotNull(message = "错误码不能为空")
    private Integer errCode;

    /** 错误类型名称 */
    @Excel(name = "错误类型")
    private String errCodeName;

    /** 场库ID */
    @Excel(name = "场库ID")
    @NotBlank(message = "场库ID不能为空")
    private String parkingId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String parkingName;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String plateNum;

    /** 入场时间 */
    @Excel(name = "入场时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String inTime;

    /** 入场通道名称 */
    @Excel(name = "入场通道")
    private String inChannelName;

    /** 出场时间 */
    @Excel(name = "出场时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String outTime;

    /** 出场通道名称 */
    @Excel(name = "出场通道")
    private String outChannelName;

    /** 金额 */
    @Excel(name = "金额")
    private String money;

    /** 图片路径（入场，出场） */
    @Excel(name = "图片路径")
    private String imgPath;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 最后更新时间 */
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdate;

    @Override
    public String toString() {
        return "ErrorDataLog{" +
                "id='" + id + '\'' +
                ", errCode=" + errCode +
                ", errCodeName='" + errCodeName + '\'' +
                ", parkingId='" + parkingId + '\'' +
                ", parkingName='" + parkingName + '\'' +
                ", plateNum='" + plateNum + '\'' +
                ", inTime='" + inTime + '\'' +
                ", inChannelName='" + inChannelName + '\'' +
                ", outTime='" + outTime + '\'' +
                ", outChannelName='" + outChannelName + '\'' +
                ", money='" + money + '\'' +
                ", imgPath='" + imgPath + '\'' +
                ", remark='" + remark + '\'' +
                ", lastUpdate=" + lastUpdate +
                '}';
    }
}
