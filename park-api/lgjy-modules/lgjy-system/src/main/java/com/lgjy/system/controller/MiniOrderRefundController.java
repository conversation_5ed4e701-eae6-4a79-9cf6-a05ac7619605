package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniOrderRefund;
import com.lgjy.system.service.IMiniOrderRefundService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 退款管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/refund")
public class MiniOrderRefundController extends BaseController
{
    @Autowired
    private IMiniOrderRefundService miniOrderRefundService;

    /**
     * 查询退款记录列表
     */
    @RequiresPermissions("refund:refund:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniOrderRefund miniOrderRefund)
    {
        startPage();
        List<MiniOrderRefund> list = miniOrderRefundService.selectMiniOrderRefundList(miniOrderRefund);
        return getDataTable(list);
    }

    /**
     * 导出退款记录列表
     */
    @RequiresPermissions("refund:refund:export")
    @Log(title = "退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniOrderRefund miniOrderRefund)
    {
        List<MiniOrderRefund> list = miniOrderRefundService.selectMiniOrderRefundList(miniOrderRefund);
        ExcelUtil<MiniOrderRefund> util = new ExcelUtil<MiniOrderRefund>(MiniOrderRefund.class);
        util.exportExcel(response, list, "退款记录数据");
    }

    /**
     * 获取退款记录详细信息
     */
    @RequiresPermissions("refund:refund:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(miniOrderRefundService.selectMiniOrderRefundById(id));
    }

    /**
     * 根据订单号查询退款记录
     */
    @RequiresPermissions("refund:refund:query")
    @GetMapping("/tradeId/{tradeId}")
    public AjaxResult getByTradeId(@PathVariable("tradeId") String tradeId)
    {
        List<MiniOrderRefund> list = miniOrderRefundService.selectMiniOrderRefundByTradeId(tradeId);
        return AjaxResult.success(list);
    }
}
