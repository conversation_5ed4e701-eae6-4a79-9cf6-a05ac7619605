package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * 会员信息对象 mini_vip_package_user_detail
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MiniVipMember extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 运营商ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    @Excel(name = "运营商名称")
    private String operatorName;

    /** 关联用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 手机号码 */
    @Excel(name = "手机号码")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "请输入正确的手机号码")
    private String phoneNumber;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    /** VIP开始时间 */
    @Excel(name = "VIP开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "VIP开始时间不能为空")
    private Date beginVipTime;

    /** VIP结束时间 */
    @Excel(name = "VIP结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "VIP结束时间不能为空")
    private Date endVipTime;

    /** 套餐名称（用于显示，不存储在数据库） */
    @Excel(name = "套餐名称")
    private String packageName;

    /** 大连系统ID */
    @Excel(name = "大连系统ID")
    private Long dlySystemId;

    /** 停车位号 */
    @Excel(name = "停车位号")
    private String parkingSpaceNo;

    /** 会员类型 */
    @Excel(name = "会员类型", readConverterExp = "0=普通会员,1=集团客户,2=VIP客户,3=团购会员")
    private Integer vipType;

    /** 删除标识 */
    private Integer deleteFlag;

    /**
     * 自定义warehouseId的JSON反序列化处理
     * 兼容处理前端级联选择器传递的数组格式和单值格式
     */
    @JsonSetter("warehouseId")
    public void setWarehouseIdFromJson(Object warehouseId) {
        if (warehouseId instanceof List) {
            // 处理数组格式：取最后一个元素（用户实际选中的场库ID）
            List<?> list = (List<?>) warehouseId;
            if (!list.isEmpty()) {
                this.warehouseId = Long.valueOf(list.get(list.size() - 1).toString());
            }
        } else if (warehouseId != null) {
            // 处理单值格式
            this.warehouseId = Long.valueOf(warehouseId.toString());
        }
    }

    /**
     * 标准的warehouseId setter方法，忽略JSON反序列化
     */
    @JsonIgnore
    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("warehouseId", getWarehouseId())
                .append("userId", getUserId())
                .append("phoneNumber", getPhoneNumber())
                .append("plateNo", getPlateNo())
                .append("beginVipTime", getBeginVipTime())
                .append("endVipTime", getEndVipTime())
                .append("dlySystemId", getDlySystemId())
                .append("parkingSpaceNo", getParkingSpaceNo())
                .append("vipType", getVipType())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
