package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.system.mapper.MiniUnionPayConfigMapper;
import com.lgjy.system.domain.MiniUnionPayConfig;
import com.lgjy.system.service.IMiniUnionPayConfigService;
import com.lgjy.common.core.exception.ServiceException;

/**
 * 银联配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Service
public class MiniUnionPayConfigServiceImpl implements IMiniUnionPayConfigService {
    @Autowired
    private MiniUnionPayConfigMapper miniUnionPayConfigMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询银联配置
     *
     * @param id 银联配置主键
     * @return 银联配置
     */
    @Override
    public MiniUnionPayConfig selectMiniUnionPayConfigById(Long id) {
        return miniUnionPayConfigMapper.selectMiniUnionPayConfigById(id);
    }

    /**
     * 查询银联配置列表
     *
     * @param miniUnionPayConfig 银联配置
     * @return 银联配置
     */
    @Override
    public List<MiniUnionPayConfig> selectMiniUnionPayConfigList(MiniUnionPayConfig miniUnionPayConfig) {
        return miniUnionPayConfigMapper.selectMiniUnionPayConfigList(miniUnionPayConfig);
    }

    /**
     * 新增银联配置
     *
     * @param miniUnionPayConfig 银联配置
     * @return 结果
     */
    @Override
    public int insertMiniUnionPayConfig(MiniUnionPayConfig miniUnionPayConfig) {
        // 校验商户号唯一性
        if (!checkMidUnique(miniUnionPayConfig.getMid(), null)) {
            throw new ServiceException("商户号已存在");
        }

        // 校验终端号唯一性
        if (!checkTidUnique(miniUnionPayConfig.getTid(), null)) {
            throw new ServiceException("终端号已存在");
        }

        // 生成ID
        miniUnionPayConfig.setId(snowflakeIdGenerator.nextId());
        miniUnionPayConfig.setDeleteFlag(0);

        return miniUnionPayConfigMapper.insertMiniUnionPayConfig(miniUnionPayConfig);
    }

    /**
     * 修改银联配置
     *
     * @param miniUnionPayConfig 银联配置
     * @return 结果
     */
    @Override
    public int updateMiniUnionPayConfig(MiniUnionPayConfig miniUnionPayConfig) {
        // 校验商户号唯一性
        if (!checkMidUnique(miniUnionPayConfig.getMid(), miniUnionPayConfig.getId())) {
            throw new ServiceException("商户号已存在");
        }

        // 校验终端号唯一性
        if (!checkTidUnique(miniUnionPayConfig.getTid(), miniUnionPayConfig.getId())) {
            throw new ServiceException("终端号已存在");
        }

        return miniUnionPayConfigMapper.updateMiniUnionPayConfig(miniUnionPayConfig);
    }

    /**
     * 批量删除银联配置
     *
     * @param ids 需要删除的银联配置主键
     * @return 结果
     */
    @Override
    public int deleteMiniUnionPayConfigByIds(Long[] ids) {
        return miniUnionPayConfigMapper.deleteMiniUnionPayConfigByIds(ids);
    }

    /**
     * 删除银联配置信息
     *
     * @param id 银联配置主键
     * @return 结果
     */
    @Override
    public int deleteMiniUnionPayConfigById(Long id) {
        return miniUnionPayConfigMapper.deleteMiniUnionPayConfigById(id);
    }

    /**
     * 检查商户号是否唯一
     *
     * @param mid 商户号
     * @param id  银联配置ID
     * @return 结果
     */
    @Override
    public boolean checkMidUnique(String mid, Long id) {
        if (StringUtils.isEmpty(mid)) {
            return false;
        }
        int count = miniUnionPayConfigMapper.checkMidUnique(mid, id);
        return count == 0;
    }

    /**
     * 检查终端号是否唯一
     *
     * @param tid 终端号
     * @param id  银联配置ID
     * @return 结果
     */
    @Override
    public boolean checkTidUnique(String tid, Long id) {
        if (StringUtils.isEmpty(tid)) {
            return false;
        }
        int count = miniUnionPayConfigMapper.checkTidUnique(tid, id);
        return count == 0;
    }

    /**
     * 根据场库ID查询银联配置
     *
     * @param warehouseId 场库ID
     * @return 银联配置
     */
    @Override
    public MiniUnionPayConfig selectMiniUnionPayConfigByWarehouseId(Long warehouseId) {
        return miniUnionPayConfigMapper.selectMiniUnionPayConfigByWarehouseId(warehouseId);
    }

    /**
     * 导出银联配置列表
     *
     * @param miniUnionPayConfig 银联配置
     * @return 银联配置集合
     */
    @Override
    public List<MiniUnionPayConfig> exportMiniUnionPayConfig(MiniUnionPayConfig miniUnionPayConfig) {
        return miniUnionPayConfigMapper.selectMiniUnionPayConfigList(miniUnionPayConfig);
    }
}
