package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniParkingOrder;
import com.lgjy.system.service.IMiniParkingOrderService;
import com.lgjy.system.service.IMiniWarehouseService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 停车订单Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/parkingOrder")
public class MiniParkingOrderController extends BaseController {
    @Autowired
    private IMiniParkingOrderService miniParkingOrderService;

    @Autowired
    private IMiniWarehouseService miniWarehouseService;

    /**
     * 查询停车订单列表
     */
    @RequiresPermissions("order:parkingOrder:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniParkingOrder miniParkingOrder) {
        startPage();
        List<MiniParkingOrder> list = miniParkingOrderService.selectMiniParkingOrderList(miniParkingOrder);
        return getDataTable(list);
    }

    /**
     * 导出停车订单列表
     */
    @RequiresPermissions("order:parkingOrder:export")
    @Log(title = "停车订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniParkingOrder miniParkingOrder) {
        List<MiniParkingOrder> list = miniParkingOrderService.selectMiniParkingOrderListForExport(miniParkingOrder);
        ExcelUtil<MiniParkingOrder> util = new ExcelUtil<MiniParkingOrder>(MiniParkingOrder.class);
        util.exportExcel(response, list, "停车订单数据");
    }

    /**
     * 获取停车订单详细信息
     */
    @RequiresPermissions("order:parkingOrder:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        AjaxResult ajax = AjaxResult.success();
        MiniParkingOrder order = miniParkingOrderService.selectMiniParkingOrderById(id);
        ajax.put("data", order);
        ajax.put("warehouses", miniWarehouseService.selectMiniWarehouseAll());

        // 获取出入场记录
        if (order != null && order.getPlateNo() != null && order.getWarehouseId() != null
                && order.getBeginParkingTime() != null) {
            try {
                // 使用与道闸系统相同的时间转换逻辑
                // 直接使用timestamp类型的begin_parking_time转换为Unix时间戳
                long beginParkingTimestamp = order.getBeginParkingTime().getTime() / 1000;
                String timestampStr = String.valueOf(beginParkingTimestamp);

                // 通过精确匹配获取出入场记录
                java.util.Map<String, Object> exactGateRecord = miniParkingOrderService
                        .selectGateParkingInfoByTimestamp(order.getWarehouseId(), order.getPlateNo(), timestampStr);

                if (exactGateRecord != null) {
                    // 如果找到精确匹配的记录，将其放入列表中
                    java.util.List<java.util.Map<String, Object>> gateRecords = new java.util.ArrayList<>();
                    gateRecords.add(exactGateRecord);
                    ajax.put("gateRecords", gateRecords);
                } else {
                    // 如果没有找到精确匹配的记录，返回空列表
                    ajax.put("gateRecords", new java.util.ArrayList<>());
                }
            } catch (Exception e) {
                // 如果时间转换出错，返回空列表
                ajax.put("gateRecords", new java.util.ArrayList<>());
            }
        } else {
            // 如果订单信息不完整，返回空列表
            ajax.put("gateRecords", new java.util.ArrayList<>());
        }

        return ajax;
    }

    /**
     * 新增停车订单
     */
    @RequiresPermissions("order:parkingOrder:add")
    @Log(title = "停车订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniParkingOrder miniParkingOrder) {
        return toAjax(miniParkingOrderService.insertMiniParkingOrder(miniParkingOrder));
    }

    /**
     * 修改停车订单
     */
    @RequiresPermissions("order:parkingOrder:edit")
    @Log(title = "停车订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniParkingOrder miniParkingOrder) {
        return toAjax(miniParkingOrderService.updateMiniParkingOrder(miniParkingOrder));
    }

    /**
     * 删除停车订单
     */
    @RequiresPermissions("order:parkingOrder:remove")
    @Log(title = "停车订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniParkingOrderService.deleteMiniParkingOrderByIds(ids));
    }

    /**
     * 根据车牌号查询停车订单
     */
    @RequiresPermissions("order:parkingOrder:query")
    @GetMapping("/plateNo/{plateNo}")
    public AjaxResult getByPlateNo(@PathVariable("plateNo") String plateNo) {
        List<MiniParkingOrder> list = miniParkingOrderService.selectMiniParkingOrderByPlateNo(plateNo);
        return AjaxResult.success(list);
    }

    /**
     * 根据场库ID查询停车订单
     */
    @RequiresPermissions("order:parkingOrder:query")
    @GetMapping("/warehouse/{warehouseId}")
    public AjaxResult getByWarehouseId(@PathVariable("warehouseId") Long warehouseId) {
        List<MiniParkingOrder> list = miniParkingOrderService.selectMiniParkingOrderByWarehouseId(warehouseId);
        return AjaxResult.success(list);
    }

    /**
     * 根据支付状态查询停车订单
     */
    @RequiresPermissions("order:parkingOrder:query")
    @GetMapping("/payStatus/{payStatus}")
    public AjaxResult getByPayStatus(@PathVariable("payStatus") Integer payStatus) {
        List<MiniParkingOrder> list = miniParkingOrderService.selectMiniParkingOrderByPayStatus(payStatus);
        return AjaxResult.success(list);
    }

    /**
     * 统计订单数量
     */
    @RequiresPermissions("order:parkingOrder:query")
    @GetMapping("/count")
    public AjaxResult count(MiniParkingOrder miniParkingOrder) {
        int count = miniParkingOrderService.countMiniParkingOrder(miniParkingOrder);
        return AjaxResult.success(count);
    }

    /**
     * 统计订单金额
     */
    @RequiresPermissions("order:parkingOrder:query")
    @GetMapping("/sum")
    public AjaxResult sum(MiniParkingOrder miniParkingOrder) {
        java.math.BigDecimal sum = miniParkingOrderService.sumMiniParkingOrderAmount(miniParkingOrder);
        return AjaxResult.success(sum);
    }

    /**
     * 获取车辆类型选项
     */
    @GetMapping("/carTypeOptions")
    public AjaxResult getCarTypeOptions() {
        List<String> carTypes = miniParkingOrderService.selectCarTypeOptions();
        return AjaxResult.success(carTypes);
    }

    /**
     * 停车订单退款
     */
    @RequiresPermissions("order:parkingOrder:refund")
    @Log(title = "停车订单", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public AjaxResult refundParkingOrder(@RequestBody MiniParkingOrder miniParkingOrder) {
        return toAjax(miniParkingOrderService.refundParkingOrder(miniParkingOrder));
    }
}
