package com.lgjy.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户车辆信息对象 wx_user_car
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxUserCar extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 车辆id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 品牌 */
    @Excel(name = "品牌")
    private String carBrand;

    /** 车型(微型，轿车，SUV，其他） */
    @Excel(name = "车型")
    private String carType;

    /** 是否是默认车辆（0否 1是） */
    @Excel(name = "是否默认", readConverterExp = "0=否,1=是")
    private Integer isDefault;

    /** 用户id */
    private Long userId;

    /** 能源类型(1燃油、2纯电、3混动) */
    @Excel(name = "能源类型", readConverterExp = "1=燃油,2=纯电,3=混动")
    private Integer energyType;

    /** 删除标志（0未删除 1已删除） */
    private Integer deleteFlag;
}
