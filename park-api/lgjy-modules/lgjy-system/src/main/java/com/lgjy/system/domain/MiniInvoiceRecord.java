package com.lgjy.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;

/**
 * 发票记录管理对象 mini_invoice_record
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public class MiniInvoiceRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 场库ID */
    @Excel(name = "场库ID")
    private Long warehouseId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 功能类型 */
    @Excel(name = "功能类型")
    private Integer functionType;

    /** 发票类型 */
    @Excel(name = "发票类型")
    private Integer invoiceType;

    /** 发票号码 */
    @Excel(name = "发票号码")
    private String invoiceNo;

    /** 发票代码 */
    @Excel(name = "发票代码")
    private String invoiceCode;

    /** 二维码ID */
    @Excel(name = "二维码ID")
    private String qrCodeId;

    /** 开票日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开票日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date issueDate;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalMoney;

    /** 税额 */
    @Excel(name = "税额")
    private BigDecimal totalTax;

    /** 发票抬头内容 */
    @Excel(name = "发票抬头内容")
    private String invoiceTitleContent;

    /** 纳税人识别号 */
    @Excel(name = "纳税人识别号")
    private String unitDutyParagraph;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registerAddress;

    /** 注册电话 */
    @Excel(name = "注册电话")
    private String registerPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String depositBank;

    /** 银行账号 */
    @Excel(name = "银行账号")
    private String bankAccount;

    /** 通知手机号 */
    @Excel(name = "通知手机号")
    private String notifyMobileNo;

    /** 通知邮箱 */
    @Excel(name = "通知邮箱")
    private String notifyEmail;

    /** PDF地址 */
    @Excel(name = "PDF地址")
    private String pdfUrl;

    /** PDF预览地址 */
    @Excel(name = "PDF预览地址")
    private String pdfPreviewUrl;

    /** 交易订单号 */
    @Excel(name = "交易订单号")
    private String tradeId;

    /** 订单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    /** 发票状态 */
    @Excel(name = "发票状态")
    private String status;

    /** 商户号 */
    @Excel(name = "商户号")
    private String mid;

    /** 终端号 */
    @Excel(name = "终端号")
    private String tid;

    /** 冲红日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "冲红日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reverseDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 重开标识 */
    @Excel(name = "重开标识")
    private Integer reopenSign;

    /** 原交易订单号 */
    @Excel(name = "原交易订单号")
    private String oldTradeId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setWarehouseId(Long warehouseId) 
    {
        this.warehouseId = warehouseId;
    }

    public Long getWarehouseId() 
    {
        return warehouseId;
    }
    public void setWarehouseName(String warehouseName) 
    {
        this.warehouseName = warehouseName;
    }

    public String getWarehouseName() 
    {
        return warehouseName;
    }
    public void setFunctionType(Integer functionType) 
    {
        this.functionType = functionType;
    }

    public Integer getFunctionType() 
    {
        return functionType;
    }
    public void setInvoiceType(Integer invoiceType) 
    {
        this.invoiceType = invoiceType;
    }

    public Integer getInvoiceType() 
    {
        return invoiceType;
    }
    public void setInvoiceNo(String invoiceNo) 
    {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceNo() 
    {
        return invoiceNo;
    }
    public void setInvoiceCode(String invoiceCode) 
    {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceCode() 
    {
        return invoiceCode;
    }
    public void setQrCodeId(String qrCodeId) 
    {
        this.qrCodeId = qrCodeId;
    }

    public String getQrCodeId() 
    {
        return qrCodeId;
    }
    public void setIssueDate(Date issueDate) 
    {
        this.issueDate = issueDate;
    }

    public Date getIssueDate() 
    {
        return issueDate;
    }
    public void setTotalMoney(BigDecimal totalMoney) 
    {
        this.totalMoney = totalMoney;
    }

    public BigDecimal getTotalMoney() 
    {
        return totalMoney;
    }
    public void setTotalTax(BigDecimal totalTax) 
    {
        this.totalTax = totalTax;
    }

    public BigDecimal getTotalTax() 
    {
        return totalTax;
    }
    public void setInvoiceTitleContent(String invoiceTitleContent) 
    {
        this.invoiceTitleContent = invoiceTitleContent;
    }

    public String getInvoiceTitleContent() 
    {
        return invoiceTitleContent;
    }
    public void setUnitDutyParagraph(String unitDutyParagraph) 
    {
        this.unitDutyParagraph = unitDutyParagraph;
    }

    public String getUnitDutyParagraph() 
    {
        return unitDutyParagraph;
    }
    public void setRegisterAddress(String registerAddress) 
    {
        this.registerAddress = registerAddress;
    }

    public String getRegisterAddress() 
    {
        return registerAddress;
    }
    public void setRegisterPhone(String registerPhone) 
    {
        this.registerPhone = registerPhone;
    }

    public String getRegisterPhone() 
    {
        return registerPhone;
    }
    public void setDepositBank(String depositBank) 
    {
        this.depositBank = depositBank;
    }

    public String getDepositBank() 
    {
        return depositBank;
    }
    public void setBankAccount(String bankAccount) 
    {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() 
    {
        return bankAccount;
    }
    public void setNotifyMobileNo(String notifyMobileNo) 
    {
        this.notifyMobileNo = notifyMobileNo;
    }

    public String getNotifyMobileNo() 
    {
        return notifyMobileNo;
    }
    public void setNotifyEmail(String notifyEmail) 
    {
        this.notifyEmail = notifyEmail;
    }

    public String getNotifyEmail() 
    {
        return notifyEmail;
    }
    public void setPdfUrl(String pdfUrl) 
    {
        this.pdfUrl = pdfUrl;
    }

    public String getPdfUrl() 
    {
        return pdfUrl;
    }
    public void setPdfPreviewUrl(String pdfPreviewUrl) 
    {
        this.pdfPreviewUrl = pdfPreviewUrl;
    }

    public String getPdfPreviewUrl() 
    {
        return pdfPreviewUrl;
    }
    public void setTradeId(String tradeId) 
    {
        this.tradeId = tradeId;
    }

    public String getTradeId() 
    {
        return tradeId;
    }
    public void setOrderDate(Date orderDate) 
    {
        this.orderDate = orderDate;
    }

    public Date getOrderDate() 
    {
        return orderDate;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setMid(String mid) 
    {
        this.mid = mid;
    }

    public String getMid() 
    {
        return mid;
    }
    public void setTid(String tid) 
    {
        this.tid = tid;
    }

    public String getTid() 
    {
        return tid;
    }
    public void setReverseDate(Date reverseDate) 
    {
        this.reverseDate = reverseDate;
    }

    public Date getReverseDate() 
    {
        return reverseDate;
    }
    public void setReopenSign(Integer reopenSign) 
    {
        this.reopenSign = reopenSign;
    }

    public Integer getReopenSign() 
    {
        return reopenSign;
    }
    public void setOldTradeId(String oldTradeId) 
    {
        this.oldTradeId = oldTradeId;
    }

    public String getOldTradeId() 
    {
        return oldTradeId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("warehouseId", getWarehouseId())
            .append("warehouseName", getWarehouseName())
            .append("functionType", getFunctionType())
            .append("invoiceType", getInvoiceType())
            .append("invoiceNo", getInvoiceNo())
            .append("invoiceCode", getInvoiceCode())
            .append("qrCodeId", getQrCodeId())
            .append("issueDate", getIssueDate())
            .append("totalMoney", getTotalMoney())
            .append("totalTax", getTotalTax())
            .append("invoiceTitleContent", getInvoiceTitleContent())
            .append("unitDutyParagraph", getUnitDutyParagraph())
            .append("registerAddress", getRegisterAddress())
            .append("registerPhone", getRegisterPhone())
            .append("depositBank", getDepositBank())
            .append("bankAccount", getBankAccount())
            .append("notifyMobileNo", getNotifyMobileNo())
            .append("notifyEmail", getNotifyEmail())
            .append("pdfUrl", getPdfUrl())
            .append("pdfPreviewUrl", getPdfPreviewUrl())
            .append("tradeId", getTradeId())
            .append("orderDate", getOrderDate())
            .append("status", getStatus())
            .append("mid", getMid())
            .append("tid", getTid())
            .append("reverseDate", getReverseDate())
            .append("remark", getRemark())
            .append("reopenSign", getReopenSign())
            .append("oldTradeId", getOldTradeId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
