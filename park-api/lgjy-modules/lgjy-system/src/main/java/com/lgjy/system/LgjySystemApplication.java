package com.lgjy.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import com.lgjy.common.security.annotation.EnableCustomConfig;
import com.lgjy.common.security.annotation.EnableRyFeignClients;

/**
 * 系统模块
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class LgjySystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjySystemApplication.class, args);
        System.out.println("系统模块启动成功");
    }
}
