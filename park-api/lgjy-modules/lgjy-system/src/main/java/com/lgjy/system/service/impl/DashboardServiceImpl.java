package com.lgjy.system.service.impl;

import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.redis.service.RedisService;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.mapper.DashboardMapper;
import com.lgjy.system.service.IDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 管理后台首页数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Service
public class DashboardServiceImpl implements IDashboardService {

    @Autowired
    private DashboardMapper dashboardMapper;

    @Autowired
    private RedisService redisService;

    private static final String CACHE_KEY_PREFIX = "dashboard:statistics:";
    private static final String CACHE_KEY_BATCH_PREFIX = "dashboard:batch:";
    private static final int CACHE_EXPIRE_MINUTES = 30; // 缓存30分钟
    private static final int CACHE_EXPIRE_MINUTES_BATCH = 15; // 批量查询缓存15分钟

    @Override
    public Map<String, Object> getDashboardStatistics(String period, Long warehouseId) {
        String cacheKey = CACHE_KEY_PREFIX + period + ":" + (warehouseId != null ? warehouseId : "all");

        // 尝试从缓存获取
        Map<String, Object> cachedData = redisService.getCacheObject(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }

        Map<String, Object> statistics = new HashMap<>();

        // 使用批量查询获取基础统计数据 - 性能优化
        Map<String, Object> batchStats = getBatchStatisticsFromCache(warehouseId);
        statistics.put("totalParkingLots", batchStats.get("totalWarehouses"));
        statistics.put("totalParkingSpaces", batchStats.get("totalParkingSpaces"));
        statistics.put("totalMembers", batchStats.get("totalMembers"));
        statistics.put("totalAppUsers", batchStats.get("totalAppUsers"));
        statistics.put("totalRevenue", getTotalRevenue(period, warehouseId));

        // 计算增长率（与上一个周期对比）
        String previousPeriod = getPreviousPeriod(period);
        statistics.put("parkingLotsGrowth", calculateGrowthRate(
                getTotalWarehouses(warehouseId),
                getTotalWarehousesPrevious(previousPeriod, warehouseId)));
        statistics.put("parkingSpacesGrowth", calculateGrowthRate(
                getTotalParkingSpaces(warehouseId),
                getTotalParkingSpacesPrevious(previousPeriod, warehouseId)));
        statistics.put("membersGrowth", calculateGrowthRate(
                getTotalMembers(warehouseId),
                getTotalMembersHistorical(previousPeriod, warehouseId)));
        statistics.put("appUsersGrowth", calculateGrowthRate(
                getTotalAppUsers(warehouseId),
                getTotalAppUsersHistorical(previousPeriod, warehouseId)));
        statistics.put("revenueGrowth", calculateGrowthRate(
                getTotalRevenue(period, warehouseId),
                getTotalRevenue(previousPeriod, warehouseId)));

        // 缓存结果
        redisService.setCacheObject(cacheKey, statistics, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return statistics;
    }

    @Override
    public Map<String, Object> getSystemStatistics(String period) {
        String cacheKey = CACHE_KEY_PREFIX + "system:" + period;

        // 尝试从缓存获取
        Map<String, Object> cachedData = redisService.getCacheObject(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }

        Map<String, Object> systemStats = new HashMap<>();

        // 获取全系统基础统计数据（不传warehouseId，获取全部数据）
        Map<String, Object> batchStats = getBatchStatisticsFromCache(null);
        systemStats.put("totalParkingLots", batchStats.get("totalWarehouses"));
        systemStats.put("totalParkingSpaces", batchStats.get("totalParkingSpaces"));
        systemStats.put("totalMembers", batchStats.get("totalMembers"));
        systemStats.put("totalAppUsers", batchStats.get("totalAppUsers"));
        systemStats.put("totalRevenue", getTotalRevenue(period, null));

        // 计算真实的增长率（与上一个周期对比）
        String previousPeriod = getPreviousPeriod(period);
        systemStats.put("parkingLotsGrowth", calculateGrowthRate(
                getTotalWarehouses(null),
                getTotalWarehousesPrevious(previousPeriod, null)));
        systemStats.put("parkingSpacesGrowth", calculateGrowthRate(
                getTotalParkingSpaces(null),
                getTotalParkingSpacesPrevious(previousPeriod, null)));
        systemStats.put("membersGrowth", calculateGrowthRate(
                getTotalMembers(null),
                getTotalMembersHistorical(previousPeriod, null)));
        systemStats.put("appUsersGrowth", calculateGrowthRate(
                getTotalAppUsers(null),
                getTotalAppUsersHistorical(previousPeriod, null)));
        systemStats.put("revenueGrowth", calculateGrowthRate(
                getTotalRevenue(period, null),
                getTotalRevenue(previousPeriod, null)));

        // 缓存结果
        redisService.setCacheObject(cacheKey, systemStats, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return systemStats;
    }

    @Override
    public Map<String, Object> getRevenueTrend(String period, Long warehouseId) {
        Map<String, Object> trendData = new HashMap<>();

        // 生成基于今天为基准的历史日期标签和数据
        List<String> labels = generateDateLabels(period);
        List<BigDecimal> data = generateRevenueData(period, warehouseId, labels);

        trendData.put("labels", labels);
        trendData.put("data", data);

        return trendData;
    }

    @Override
    public Map<String, Object> getUserTrend(String period, Long warehouseId) {
        Map<String, Object> userTrendData = new HashMap<>();

        // 生成基于今天为基准的历史日期标签和数据
        List<String> labels = generateDateLabels(period);
        List<Long> membersData = generateMemberData(period, warehouseId, labels);
        List<Long> appUsersData = generateAppUserData(period, warehouseId, labels);

        userTrendData.put("labels", labels);
        userTrendData.put("membersData", membersData);
        userTrendData.put("appUsersData", appUsersData);

        return userTrendData;
    }

    @Override
    public List<MiniWarehouse> getWarehouseList() {
        return dashboardMapper.getWarehouseList();
    }

    @Override
    public Map<String, Object> getWarehouseStatistics(Long warehouseId) {
        Map<String, Object> warehouseStats = new HashMap<>();

        // 只返回场库相关的数据：车位数量、会员数量、今日收入
        warehouseStats.put("members", getTotalMembers(warehouseId));
        warehouseStats.put("parkingSpaces", getTotalParkingSpaces(warehouseId));
        warehouseStats.put("todayRevenue", getTotalRevenue("today", warehouseId));

        return warehouseStats;
    }

    @Override
    public void refreshStatisticsCache() {
        // 清除所有dashboard相关缓存
        Collection<String> keys = redisService.keys(CACHE_KEY_PREFIX + "*");
        if (!keys.isEmpty()) {
            redisService.deleteObject(keys);
        }

        // 清除批量查询缓存
        Collection<String> batchKeys = redisService.keys(CACHE_KEY_BATCH_PREFIX + "*");
        if (!batchKeys.isEmpty()) {
            redisService.deleteObject(batchKeys);
        }
    }

    /**
     * 从缓存获取批量统计数据，缓存未命中时执行批量查询
     */
    private Map<String, Object> getBatchStatisticsFromCache(Long warehouseId) {
        String batchCacheKey = CACHE_KEY_BATCH_PREFIX + (warehouseId != null ? warehouseId : "all");

        // 尝试从缓存获取批量统计数据
        Map<String, Object> cachedBatchData = redisService.getCacheObject(batchCacheKey);
        if (cachedBatchData != null) {
            return cachedBatchData;
        }

        // 缓存未命中，执行批量查询
        List<Map<String, Object>> batchResults = dashboardMapper.getBatchStatistics(warehouseId);
        Map<String, Object> batchStats = new HashMap<>();

        // 转换批量查询结果为Map格式
        for (Map<String, Object> result : batchResults) {
            String statType = (String) result.get("stat_type");
            Object statValue = result.get("stat_value");

            switch (statType) {
                case "warehouses":
                    batchStats.put("totalWarehouses", statValue);
                    break;
                case "parking_spaces":
                    batchStats.put("totalParkingSpaces", statValue);
                    break;
                case "members":
                    batchStats.put("totalMembers", statValue);
                    break;
                case "app_users":
                    batchStats.put("totalAppUsers", statValue);
                    break;
            }
        }

        // 缓存批量统计结果，使用较短的缓存时间
        redisService.setCacheObject(batchCacheKey, batchStats, CACHE_EXPIRE_MINUTES_BATCH, TimeUnit.MINUTES);

        return batchStats;
    }

    @Override
    public Long getTotalWarehouses(Long warehouseId) {
        return dashboardMapper.getTotalWarehouses(warehouseId);
    }

    @Override
    public Long getTotalParkingSpaces(Long warehouseId) {
        return dashboardMapper.getTotalParkingSpaces(warehouseId);
    }

    @Override
    public Long getTotalMembers(Long warehouseId) {
        return dashboardMapper.getTotalMembers(warehouseId);
    }

    @Override
    public Long getTotalAppUsers(Long warehouseId) {
        return dashboardMapper.getTotalAppUsers(warehouseId);
    }

    @Override
    public BigDecimal getTotalRevenue(String period, Long warehouseId) {
        String startDate = getStartDate(period);
        String endDate = getEndDate();
        return dashboardMapper.getTotalRevenue(startDate, endDate, warehouseId);
    }

    @Override
    public Double calculateGrowthRate(Number currentValue, Number previousValue) {
        if (previousValue == null || previousValue.doubleValue() == 0) {
            return currentValue != null && currentValue.doubleValue() > 0 ? 100.0 : 0.0;
        }

        if (currentValue == null) {
            return -100.0;
        }

        double current = currentValue.doubleValue();
        double previous = previousValue.doubleValue();
        double growth = ((current - previous) / previous) * 100;

        return Math.round(growth * 10.0) / 10.0; // 保留一位小数
    }

    // 私有辅助方法
    private String getStartDate(String period) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startDate;

        switch (period) {
            case "week":
                startDate = now.minusDays(7);
                break;
            case "month":
                startDate = now.minusDays(30);
                break;
            case "halfYear":
                startDate = now.minusDays(180);
                break;
            case "year":
                startDate = now.minusDays(365);
                break;
            case "today":
                startDate = now.withHour(0).withMinute(0).withSecond(0);
                break;
            default:
                startDate = now.minusDays(7);
        }

        return startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private String getEndDate() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private String getPreviousPeriod(String period) {
        // 返回相同周期用于对比，实际的历史时间点在getHistoricalEndDate中计算
        return period;
    }

    /**
     * 获取历史数据的截止日期（用于计算增长率）
     */
    private String getHistoricalEndDate(String period) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime historicalDate;

        switch (period) {
            case "week":
                // 上周同期
                historicalDate = now.minusDays(7);
                break;
            case "month":
                // 上月同期
                historicalDate = now.minusDays(30);
                break;
            case "halfYear":
                // 半年前同期
                historicalDate = now.minusDays(180);
                break;
            case "year":
                // 一年前同期
                historicalDate = now.minusDays(365);
                break;
            default:
                // 默认一周前
                historicalDate = now.minusDays(7);
        }

        return historicalDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private String getPeriodGroupBy(String period) {
        switch (period) {
            case "week":
                return "DATE(create_time)";
            case "month":
                return "DATE(create_time)";
            case "halfYear":
                return "DATE_FORMAT(create_time, '%Y-%m')";
            case "year":
                return "DATE_FORMAT(create_time, '%Y-%m')";
            default:
                return "DATE(create_time)";
        }
    }

    // 获取历史数据的方法（用于计算增长率）
    private Long getTotalWarehousesPrevious(String period, Long warehouseId) {
        // 场库数量通常不会频繁变化，返回当前值
        return getTotalWarehouses(warehouseId);
    }

    private Long getTotalParkingSpacesPrevious(String period, Long warehouseId) {
        // 车位数量通常不会频繁变化，返回当前值
        return getTotalParkingSpaces(warehouseId);
    }

    /**
     * 获取历史会员数量（用于计算增长率）
     */
    private Long getTotalMembersHistorical(String period, Long warehouseId) {
        String endDate = getHistoricalEndDate(period);
        return dashboardMapper.getTotalMembersHistorical(endDate, warehouseId);
    }

    /**
     * 获取历史小程序用户数量（用于计算增长率）
     */
    private Long getTotalAppUsersHistorical(String period, Long warehouseId) {
        String endDate = getHistoricalEndDate(period);
        return dashboardMapper.getTotalAppUsersHistorical(endDate, warehouseId);
    }

    /**
     * 生成基于今天为基准的历史日期标签
     */
    private List<String> generateDateLabels(String period) {
        List<String> labels = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        switch (period) {
            case "week":
                // 前7天（包括今天），显示天数
                DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("d日");
                for (int i = 6; i >= 0; i--) {
                    LocalDateTime date = now.minusDays(i);
                    labels.add(date.format(dayFormatter));
                }
                break;
            case "month":
                // 前30天（包括今天），显示天数
                DateTimeFormatter monthDayFormatter = DateTimeFormatter.ofPattern("d日");
                for (int i = 29; i >= 0; i--) {
                    LocalDateTime date = now.minusDays(i);
                    labels.add(date.format(monthDayFormatter));
                }
                break;
            case "halfYear":
                // 前6个月，按月显示
                DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("M月");
                for (int i = 5; i >= 0; i--) {
                    LocalDateTime date = now.minusMonths(i);
                    labels.add(date.format(monthFormatter));
                }
                break;
            case "year":
                // 前12个月，按月显示
                DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("M月");
                for (int i = 11; i >= 0; i--) {
                    LocalDateTime date = now.minusMonths(i);
                    labels.add(date.format(yearFormatter));
                }
                break;
            default:
                // 默认前7天
                DateTimeFormatter defaultFormatter = DateTimeFormatter.ofPattern("d日");
                for (int i = 6; i >= 0; i--) {
                    LocalDateTime date = now.minusDays(i);
                    labels.add(date.format(defaultFormatter));
                }
        }

        return labels;
    }

    /**
     * 获取真实的收入趋势数据
     */
    private List<BigDecimal> generateRevenueData(String period, Long warehouseId, List<String> labels) {
        String startDate = calculateStartDate(period);
        String endDate = LocalDate.now().toString();

        List<Map<String, Object>> dbResults = dashboardMapper.getRevenueTrendByDate(startDate, endDate, warehouseId);

        // 将数据库结果映射到日期标签
        Map<String, BigDecimal> dataMap = dbResults.stream()
                .collect(Collectors.toMap(
                        item -> item.get("date").toString(),
                        item -> (BigDecimal) item.get("revenue")));

        // 按标签顺序填充数据，缺失日期补0
        return labels.stream()
                .map(label -> dataMap.getOrDefault(convertLabelToDate(label), BigDecimal.ZERO))
                .collect(Collectors.toList());
    }

    /**
     * 获取真实的会员增长数据
     */
    private List<Long> generateMemberData(String period, Long warehouseId, List<String> labels) {
        String startDate = calculateStartDate(period);
        String endDate = LocalDate.now().toString();

        List<Map<String, Object>> dbResults = dashboardMapper.getMemberGrowthByDate(startDate, endDate, warehouseId);

        // 将数据库结果映射到日期标签
        Map<String, Long> dataMap = dbResults.stream()
                .collect(Collectors.toMap(
                        item -> item.get("date").toString(),
                        item -> ((Number) item.get("count")).longValue()));

        // 按标签顺序填充数据，缺失日期补0，确保返回整数
        return labels.stream()
                .map(label -> dataMap.getOrDefault(convertLabelToDate(label), 0L))
                .collect(Collectors.toList());
    }

    /**
     * 获取真实的小程序用户增长数据
     */
    private List<Long> generateAppUserData(String period, Long warehouseId, List<String> labels) {
        String startDate = calculateStartDate(period);
        String endDate = LocalDate.now().toString();

        List<Map<String, Object>> dbResults = dashboardMapper.getAppUserGrowthByDate(startDate, endDate, warehouseId);

        // 将数据库结果映射到日期标签
        Map<String, Long> dataMap = dbResults.stream()
                .collect(Collectors.toMap(
                        item -> item.get("date").toString(),
                        item -> ((Number) item.get("count")).longValue()));

        // 按标签顺序填充数据，缺失日期补0，确保返回整数
        return labels.stream()
                .map(label -> dataMap.getOrDefault(convertLabelToDate(label), 0L))
                .collect(Collectors.toList());
    }

    /**
     * 将显示标签转换为数据库日期格式
     */
    private String convertLabelToDate(String label) {
        // MM-dd -> yyyy-MM-dd
        int currentYear = LocalDate.now().getYear();
        return currentYear + "-" + label;
    }

    /**
     * 计算开始日期
     */
    private String calculateStartDate(String period) {
        LocalDate now = LocalDate.now();
        switch (period) {
            case "week":
                return now.minusDays(6).toString();
            case "month":
                return now.minusDays(29).toString();
            case "halfYear":
                return now.minusMonths(5).withDayOfMonth(1).toString();
            case "year":
                return now.minusMonths(11).withDayOfMonth(1).toString();
            default:
                return now.minusDays(6).toString();
        }
    }
}
