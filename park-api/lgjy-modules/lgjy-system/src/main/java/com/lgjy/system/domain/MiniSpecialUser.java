package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 特殊会员对象 mini_special_user
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
public class MiniSpecialUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String nickName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "请输入正确的手机号码")
    private String phoneNumber;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 用户类型 */
    @Excel(name = "用户类型", readConverterExp = "VIP客户=VIP客户,集团客户=集团客户")
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    /** 删除标识 */
    private Integer deleteFlag;

    /** 创建者姓名 */
    @Excel(name = "创建者")
    private String createByName;

    /** 更新者姓名 */
    @Excel(name = "更新者")
    private String updateByName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("nickName", getNickName())
                .append("phoneNumber", getPhoneNumber())
                .append("plateNo", getPlateNo())
                .append("userType", getUserType())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
