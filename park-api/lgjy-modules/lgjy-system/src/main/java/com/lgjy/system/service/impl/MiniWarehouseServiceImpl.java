package com.lgjy.system.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.system.mapper.MiniWarehouseMapper;
import com.lgjy.system.mapper.MiniWarehouseManagerMapper;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.service.IMiniWarehouseService;
import com.lgjy.common.core.exception.ServiceException;

/**
 * 场库信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniWarehouseServiceImpl implements IMiniWarehouseService {
    @Autowired
    private MiniWarehouseMapper miniWarehouseMapper;

    @Autowired
    private MiniWarehouseManagerMapper miniWarehouseManagerMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询场库信息
     *
     * @param id 场库信息主键
     * @return 场库信息
     */
    @Override
    public MiniWarehouse selectMiniWarehouseById(Long id) {
        return miniWarehouseMapper.selectMiniWarehouseById(id);
    }

    /**
     * 查询场库信息列表
     *
     * @param miniWarehouse 场库信息
     * @return 场库信息
     */
    @Override
    public List<MiniWarehouse> selectMiniWarehouseList(MiniWarehouse miniWarehouse) {
        return miniWarehouseMapper.selectMiniWarehouseList(miniWarehouse);
    }

    /**
     * 新增场库信息
     *
     * @param miniWarehouse 场库信息
     * @return 结果
     */
    @Override
    public int insertMiniWarehouse(MiniWarehouse miniWarehouse) {
        // 使用雪花算法生成ID
        miniWarehouse.setId(snowflakeIdGenerator.nextId());

        // 自动设置用户ID为当前登录用户ID
        if (miniWarehouse.getUserId() == null) {
            miniWarehouse.setUserId(SecurityUtils.getUserId());
        }

        return miniWarehouseMapper.insertMiniWarehouse(miniWarehouse);
    }

    /**
     * 修改场库信息
     *
     * @param miniWarehouse 场库信息
     * @return 结果
     */
    @Override
    public int updateMiniWarehouse(MiniWarehouse miniWarehouse) {
        // 获取原始场库信息
        MiniWarehouse originalWarehouse = selectMiniWarehouseById(miniWarehouse.getId());
        if (originalWarehouse == null) {
            throw new ServiceException("场库信息不存在");
        }

        // 检查是否将主场库改为子场库
        if (originalWarehouse.getParentId() == 0 && miniWarehouse.getParentId() != null
                && miniWarehouse.getParentId() != 0) {
            // 检查该主场库是否有子场库
            int childCount = countChildWarehousesByParentId(miniWarehouse.getId());
            if (childCount > 0) {
                throw new ServiceException("该主场库下还有" + childCount + "个子场库，不能修改为子场库");
            }
        }

        // 自动设置用户ID为当前登录用户ID（如果为空）
        if (miniWarehouse.getUserId() == null) {
            miniWarehouse.setUserId(SecurityUtils.getUserId());
        }

        return miniWarehouseMapper.updateMiniWarehouse(miniWarehouse);
    }

    /**
     * 批量删除场库信息
     *
     * @param ids 需要删除的场库信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniWarehouseByIds(Long[] ids) {
        // 检查是否有关联的管理员
        for (Long id : ids) {
            int count = miniWarehouseManagerMapper.countManagerByWarehouseId(id);
            if (count > 0) {
                MiniWarehouse warehouse = selectMiniWarehouseById(id);
                throw new ServiceException("场库'" + warehouse.getWarehouseName() + "'下还有管理员，不能删除");
            }
        }
        return miniWarehouseMapper.deleteMiniWarehouseByIds(ids);
    }

    /**
     * 删除场库信息
     *
     * @param id 场库信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniWarehouseById(Long id) {
        // 检查是否有关联的管理员
        int count = miniWarehouseManagerMapper.countManagerByWarehouseId(id);
        if (count > 0) {
            MiniWarehouse warehouse = selectMiniWarehouseById(id);
            throw new ServiceException("场库'" + warehouse.getWarehouseName() + "'下还有管理员，不能删除");
        }
        return miniWarehouseMapper.deleteMiniWarehouseById(id);
    }

    /**
     * 查询所有场库列表（用于下拉选择）
     *
     * @return 场库列表
     */
    @Override
    public List<MiniWarehouse> selectMiniWarehouseAll() {
        return miniWarehouseMapper.selectMiniWarehouseAll();
    }

    /**
     * 根据运营商ID查询场库列表
     *
     * @param operatorId 运营商ID
     * @return 场库列表
     */
    @Override
    public List<MiniWarehouse> selectMiniWarehouseByOperatorId(Long operatorId) {
        return miniWarehouseMapper.selectMiniWarehouseByOperatorId(operatorId);
    }

    /**
     * 查询所有子场库列表（用于下拉选择）
     *
     * @return 子场库列表
     */
    @Override
    public List<MiniWarehouse> selectChildWarehouseOptions() {
        return miniWarehouseMapper.selectChildWarehouseOptions();
    }

    /**
     * 根据父场库ID查询子场库列表
     *
     * @param parentId 父场库ID
     * @return 子场库列表
     */
    @Override
    public List<MiniWarehouse> selectChildWarehouseOptionsByParentId(Long parentId) {
        return miniWarehouseMapper.selectChildWarehouseOptionsByParentId(parentId);
    }

    /**
     * 统计场库下的子场库数量
     *
     * @param parentId 父场库ID
     * @return 子场库数量
     */
    @Override
    public int countChildWarehousesByParentId(Long parentId) {
        return miniWarehouseMapper.countChildWarehousesByParentId(parentId);
    }

    /**
     * 获取所有场库选项（包含层级关系）
     *
     * @return 场库列表
     */
    @Override
    public List<MiniWarehouse> selectAllWarehouseOptions() {
        return miniWarehouseMapper.selectAllWarehouseOptions();
    }

    /**
     * 构建场库树结构
     *
     * @param warehouses 场库列表
     * @return 树结构列表
     */
    @Override
    public List<MiniWarehouse> buildWarehouseTree(List<MiniWarehouse> warehouses) {
        List<MiniWarehouse> returnList = new ArrayList<MiniWarehouse>();
        List<Long> tempList = warehouses.stream().map(MiniWarehouse::getId).collect(Collectors.toList());
        for (MiniWarehouse warehouse : warehouses) {
            // 如果是顶级节点(parent_id=0或parent_id不在当前列表中), 遍历该父节点的所有子节点
            if (warehouse.getParentId() == null || warehouse.getParentId() == 0L
                    || !tempList.contains(warehouse.getParentId())) {
                recursionFn(warehouses, warehouse);
                returnList.add(warehouse);
            }
        }
        if (returnList.isEmpty()) {
            returnList = warehouses;
        }
        return returnList;
    }

    /**
     * 查询场库树结构信息
     *
     * @param miniWarehouse 场库信息
     * @return 场库树信息集合
     */
    @Override
    public List<MiniWarehouse> selectWarehouseTreeList(MiniWarehouse miniWarehouse) {
        List<MiniWarehouse> warehouses = selectMiniWarehouseList(miniWarehouse);
        return buildWarehouseTree(warehouses);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<MiniWarehouse> list, MiniWarehouse t) {
        // 得到子节点列表
        List<MiniWarehouse> childList = getChildList(list, t);
        t.setChildren(childList);
        for (MiniWarehouse tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<MiniWarehouse> getChildList(List<MiniWarehouse> list, MiniWarehouse t) {
        List<MiniWarehouse> tlist = new ArrayList<MiniWarehouse>();
        Iterator<MiniWarehouse> it = list.iterator();
        while (it.hasNext()) {
            MiniWarehouse n = (MiniWarehouse) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<MiniWarehouse> list, MiniWarehouse t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
