package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.core.utils.StringUtils;

import com.lgjy.system.mapper.MiniAdvertConfigMapper;
import com.lgjy.system.domain.MiniAdvertConfig;
import com.lgjy.system.service.IMiniAdvertConfigService;

/**
 * 广告配置信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniAdvertConfigServiceImpl implements IMiniAdvertConfigService {
    @Autowired
    private MiniAdvertConfigMapper miniAdvertConfigMapper;

    /**
     * 查询广告配置信息
     * 
     * @param id 广告配置信息主键
     * @return 广告配置信息
     */
    @Override
    public MiniAdvertConfig selectMiniAdvertConfigById(Long id) {
        return miniAdvertConfigMapper.selectMiniAdvertConfigById(id);
    }

    /**
     * 查询广告配置信息列表
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 广告配置信息
     */
    @Override
    public List<MiniAdvertConfig> selectMiniAdvertConfigList(MiniAdvertConfig miniAdvertConfig) {
        return miniAdvertConfigMapper.selectMiniAdvertConfigList(miniAdvertConfig);
    }

    /**
     * 新增广告配置信息
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 结果
     */
    @Override
    public int insertMiniAdvertConfig(MiniAdvertConfig miniAdvertConfig) {
        miniAdvertConfig.setDeleteFlag(0);
        return miniAdvertConfigMapper.insertMiniAdvertConfig(miniAdvertConfig);
    }

    /**
     * 修改广告配置信息
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 结果
     */
    @Override
    public int updateMiniAdvertConfig(MiniAdvertConfig miniAdvertConfig) {
        return miniAdvertConfigMapper.updateMiniAdvertConfig(miniAdvertConfig);
    }

    /**
     * 批量删除广告配置信息
     * 
     * @param ids 需要删除的广告配置信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniAdvertConfigByIds(Long[] ids) {
        return miniAdvertConfigMapper.deleteMiniAdvertConfigByIds(ids);
    }

    /**
     * 删除广告配置信息信息
     * 
     * @param id 广告配置信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniAdvertConfigById(Long id) {
        return miniAdvertConfigMapper.deleteMiniAdvertConfigById(id);
    }

    /**
     * 校验广告标题是否唯一
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 结果
     */
    @Override
    public boolean checkAdvertTitleUnique(MiniAdvertConfig miniAdvertConfig) {
        Long id = StringUtils.isNull(miniAdvertConfig.getId()) ? -1L : miniAdvertConfig.getId();
        MiniAdvertConfig info = miniAdvertConfigMapper.checkAdvertTitleUnique(miniAdvertConfig.getAdvertTitle());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue()) {
            return false;
        }
        return true;
    }
}
