package com.lgjy.system.service.impl;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.system.mapper.MiniVipTransactionMapper;
import com.lgjy.system.mapper.MiniWarehouseMapper;
import com.lgjy.system.mapper.MiniVipMemberMapper;
import com.lgjy.system.mapper.MiniVipPackageMapper;
import com.lgjy.system.domain.MiniVipTransaction;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.domain.MiniVipMember;
import com.lgjy.system.domain.MiniVipPackage;
import com.lgjy.system.service.IMiniVipTransactionService;
import com.lgjy.common.enums.SpecialUserPackageEnum;

/**
 * 会员交易记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniVipTransactionServiceImpl implements IMiniVipTransactionService {
    @Autowired
    private MiniVipTransactionMapper miniVipTransactionMapper;

    @Autowired
    private MiniWarehouseMapper miniWarehouseMapper;

    @Autowired
    private MiniVipMemberMapper miniVipMemberMapper;

    @Autowired
    private MiniVipPackageMapper miniVipPackageMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询会员交易记录
     *
     * @param id 会员交易记录主键
     * @return 会员交易记录
     */
    @Override
    public MiniVipTransaction selectMiniVipTransactionById(Long id) {
        MiniVipTransaction vipTransaction = miniVipTransactionMapper.selectMiniVipTransactionById(id);
        if (vipTransaction != null) {
            // 填充关联信息
            fillRelatedInfo(vipTransaction);
        }
        return vipTransaction;
    }

    /**
     * 查询会员交易记录列表
     *
     * @param miniVipTransaction 会员交易记录
     * @return 会员交易记录
     */
    @Override
    public List<MiniVipTransaction> selectMiniVipTransactionList(MiniVipTransaction miniVipTransaction) {
        List<MiniVipTransaction> list = miniVipTransactionMapper.selectMiniVipTransactionList(miniVipTransaction);
        // 填充关联信息
        for (MiniVipTransaction vipTransaction : list) {
            fillRelatedInfo(vipTransaction);
        }
        return list;
    }

    /**
     * 填充关联信息
     */
    private void fillRelatedInfo(MiniVipTransaction vipTransaction) {
        // 填充场库名称
        if (vipTransaction.getWarehouseId() != null) {
            MiniWarehouse warehouse = miniWarehouseMapper
                    .selectMiniWarehouseById(vipTransaction.getWarehouseId());
            if (warehouse != null) {
                vipTransaction.setWarehouseName(warehouse.getWarehouseName());
            }
        }

        // 填充套餐名称 - 根据会员类型决定从哪里获取套餐信息
        if (vipTransaction.getPackageId() != null) {
            String packageName = getPackageNameByVipType(vipTransaction.getPackageId(), vipTransaction.getVipType());
            vipTransaction.setPackageName(packageName);
        }
    }

    /**
     * 根据会员类型获取套餐名称
     *
     * @param packageId 套餐ID
     * @param vipType 会员类型 (0=普通会员, 1=集团客户, 2=VIP客户)
     * @return 套餐名称
     */
    private String getPackageNameByVipType(Long packageId, Integer vipType) {
        if (packageId == null) {
            return null;
        }

        // 普通会员(0)：从数据库套餐表查询
        if (vipType == null || vipType == 0) {
            MiniVipPackage vipPackage = miniVipPackageMapper.selectMiniVipPackageById(packageId);
            return vipPackage != null ? vipPackage.getPackageName() : null;
        }

        // 集团客户(1)、VIP客户(2)：从枚举类查询
        SpecialUserPackageEnum packageEnum = SpecialUserPackageEnum.getEnum(packageId);
        return packageEnum != null ? packageEnum.getName() : null;
    }

    /**
     * 新增会员交易记录
     *
     * @param miniVipTransaction 会员交易记录
     * @return 结果
     */
    @Override
    public int insertMiniVipTransaction(MiniVipTransaction miniVipTransaction) {
        // 使用雪花算法生成ID
        miniVipTransaction.setId(snowflakeIdGenerator.nextId());
        miniVipTransaction.setDeleteFlag(0);

        // 如果没有设置交易时间，使用当前时间（同创建时间）
        if (miniVipTransaction.getTransactTime() == null) {
            miniVipTransaction.setTransactTime(new java.util.Date());
        }

        return miniVipTransactionMapper.insertMiniVipTransaction(miniVipTransaction);
    }

    /**
     * 修改会员交易记录
     *
     * @param miniVipTransaction 会员交易记录
     * @return 结果
     */
    @Override
    public int updateMiniVipTransaction(MiniVipTransaction miniVipTransaction) {
        return miniVipTransactionMapper.updateMiniVipTransaction(miniVipTransaction);
    }

    /**
     * 批量删除会员交易记录
     *
     * @param ids 需要删除的会员交易记录主键
     * @return 结果
     */
    @Override
    public int deleteMiniVipTransactionByIds(Long[] ids) {
        return miniVipTransactionMapper.deleteMiniVipTransactionByIds(ids);
    }

    /**
     * 删除会员交易记录信息
     *
     * @param id 会员交易记录主键
     * @return 结果
     */
    @Override
    public int deleteMiniVipTransactionById(Long id) {
        return miniVipTransactionMapper.deleteMiniVipTransactionById(id);
    }

    /**
     * 校验交易流水号是否唯一
     *
     * @param miniVipTransaction 交易记录
     * @return 结果
     */
    @Override
    public boolean checkTransactionNoUnique(MiniVipTransaction miniVipTransaction) {
        Long transactionId = StringUtils.isNull(miniVipTransaction.getId()) ? -1L : miniVipTransaction.getId();
        MiniVipTransaction info = miniVipTransactionMapper.checkTransactionNoUnique(miniVipTransaction.getTradeId());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != transactionId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 根据会员ID查询交易记录
     *
     * @param memberId 会员ID
     * @return 交易记录列表
     */
    @Override
    public List<MiniVipTransaction> selectTransactionsByMemberId(Long memberId) {
        return miniVipTransactionMapper.selectTransactionsByMemberId(memberId);
    }

    /**
     * 根据套餐ID查询交易记录
     *
     * @param packageId 套餐ID
     * @return 交易记录列表
     */
    @Override
    public List<MiniVipTransaction> selectTransactionsByPackageId(Long packageId) {
        return miniVipTransactionMapper.selectTransactionsByPackageId(packageId);
    }

    /**
     * 查询指定时间范围内的交易记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 交易记录列表
     */
    @Override
    public List<MiniVipTransaction> selectTransactionsByTimeRange(String startTime, String endTime) {
        return miniVipTransactionMapper.selectTransactionsByTimeRange(startTime, endTime);
    }

    /**
     * 统计交易金额按状态
     *
     * @param params 查询参数（可包含warehouseId）
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> sumAmountByStatus(Map<String, Object> params) {
        return miniVipTransactionMapper.sumAmountByStatus(params);
    }

    /**
     * 统计交易数量按类型
     *
     * @param params 查询参数（可包含warehouseId）
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> countTransactionsByType(Map<String, Object> params) {
        return miniVipTransactionMapper.countTransactionsByType(params);
    }

    /**
     * 获取所有子场库选项
     *
     * @return 子场库列表
     */
    @Override
    public List<MiniWarehouse> selectChildWarehouseOptions() {
        return miniWarehouseMapper.selectChildWarehouseOptions();
    }

    /**
     * 根据父场库ID获取子场库选项
     *
     * @param parentId 父场库ID
     * @return 子场库列表
     */
    @Override
    public List<MiniWarehouse> selectChildWarehouseOptionsByParentId(Long parentId) {
        return miniWarehouseMapper.selectChildWarehouseOptionsByParentId(parentId);
    }
}
