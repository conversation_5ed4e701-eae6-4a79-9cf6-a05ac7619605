package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.SysAgreement;
import com.lgjy.system.service.ISysAgreementService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 系统协议Controller
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@RestController
@RequestMapping("/agreement")
public class SysAgreementController extends BaseController
{
    @Autowired
    private ISysAgreementService sysAgreementService;

    /**
     * 查询系统协议列表
     */
    @RequiresPermissions("system:agreement:list")
    @GetMapping("/list")
    public TableDataInfo list(SysAgreement sysAgreement)
    {
        startPage();
        List<SysAgreement> list = sysAgreementService.selectSysAgreementList(sysAgreement);
        return getDataTable(list);
    }

    /**
     * 导出系统协议列表
     */
    @RequiresPermissions("system:agreement:export")
    @Log(title = "系统协议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAgreement sysAgreement)
    {
        List<SysAgreement> list = sysAgreementService.selectSysAgreementList(sysAgreement);
        ExcelUtil<SysAgreement> util = new ExcelUtil<SysAgreement>(SysAgreement.class);
        util.exportExcel(response, list, "系统协议数据");
    }

    /**
     * 获取系统协议详细信息
     */
    @RequiresPermissions("system:agreement:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysAgreementService.selectSysAgreementById(id));
    }

    /**
     * 新增系统协议
     */
    @RequiresPermissions("system:agreement:add")
    @Log(title = "系统协议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAgreement sysAgreement)
    {
        try {
            return toAjax(sysAgreementService.insertSysAgreement(sysAgreement));
        } catch (Exception e) {
            logger.error("新增协议失败", e);
            return AjaxResult.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改系统协议
     */
    @RequiresPermissions("system:agreement:edit")
    @Log(title = "系统协议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAgreement sysAgreement)
    {
        try {
            return toAjax(sysAgreementService.updateSysAgreement(sysAgreement));
        } catch (Exception e) {
            logger.error("修改协议失败", e);
            return AjaxResult.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除系统协议
     */
    @RequiresPermissions("system:agreement:remove")
    @Log(title = "系统协议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysAgreementService.deleteSysAgreementByIds(ids));
    }
}
