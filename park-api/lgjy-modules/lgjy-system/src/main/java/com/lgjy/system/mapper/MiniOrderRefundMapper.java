package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniOrderRefund;

/**
 * 订单退款记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface MiniOrderRefundMapper 
{
    /**
     * 查询订单退款记录
     * 
     * @param id 订单退款记录主键
     * @return 订单退款记录
     */
    public MiniOrderRefund selectMiniOrderRefundById(Long id);

    /**
     * 查询订单退款记录列表
     * 
     * @param miniOrderRefund 订单退款记录
     * @return 订单退款记录集合
     */
    public List<MiniOrderRefund> selectMiniOrderRefundList(MiniOrderRefund miniOrderRefund);

    /**
     * 新增订单退款记录
     * 
     * @param miniOrderRefund 订单退款记录
     * @return 结果
     */
    public int insertMiniOrderRefund(MiniOrderRefund miniOrderRefund);

    /**
     * 修改订单退款记录
     * 
     * @param miniOrderRefund 订单退款记录
     * @return 结果
     */
    public int updateMiniOrderRefund(MiniOrderRefund miniOrderRefund);

    /**
     * 删除订单退款记录
     * 
     * @param id 订单退款记录主键
     * @return 结果
     */
    public int deleteMiniOrderRefundById(Long id);

    /**
     * 批量删除订单退款记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniOrderRefundByIds(Long[] ids);

    /**
     * 根据订单号查询退款记录
     * 
     * @param tradeId 订单号
     * @return 退款记录列表
     */
    public List<MiniOrderRefund> selectMiniOrderRefundByTradeId(String tradeId);
}
