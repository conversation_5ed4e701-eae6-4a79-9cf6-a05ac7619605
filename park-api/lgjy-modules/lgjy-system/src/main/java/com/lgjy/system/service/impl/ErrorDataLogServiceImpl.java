package com.lgjy.system.service.impl;

import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.utils.uuid.IdUtils;
import com.lgjy.system.domain.ErrorDataLog;
import com.lgjy.system.mapper.ErrorDataLogMapper;
import com.lgjy.system.service.IErrorDataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 错误数据日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Service
public class ErrorDataLogServiceImpl implements IErrorDataLogService {
    @Autowired
    private ErrorDataLogMapper errorDataLogMapper;

    /**
     * 查询错误数据日志
     *
     * @param id 错误数据日志主键
     * @return 错误数据日志
     */
    @Override
    public ErrorDataLog selectErrorDataLogById(String id) {
        return errorDataLogMapper.selectErrorDataLogById(id);
    }

    /**
     * 查询错误数据日志列表
     *
     * @param errorDataLog 错误数据日志
     * @return 错误数据日志
     */
    @Override
    public List<ErrorDataLog> selectErrorDataLogList(ErrorDataLog errorDataLog) {
        return errorDataLogMapper.selectErrorDataLogList(errorDataLog);
    }

    /**
     * 新增错误数据日志
     *
     * @param errorDataLog 错误数据日志
     * @return 结果
     */
    @Override
    public int insertErrorDataLog(ErrorDataLog errorDataLog) {
        if (errorDataLog.getId() == null || errorDataLog.getId().isEmpty()) {
            errorDataLog.setId(IdUtils.fastUUID());
        }
        errorDataLog.setLastUpdate(DateUtils.getNowDate());
        return errorDataLogMapper.insertErrorDataLog(errorDataLog);
    }

    /**
     * 修改错误数据日志
     *
     * @param errorDataLog 错误数据日志
     * @return 结果
     */
    @Override
    public int updateErrorDataLog(ErrorDataLog errorDataLog) {
        errorDataLog.setLastUpdate(DateUtils.getNowDate());
        return errorDataLogMapper.updateErrorDataLog(errorDataLog);
    }

    /**
     * 批量删除错误数据日志
     *
     * @param ids 需要删除的错误数据日志主键
     * @return 结果
     */
    @Override
    public int deleteErrorDataLogByIds(String[] ids) {
        return errorDataLogMapper.deleteErrorDataLogByIds(ids);
    }

    /**
     * 删除错误数据日志信息
     *
     * @param id 错误数据日志主键
     * @return 结果
     */
    @Override
    public int deleteErrorDataLogById(String id) {
        return errorDataLogMapper.deleteErrorDataLogById(id);
    }

    /**
     * 根据车牌号查询错误数据日志列表
     *
     * @param plateNum 车牌号
     * @return 错误数据日志集合
     */
    @Override
    public List<ErrorDataLog> selectErrorDataLogByPlateNum(String plateNum) {
        return errorDataLogMapper.selectErrorDataLogByPlateNum(plateNum);
    }

    /**
     * 根据场库ID查询错误数据日志列表
     *
     * @param parkingId 场库ID
     * @return 错误数据日志集合
     */
    @Override
    public List<ErrorDataLog> selectErrorDataLogByParkingId(String parkingId) {
        return errorDataLogMapper.selectErrorDataLogByParkingId(parkingId);
    }

    /**
     * 根据错误码查询错误数据日志列表
     *
     * @param errCode 错误码
     * @return 错误数据日志集合
     */
    @Override
    public List<ErrorDataLog> selectErrorDataLogByErrCode(Integer errCode) {
        return errorDataLogMapper.selectErrorDataLogByErrCode(errCode);
    }

    /**
     * 统计错误数据日志数量
     *
     * @param errorDataLog 查询条件
     * @return 统计结果
     */
    @Override
    public int countErrorDataLog(ErrorDataLog errorDataLog) {
        return errorDataLogMapper.countErrorDataLog(errorDataLog);
    }

    /**
     * 统计错误数据日志按错误码分组
     *
     * @param errorDataLog 查询条件
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> countErrorDataLogByErrCode(ErrorDataLog errorDataLog) {
        return errorDataLogMapper.countErrorDataLogByErrCode(errorDataLog);
    }

    /**
     * 统计错误数据日志按场库分组
     *
     * @param errorDataLog 查询条件
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> countErrorDataLogByParking(ErrorDataLog errorDataLog) {
        return errorDataLogMapper.countErrorDataLogByParking(errorDataLog);
    }
}
