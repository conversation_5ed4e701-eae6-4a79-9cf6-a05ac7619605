package com.lgjy.system.service.impl;

import java.util.List;
import com.lgjy.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.SysAgreementMapper;
import com.lgjy.system.domain.SysAgreement;
import com.lgjy.system.service.ISysAgreementService;

/**
 * 系统协议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@Service
public class SysAgreementServiceImpl implements ISysAgreementService 
{
    @Autowired
    private SysAgreementMapper sysAgreementMapper;

    /**
     * 查询系统协议
     * 
     * @param id 系统协议主键
     * @return 系统协议
     */
    @Override
    public SysAgreement selectSysAgreementById(Long id)
    {
        return sysAgreementMapper.selectSysAgreementById(id);
    }

    /**
     * 查询系统协议列表
     * 
     * @param sysAgreement 系统协议
     * @return 系统协议
     */
    @Override
    public List<SysAgreement> selectSysAgreementList(SysAgreement sysAgreement)
    {
        return sysAgreementMapper.selectSysAgreementList(sysAgreement);
    }

    /**
     * 新增系统协议
     * 
     * @param sysAgreement 系统协议
     * @return 结果
     */
    @Override
    public int insertSysAgreement(SysAgreement sysAgreement)
    {
        sysAgreement.setCreateTime(DateUtils.getNowDate());
        sysAgreement.setDeleteFlag(0);
        return sysAgreementMapper.insertSysAgreement(sysAgreement);
    }

    /**
     * 修改系统协议
     * 
     * @param sysAgreement 系统协议
     * @return 结果
     */
    @Override
    public int updateSysAgreement(SysAgreement sysAgreement)
    {
        sysAgreement.setUpdateTime(DateUtils.getNowDate());
        return sysAgreementMapper.updateSysAgreement(sysAgreement);
    }

    /**
     * 批量删除系统协议
     * 
     * @param ids 需要删除的系统协议主键
     * @return 结果
     */
    @Override
    public int deleteSysAgreementByIds(Long[] ids)
    {
        return sysAgreementMapper.deleteSysAgreementByIds(ids);
    }

    /**
     * 删除系统协议信息
     * 
     * @param id 系统协议主键
     * @return 结果
     */
    @Override
    public int deleteSysAgreementById(Long id)
    {
        return sysAgreementMapper.deleteSysAgreementById(id);
    }
}
