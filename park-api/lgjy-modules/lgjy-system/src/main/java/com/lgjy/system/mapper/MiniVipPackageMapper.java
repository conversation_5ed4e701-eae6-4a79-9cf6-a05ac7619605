package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniVipPackage;

/**
 * 会员套餐配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MiniVipPackageMapper {
    /**
     * 查询会员套餐配置
     * 
     * @param id 会员套餐配置主键
     * @return 会员套餐配置
     */
    public MiniVipPackage selectMiniVipPackageById(Long id);

    /**
     * 查询会员套餐配置列表
     * 
     * @param miniVipPackage 会员套餐配置
     * @return 会员套餐配置集合
     */
    public List<MiniVipPackage> selectMiniVipPackageList(MiniVipPackage miniVipPackage);

    /**
     * 新增会员套餐配置
     * 
     * @param miniVipPackage 会员套餐配置
     * @return 结果
     */
    public int insertMiniVipPackage(MiniVipPackage miniVipPackage);

    /**
     * 修改会员套餐配置
     * 
     * @param miniVipPackage 会员套餐配置
     * @return 结果
     */
    public int updateMiniVipPackage(MiniVipPackage miniVipPackage);

    /**
     * 删除会员套餐配置
     * 
     * @param id 会员套餐配置主键
     * @return 结果
     */
    public int deleteMiniVipPackageById(Long id);

    /**
     * 批量删除会员套餐配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniVipPackageByIds(Long[] ids);

    /**
     * 根据停车场ID查询套餐列表
     *
     * @param warehouseId 停车场ID
     * @return 套餐列表
     */
    public List<MiniVipPackage> selectPackagesByWarehouseId(Long warehouseId);
}
