package com.lgjy.system.controller;

import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.ErrorDataLog;
import com.lgjy.system.service.IErrorDataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 错误数据日志Controller
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@RestController
@RequestMapping("/errorDataLog")
public class ErrorDataLogController extends BaseController {
    @Autowired
    private IErrorDataLogService errorDataLogService;

    /**
     * 查询错误数据日志列表
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/list")
    public TableDataInfo list(ErrorDataLog errorDataLog) {
        startPage();
        List<ErrorDataLog> list = errorDataLogService.selectErrorDataLogList(errorDataLog);
        return getDataTable(list);
    }

    /**
     * 导出错误数据日志列表
     */
    @RequiresPermissions("system:errorDataLog:export")
    @Log(title = "错误数据日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErrorDataLog errorDataLog) {
        List<ErrorDataLog> list = errorDataLogService.selectErrorDataLogList(errorDataLog);
        ExcelUtil<ErrorDataLog> util = new ExcelUtil<ErrorDataLog>(ErrorDataLog.class);
        util.exportExcel(response, list, "错误数据日志数据");
    }

    /**
     * 获取错误数据日志详细信息
     */
    @RequiresPermissions("system:errorDataLog:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(errorDataLogService.selectErrorDataLogById(id));
    }

    /**
     * 新增错误数据日志
     */
    @RequiresPermissions("system:errorDataLog:add")
    @Log(title = "错误数据日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErrorDataLog errorDataLog) {
        return toAjax(errorDataLogService.insertErrorDataLog(errorDataLog));
    }

    /**
     * 修改错误数据日志
     */
    @RequiresPermissions("system:errorDataLog:edit")
    @Log(title = "错误数据日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErrorDataLog errorDataLog) {
        return toAjax(errorDataLogService.updateErrorDataLog(errorDataLog));
    }

    /**
     * 删除错误数据日志
     */
    @RequiresPermissions("system:errorDataLog:remove")
    @Log(title = "错误数据日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(errorDataLogService.deleteErrorDataLogByIds(ids));
    }

    /**
     * 根据车牌号查询错误数据日志列表
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/plateNum/{plateNum}")
    public AjaxResult getByPlateNum(@PathVariable String plateNum) {
        List<ErrorDataLog> list = errorDataLogService.selectErrorDataLogByPlateNum(plateNum);
        return success(list);
    }

    /**
     * 根据场库ID查询错误数据日志列表
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/parking/{parkingId}")
    public AjaxResult getByParkingId(@PathVariable String parkingId) {
        List<ErrorDataLog> list = errorDataLogService.selectErrorDataLogByParkingId(parkingId);
        return success(list);
    }

    /**
     * 根据错误码查询错误数据日志列表
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/errCode/{errCode}")
    public AjaxResult getByErrCode(@PathVariable Integer errCode) {
        List<ErrorDataLog> list = errorDataLogService.selectErrorDataLogByErrCode(errCode);
        return success(list);
    }

    /**
     * 统计错误数据日志数量
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/count")
    public AjaxResult count(ErrorDataLog errorDataLog) {
        int count = errorDataLogService.countErrorDataLog(errorDataLog);
        return success(count);
    }

    /**
     * 统计错误数据日志按错误码分组
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/countByErrCode")
    public AjaxResult countByErrCode(ErrorDataLog errorDataLog) {
        List<Map<String, Object>> result = errorDataLogService.countErrorDataLogByErrCode(errorDataLog);
        return success(result);
    }

    /**
     * 统计错误数据日志按场库分组
     */
    @RequiresPermissions("system:errorDataLog:list")
    @GetMapping("/countByParking")
    public AjaxResult countByParking(ErrorDataLog errorDataLog) {
        List<Map<String, Object>> result = errorDataLogService.countErrorDataLogByParking(errorDataLog);
        return success(result);
    }
}
