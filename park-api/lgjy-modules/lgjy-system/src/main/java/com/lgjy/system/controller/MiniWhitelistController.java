package com.lgjy.system.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniWhitelist;
import com.lgjy.system.service.IMiniWhitelistService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 白名单管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/owner/whitelist")
public class MiniWhitelistController extends BaseController {
    @Autowired
    private IMiniWhitelistService miniWhitelistService;

    /**
     * 查询白名单管理列表
     */
    @RequiresPermissions("owner:whitelist:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniWhitelist miniWhitelist) {
        startPage();
        List<MiniWhitelist> list = miniWhitelistService.selectMiniWhitelistList(miniWhitelist);
        return getDataTable(list);
    }

    /**
     * 导出白名单管理列表
     */
    @RequiresPermissions("owner:whitelist:export")
    @Log(title = "白名单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniWhitelist miniWhitelist) {
        List<MiniWhitelist> list = miniWhitelistService.selectMiniWhitelistList(miniWhitelist);
        ExcelUtil<MiniWhitelist> util = new ExcelUtil<MiniWhitelist>(MiniWhitelist.class);
        util.exportExcel(response, list, "白名单管理数据");
    }

    /**
     * 获取白名单管理详细信息
     */
    @RequiresPermissions("owner:whitelist:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(miniWhitelistService.selectMiniWhitelistById(id));
    }

    /**
     * 新增白名单管理
     */
    @RequiresPermissions("owner:whitelist:add")
    @Log(title = "白名单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniWhitelist miniWhitelist) {
        return toAjax(miniWhitelistService.addWhitelistWithPush(miniWhitelist));
    }

    /**
     * 修改白名单管理
     */
    @RequiresPermissions("owner:whitelist:edit")
    @Log(title = "白名单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniWhitelist miniWhitelist) {
        return toAjax(miniWhitelistService.updateWhitelistWithPush(miniWhitelist));
    }

    /**
     * 删除白名单管理
     */
    @RequiresPermissions("owner:whitelist:remove")
    @Log(title = "白名单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(miniWhitelistService.removeWhitelistWithPush(id));
    }
}
