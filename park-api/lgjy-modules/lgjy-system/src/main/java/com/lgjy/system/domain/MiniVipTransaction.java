package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员交易记录对象 mini_vip_transact_record
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MiniVipTransaction extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotNull(message = "场库ID不能为空")
    private Long warehouseId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotNull(message = "套餐ID不能为空")
    private Long packageId;

    /** 套餐名称 */
    @Excel(name = "套餐名称")
    private String packageName;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    /** 操作类型 */
    @Excel(name = "操作类型", readConverterExp = "1=新购,2=续费,3=退款")
    private Integer operateType;

    /** 交易时间 */
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "交易时间不能为空")
    private Date transactTime;

    /** VIP开始时间 */
    @Excel(name = "VIP开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "VIP开始时间不能为空")
    private Date beginVipTime;

    /** VIP到期时间 */
    @Excel(name = "VIP到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationTime;

    /** 应付金额 */
    @Excel(name = "应付金额")
    @NotNull(message = "应付金额不能为空")
    @DecimalMin(value = "0.01", message = "应付金额必须大于0")
    private BigDecimal paymentAmount;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal actualPayment;

    /** 交易ID */
    @Excel(name = "交易ID")
    private String tradeId;

    /** 支付状态 */
    @Excel(name = "支付状态", readConverterExp = "0=未支付,1=进行中,2=支付中,3=支付失败,4=已退款,5=已支付")
    private Integer payStatus;

    /** 发票ID */
    @Excel(name = "发票ID")
    private Long invoiceId;

    /** 停车位号 */
    @Excel(name = "停车位号")
    private String parkingSpaceNo;

    /** 团购记录ID */
    @Excel(name = "团购记录ID")
    private Long groupBuyRecordId;

    /** 选择时间 */
    @Excel(name = "选择时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chooseTime;

    /** 会员类型 */
    @Excel(name = "会员类型", readConverterExp = "0=普通会员,1=集团客户,2=VIP客户,3=团购会员")
    private Integer vipType;

    /** 删除标识 */
    private Integer deleteFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("warehouseId", getWarehouseId())
                .append("packageId", getPackageId())
                .append("userId", getUserId())
                .append("phoneNumber", getPhoneNumber())
                .append("plateNo", getPlateNo())
                .append("packageId", getPackageId())
                .append("operateType", getOperateType())
                .append("transactTime", getTransactTime())
                .append("beginVipTime", getBeginVipTime())
                .append("expirationTime", getExpirationTime())
                .append("paymentAmount", getPaymentAmount())
                .append("discountAmount", getDiscountAmount())
                .append("actualPayment", getActualPayment())
                .append("tradeId", getTradeId())
                .append("payStatus", getPayStatus())
                .append("invoiceId", getInvoiceId())
                .append("parkingSpaceNo", getParkingSpaceNo())
                .append("groupBuyRecordId", getGroupBuyRecordId())
                .append("chooseTime", getChooseTime())
                .append("remark", getRemark())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
