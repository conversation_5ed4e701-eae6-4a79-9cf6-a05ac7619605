package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.GateParkingInfo;

/**
 * 车辆出入场记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IGateParkingInfoService {
    /**
     * 查询车辆出入场记录
     *
     * @param id 车辆出入场记录主键
     * @return 车辆出入场记录
     */
    public GateParkingInfo selectGateParkingInfoById(String id);

    /**
     * 查询车辆出入场记录列表
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 车辆出入场记录集合
     */
    public List<GateParkingInfo> selectGateParkingInfoList(GateParkingInfo gateParkingInfo);

    /**
     * 新增车辆出入场记录
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 结果
     */
    public int insertGateParkingInfo(GateParkingInfo gateParkingInfo);

    /**
     * 修改车辆出入场记录
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 结果
     */
    public int updateGateParkingInfo(GateParkingInfo gateParkingInfo);

    /**
     * 批量删除车辆出入场记录
     *
     * @param ids 需要删除的车辆出入场记录主键集合
     * @return 结果
     */
    public int deleteGateParkingInfoByIds(String[] ids);

    /**
     * 删除车辆出入场记录信息
     *
     * @param id 车辆出入场记录主键
     * @return 结果
     */
    public int deleteGateParkingInfoById(String id);

    /**
     * 根据车牌号查询车辆出入场记录
     *
     * @param plateNum 车牌号
     * @return 车辆出入场记录集合
     */
    public List<GateParkingInfo> selectGateParkingInfoByPlateNum(String plateNum);

    /**
     * 根据停车场ID查询车辆出入场记录
     *
     * @param parkingId 停车场ID
     * @return 车辆出入场记录集合
     */
    public List<GateParkingInfo> selectGateParkingInfoByParkingId(String parkingId);

    /**
     * 根据状态查询车辆出入场记录
     *
     * @param status 状态
     * @return 车辆出入场记录集合
     */
    public List<GateParkingInfo> selectGateParkingInfoByStatus(Integer status);

    /**
     * 统计车辆出入场记录数量
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 结果
     */
    public int countGateParkingInfo(GateParkingInfo gateParkingInfo);

    /**
     * 获取车辆类型选项
     *
     * @return 车辆类型集合
     */
    public List<String> selectCarTypeOptions();

    /**
     * 获取支付类型选项
     *
     * @return 支付类型集合
     */
    public List<String> selectPayTypeOptions();

    /**
     * 获取通道名称选项
     *
     * @return 通道名称集合
     */
    public List<String> selectChannelNameOptions();
}
