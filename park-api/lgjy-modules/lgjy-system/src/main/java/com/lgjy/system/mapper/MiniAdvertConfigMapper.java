package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniAdvertConfig;

/**
 * 广告配置信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MiniAdvertConfigMapper 
{
    /**
     * 查询广告配置信息
     * 
     * @param id 广告配置信息主键
     * @return 广告配置信息
     */
    public MiniAdvertConfig selectMiniAdvertConfigById(Long id);

    /**
     * 查询广告配置信息列表
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 广告配置信息集合
     */
    public List<MiniAdvertConfig> selectMiniAdvertConfigList(MiniAdvertConfig miniAdvertConfig);

    /**
     * 新增广告配置信息
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 结果
     */
    public int insertMiniAdvertConfig(MiniAdvertConfig miniAdvertConfig);

    /**
     * 修改广告配置信息
     * 
     * @param miniAdvertConfig 广告配置信息
     * @return 结果
     */
    public int updateMiniAdvertConfig(MiniAdvertConfig miniAdvertConfig);

    /**
     * 删除广告配置信息
     * 
     * @param id 广告配置信息主键
     * @return 结果
     */
    public int deleteMiniAdvertConfigById(Long id);

    /**
     * 批量删除广告配置信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniAdvertConfigByIds(Long[] ids);

    /**
     * 校验广告标题是否唯一
     * 
     * @param advertTitle 广告标题
     * @return 结果
     */
    public MiniAdvertConfig checkAdvertTitleUnique(String advertTitle);
}
