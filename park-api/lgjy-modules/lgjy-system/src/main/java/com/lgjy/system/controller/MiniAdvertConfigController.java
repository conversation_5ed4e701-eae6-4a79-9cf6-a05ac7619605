package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniAdvertConfig;
import com.lgjy.system.service.IMiniAdvertConfigService;

/**
 * 广告配置信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/advertConfig")
public class MiniAdvertConfigController extends BaseController
{
    @Autowired
    private IMiniAdvertConfigService miniAdvertConfigService;

    /**
     * 查询广告配置信息列表
     */
    @RequiresPermissions("system:advertConfig:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniAdvertConfig miniAdvertConfig)
    {
        startPage();
        List<MiniAdvertConfig> list = miniAdvertConfigService.selectMiniAdvertConfigList(miniAdvertConfig);
        return getDataTable(list);
    }

    /**
     * 导出广告配置信息列表
     */
    @RequiresPermissions("system:advertConfig:export")
    @Log(title = "广告配置信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniAdvertConfig miniAdvertConfig)
    {
        List<MiniAdvertConfig> list = miniAdvertConfigService.selectMiniAdvertConfigList(miniAdvertConfig);
        ExcelUtil<MiniAdvertConfig> util = new ExcelUtil<MiniAdvertConfig>(MiniAdvertConfig.class);
        util.exportExcel(response, list, "广告配置信息数据");
    }

    /**
     * 获取广告配置信息详细信息
     */
    @RequiresPermissions("system:advertConfig:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(miniAdvertConfigService.selectMiniAdvertConfigById(id));
    }

    /**
     * 新增广告配置信息
     */
    @RequiresPermissions("system:advertConfig:add")
    @Log(title = "广告配置信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniAdvertConfig miniAdvertConfig)
    {
        if (!miniAdvertConfigService.checkAdvertTitleUnique(miniAdvertConfig))
        {
            return error("新增广告配置'" + miniAdvertConfig.getAdvertTitle() + "'失败，广告标题已存在");
        }
        return toAjax(miniAdvertConfigService.insertMiniAdvertConfig(miniAdvertConfig));
    }

    /**
     * 修改广告配置信息
     */
    @RequiresPermissions("system:advertConfig:edit")
    @Log(title = "广告配置信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniAdvertConfig miniAdvertConfig)
    {
        if (!miniAdvertConfigService.checkAdvertTitleUnique(miniAdvertConfig))
        {
            return error("修改广告配置'" + miniAdvertConfig.getAdvertTitle() + "'失败，广告标题已存在");
        }
        return toAjax(miniAdvertConfigService.updateMiniAdvertConfig(miniAdvertConfig));
    }

    /**
     * 删除广告配置信息
     */
    @RequiresPermissions("system:advertConfig:remove")
    @Log(title = "广告配置信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(miniAdvertConfigService.deleteMiniAdvertConfigByIds(ids));
    }
}
