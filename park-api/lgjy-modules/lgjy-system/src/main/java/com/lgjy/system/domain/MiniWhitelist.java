package com.lgjy.system.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;

/**
 * 白名单管理对象 mini_whitelist
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public class MiniWhitelist extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 代理系统ID */
    private Long dlySystemId;

    /** 停车类型 */
    @Excel(name = "停车类型")
    private Integer parkType;

    /** 白名单类型 */
    @Excel(name = "白名单类型")
    private String whiteType;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 删除标记 */
    private Integer deleteFlag;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 运营商ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /** 运营商名称 */
    @Excel(name = "运营商名称")
    private String operatorName;

    /**
     * 自定义warehouseId的JSON反序列化处理
     * 兼容处理前端级联选择器传递的数组格式和单值格式
     */
    @JsonSetter("warehouseId")
    public void setWarehouseIdFromJson(Object warehouseId) {
        if (warehouseId instanceof List) {
            // 处理数组格式：取最后一个元素（用户实际选中的场库ID）
            List<?> list = (List<?>) warehouseId;
            if (!list.isEmpty()) {
                this.warehouseId = Long.valueOf(list.get(list.size() - 1).toString());
            }
        } else if (warehouseId != null) {
            // 处理单值格式
            this.warehouseId = Long.valueOf(warehouseId.toString());
        }
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    public String getPlateNo() {
        return plateNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setDlySystemId(Long dlySystemId) {
        this.dlySystemId = dlySystemId;
    }

    public Long getDlySystemId() {
        return dlySystemId;
    }

    public void setParkType(Integer parkType) {
        this.parkType = parkType;
    }

    public Integer getParkType() {
        return parkType;
    }

    public void setWhiteType(String whiteType) {
        this.whiteType = whiteType;
    }

    public String getWhiteType() {
        return whiteType;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("warehouseId", getWarehouseId())
                .append("phoneNumber", getPhoneNumber())
                .append("beginTime", getBeginTime())
                .append("endTime", getEndTime())
                .append("plateNo", getPlateNo())
                .append("name", getName())
                .append("dlySystemId", getDlySystemId())
                .append("parkType", getParkType())
                .append("whiteType", getWhiteType())
                .append("remark", getRemark())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
