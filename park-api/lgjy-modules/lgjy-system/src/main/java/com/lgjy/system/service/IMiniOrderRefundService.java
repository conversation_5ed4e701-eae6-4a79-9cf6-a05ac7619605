package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.MiniOrderRefund;

/**
 * 退款管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IMiniOrderRefundService 
{
    /**
     * 查询退款记录
     * 
     * @param id 退款记录主键
     * @return 退款记录
     */
    public MiniOrderRefund selectMiniOrderRefundById(Long id);

    /**
     * 查询退款记录列表
     * 
     * @param miniOrderRefund 退款记录
     * @return 退款记录集合
     */
    public List<MiniOrderRefund> selectMiniOrderRefundList(MiniOrderRefund miniOrderRefund);

    /**
     * 根据订单号查询退款记录
     * 
     * @param tradeId 订单号
     * @return 退款记录列表
     */
    public List<MiniOrderRefund> selectMiniOrderRefundByTradeId(String tradeId);
}
