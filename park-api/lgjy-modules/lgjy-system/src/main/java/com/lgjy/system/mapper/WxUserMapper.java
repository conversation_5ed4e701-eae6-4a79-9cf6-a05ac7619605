package com.lgjy.system.mapper;

import java.util.Date;
import java.util.List;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.system.domain.WxUserCar;
import org.apache.ibatis.annotations.Param;

/**
 * 小程序用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface WxUserMapper {
    /**
     * 查询小程序用户
     * 
     * @param id 小程序用户主键
     * @return 小程序用户
     */
    public WxUser selectWxUserById(Long id);

    /**
     * 查询小程序用户列表
     * 
     * @param wxUser 小程序用户
     * @return 小程序用户集合
     */
    public List<WxUser> selectWxUserList(WxUser wxUser);

    /**
     * 新增小程序用户
     * 
     * @param wxUser 小程序用户
     * @return 结果
     */
    public int insertWxUser(WxUser wxUser);

    /**
     * 修改小程序用户
     * 
     * @param wxUser 小程序用户
     * @return 结果
     */
    public int updateWxUser(WxUser wxUser);

    /**
     * 删除小程序用户
     * 
     * @param id 小程序用户主键
     * @return 结果
     */
    public int deleteWxUserById(Long id);

    /**
     * 批量删除小程序用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWxUserByIds(Long[] ids);

    /**
     * 批量修改用户状态
     * 
     * @param ids        用户ID数组
     * @param status     状态值
     * @param updateBy   更新者
     * @param updateTime 更新时间
     * @return 结果
     */
    public int batchUpdateStatus(@Param("ids") Long[] ids, @Param("status") Integer status,
            @Param("updateBy") Long updateBy, @Param("updateTime") Date updateTime);

    /**
     * 校验用户名称是否唯一
     * 
     * @param userName 用户名称
     * @return 结果
     */
    public WxUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     * 
     * @param phoneNumber 手机号码
     * @return 结果
     */
    public WxUser checkPhoneUnique(String phoneNumber);

    /**
     * 查询用户车辆信息
     *
     * @param userId 用户ID
     * @return 车辆信息集合
     */
    public List<WxUserCar> selectWxUserCarsByUserId(Long userId);

    /**
     * 根据手机号更新用户类型
     * @param phoneNumber 手机号
     * @param userType 用户类型
     * @return 更新结果
     */
    int updateUserTypeByPhoneNumber(@Param("phoneNumber") String phoneNumber, @Param("userType") Integer userType);

    /**
     * 根据旧手机号更新为新手机号
     * @param oldPhoneNumber 旧手机号
     * @param newPhoneNumber 新手机号
     * @return 更新结果
     */
    int updatePhoneNumberByOldPhone(@Param("oldPhoneNumber") String oldPhoneNumber, @Param("newPhoneNumber") String newPhoneNumber);

    /**
     * 根据手机号查询用户信息
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    WxUser selectUserByPhoneNumber(@Param("phoneNumber") String phoneNumber);

}
