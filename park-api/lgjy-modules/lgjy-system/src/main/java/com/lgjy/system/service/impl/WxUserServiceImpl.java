package com.lgjy.system.service.impl;

import java.util.List;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.domain.WxUserCar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.WxUserMapper;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.system.service.IWxUserService;

/**
 * 小程序用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class WxUserServiceImpl implements IWxUserService {
    @Autowired
    private WxUserMapper wxUserMapper;

    /**
     * 查询小程序用户
     * 
     * @param id 小程序用户主键
     * @return 小程序用户
     */
    @Override
    public WxUser selectWxUserById(Long id) {
        return wxUserMapper.selectWxUserById(id);
    }

    /**
     * 查询小程序用户列表
     * 
     * @param wxUser 小程序用户
     * @return 小程序用户
     */
    @Override
    public List<WxUser> selectWxUserList(WxUser wxUser) {
        return wxUserMapper.selectWxUserList(wxUser);
    }

    /**
     * 新增小程序用户
     * 
     * @param wxUser 小程序用户
     * @return 结果
     */
    @Override
    public int insertWxUser(WxUser wxUser) {
        wxUser.setCreateTime(DateUtils.getNowDate());
        return wxUserMapper.insertWxUser(wxUser);
    }

    /**
     * 修改小程序用户
     * 
     * @param wxUser 小程序用户
     * @return 结果
     */
    @Override
    public int updateWxUser(WxUser wxUser) {
        wxUser.setUpdateTime(DateUtils.getNowDate());
        return wxUserMapper.updateWxUser(wxUser);
    }

    /**
     * 批量删除小程序用户
     * 
     * @param ids 需要删除的小程序用户主键
     * @return 结果
     */
    @Override
    public int deleteWxUserByIds(Long[] ids) {
        return wxUserMapper.deleteWxUserByIds(ids);
    }

    /**
     * 删除小程序用户信息
     * 
     * @param id 小程序用户主键
     * @return 结果
     */
    @Override
    public int deleteWxUserById(Long id) {
        return wxUserMapper.deleteWxUserById(id);
    }

    /**
     * 修改用户状态
     *
     * @param wxUser 用户信息
     * @return 结果
     */
    @Override
    public int updateWxUserStatus(WxUser wxUser) {
        // 验证必要参数
        if (wxUser.getId() == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        if (wxUser.getStatus() == null) {
            throw new RuntimeException("用户状态不能为空");
        }

        wxUser.setUpdateTime(DateUtils.getNowDate());
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId != null) {
            wxUser.setUpdateBy(currentUserId);
        }

        return wxUserMapper.updateWxUser(wxUser);
    }

    /**
     * 批量修改用户状态
     * 
     * @param ids    用户ID数组
     * @param status 状态值
     * @return 结果
     */
    @Override
    public int batchUpdateStatus(Long[] ids, Integer status) {
        return wxUserMapper.batchUpdateStatus(ids, status, SecurityUtils.getUserId(), DateUtils.getNowDate());
    }

    /**
     * 校验用户账号是否唯一
     * 
     * @param wxUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(WxUser wxUser) {
        Long userId = StringUtils.isNull(wxUser.getId()) ? -1L : wxUser.getId();
        WxUser info = wxUserMapper.checkUserNameUnique(wxUser.getUserName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != userId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 校验手机号码是否唯一
     * 
     * @param wxUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkPhoneUnique(WxUser wxUser) {
        Long userId = StringUtils.isNull(wxUser.getId()) ? -1L : wxUser.getId();
        WxUser info = wxUserMapper.checkPhoneUnique(wxUser.getPhoneNumber());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != userId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查询用户车辆信息
     * 
     * @param userId 用户ID
     * @return 车辆信息集合
     */
    @Override
    public List<WxUserCar> selectWxUserCarsByUserId(Long userId) {
        return wxUserMapper.selectWxUserCarsByUserId(userId);
    }
}
