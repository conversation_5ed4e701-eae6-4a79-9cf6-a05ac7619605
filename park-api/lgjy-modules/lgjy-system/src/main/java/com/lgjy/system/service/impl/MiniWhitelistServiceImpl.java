package com.lgjy.system.service.impl;

import java.util.Date;
import java.util.List;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.Constants;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.system.api.RemoteGateService;
import com.lgjy.system.domain.MiniVipMember;
import com.lgjy.system.service.IMiniVipMemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import com.lgjy.system.mapper.MiniWhitelistMapper;
import com.lgjy.system.domain.MiniWhitelist;
import com.lgjy.system.service.IMiniWhitelistService;

/**
 * 白名单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class MiniWhitelistServiceImpl implements IMiniWhitelistService
{
    private static final Logger logger = LoggerFactory.getLogger(MiniWhitelistServiceImpl.class);

    @Autowired
    private MiniWhitelistMapper miniWhitelistMapper;

    @Autowired
    private RemoteGateService remoteGateService;

    @Autowired
    private IMiniVipMemberService miniVipMemberService;

    /**
     * 查询白名单管理
     * 
     * @param id 白名单管理主键
     * @return 白名单管理
     */
    @Override
    public MiniWhitelist selectMiniWhitelistById(Long id)
    {
        return miniWhitelistMapper.selectMiniWhitelistById(id);
    }

    /**
     * 查询白名单管理列表
     * 
     * @param miniWhitelist 白名单管理
     * @return 白名单管理
     */
    @Override
    public List<MiniWhitelist> selectMiniWhitelistList(MiniWhitelist miniWhitelist)
    {
        return miniWhitelistMapper.selectMiniWhitelistList(miniWhitelist);
    }

    /**
     * 新增白名单管理
     * 
     * @param miniWhitelist 白名单管理
     * @return 结果
     */
    @Override
    public int insertMiniWhitelist(MiniWhitelist miniWhitelist)
    {
        miniWhitelist.setCreateTime(DateUtils.getNowDate());
        miniWhitelist.setDeleteFlag(0);
        return miniWhitelistMapper.insertMiniWhitelist(miniWhitelist);
    }

    /**
     * 修改白名单管理
     * 
     * @param miniWhitelist 白名单管理
     * @return 结果
     */
    @Override
    public int updateMiniWhitelist(MiniWhitelist miniWhitelist)
    {
        miniWhitelist.setUpdateTime(DateUtils.getNowDate());
        return miniWhitelistMapper.updateMiniWhitelist(miniWhitelist);
    }

    /**
     * 批量删除白名单管理
     * 
     * @param ids 需要删除的白名单管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniWhitelistByIds(Long[] ids)
    {
        return miniWhitelistMapper.deleteMiniWhitelistByIds(ids);
    }

    /**
     * 删除白名单管理信息
     * 
     * @param id 白名单管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniWhitelistById(Long id)
    {
        return miniWhitelistMapper.deleteMiniWhitelistById(id);
    }

    /**
     * 根据车牌号查询白名单信息
     * 
     * @param plateNo 车牌号
     * @return 白名单信息
     */
    @Override
    public MiniWhitelist selectMiniWhitelistByPlateNo(String plateNo)
    {
        return miniWhitelistMapper.selectMiniWhitelistByPlateNo(plateNo);
    }

    /**
     * 根据车牌号和场库ID查询白名单信息
     * 
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 白名单信息
     */
    @Override
    public MiniWhitelist selectMiniWhitelistByPlateNoAndWarehouse(String plateNo, Long warehouseId)
    {
        return miniWhitelistMapper.selectMiniWhitelistByPlateNoAndWarehouse(plateNo, warehouseId);
    }

    /**
     * 校验车牌号是否唯一
     *
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 结果
     */
    @Override
    public String checkPlateNoUnique(String plateNo, Long warehouseId)
    {
        MiniWhitelist whitelist = miniWhitelistMapper.checkPlateNoUnique(plateNo, warehouseId);
        if (StringUtils.isNotNull(whitelist))
        {
            return "1";
        }
        return "0";
    }

    /**
     * 新增白名单管理（包含推送到道闸系统）
     *
     * @param miniWhitelist 白名单管理对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addWhitelistWithPush(MiniWhitelist miniWhitelist) {
        // 1. 参数校验
        Assert.notNull(miniWhitelist, "白名单对象不能为空");
        Assert.notNull(miniWhitelist.getWarehouseId(), "场库ID不能为空");
        Assert.hasText(miniWhitelist.getPlateNo(), "车牌号不能为空");
        Assert.notNull(miniWhitelist.getBeginTime(), "开始时间不能为空");
        Assert.notNull(miniWhitelist.getEndTime(), "结束时间不能为空");

        // 2. 校验车牌号是否已存在
        String checkResult = checkPlateNoUnique(miniWhitelist.getPlateNo(), miniWhitelist.getWarehouseId());
        if ("1".equals(checkResult)) {
            throw new ServiceException("该车牌号在当前场库已存在白名单记录");
        }

        // 3. 检查是否为会员车辆
        MiniVipMember existingMember = checkMemberExists(miniWhitelist.getPlateNo(), miniWhitelist.getWarehouseId());
        if (existingMember != null) {
            throw new ServiceException("该用户已是会员账号，无需添加白名单");
        }

        // 4. 先保存到本地数据库
        int insertResult = insertMiniWhitelist(miniWhitelist);
        if (insertResult <= 0) {
            throw new ServiceException("保存白名单记录失败");
        }

        // 5. 再推送到远程道闸系统
        try {
            String warehouseIdStr = miniWhitelist.getWarehouseId().toString();
            Assert.hasText(warehouseIdStr, "场库ID转换失败");

            JSONObject requestData = new JSONObject();
            requestData.put("parkingId", warehouseIdStr);
            requestData.put("plateNum", miniWhitelist.getPlateNo());
            requestData.put("userName", miniWhitelist.getName());
            requestData.put("beginDate", miniWhitelist.getBeginTime());
            requestData.put("endDate", miniWhitelist.getEndTime());
            R<String> pushResult = remoteGateService.saveFreeCar(requestData, SecurityConstants.INNER);

            if (pushResult.getCode() != 200) {
                throw new ServiceException("推送到道闸系统失败：" + pushResult.getMsg());
            }

            return insertResult;

        } catch (Exception e) {
            logger.error("推送白名单车辆到道闸系统异常", e);
            throw new ServiceException("推送到道闸系统失败：" + e.getMessage());
        }
    }

    /**
     * 删除白名单管理（包含从道闸系统删除）
     *
     * @param id 需要删除的白名单管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeWhitelistWithPush(Long id) {
        Assert.notNull(id, "删除ID不能为空");

        // 1. 获取白名单记录
        MiniWhitelist whitelist = selectMiniWhitelistById(id);
        if (whitelist == null) {
            throw new ServiceException("白名单记录不存在，ID: " + id);
        }

        // 2. 先删除本地数据库记录
        int deleteResult = deleteMiniWhitelistById(id);
        if (deleteResult <= 0) {
            throw new ServiceException("删除本地白名单记录失败，ID: " + id);
        }
        logger.info("成功删除本地白名单记录，ID: {}, 车牌号: {}", id, whitelist.getPlateNo());

        // 3. 再删除远程道闸系统
        try {
            String warehouseIdStr = whitelist.getWarehouseId().toString();
            if (StringUtils.isNotEmpty(warehouseIdStr) && whitelist.getPlateNo() != null) {
                JSONObject requestData = new JSONObject();
                requestData.put("parkingId", warehouseIdStr);
                requestData.put("plateNum", whitelist.getPlateNo());

                // 使用删除月租车接口删除免费车
                R<String> delResult = remoteGateService.delMonthCar(requestData, SecurityConstants.INNER);
                if (delResult.getCode() != Constants.SUCCESS) {
                    throw new ServiceException("从道闸系统删除白名单失败：" + delResult.getMsg());
                }
                logger.info("成功从道闸系统删除白名单，车牌号: {}, 场库ID: {}", whitelist.getPlateNo(), warehouseIdStr);
            }else{
                throw new ServiceException("参数错误");
            }
        } catch (Exception e) {
            logger.error("从道闸系统删除白名单失败，ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new ServiceException("从道闸系统删除白名单失败：" + e.getMessage());
        }

        return deleteResult;
    }

    /**
     * 修改白名单管理（包含推送到道闸系统）
     *
     * @param miniWhitelist 白名单管理对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateWhitelistWithPush(MiniWhitelist miniWhitelist) {
        // 1. 参数校验
        Assert.notNull(miniWhitelist, "白名单对象不能为空");
        Assert.notNull(miniWhitelist.getId(), "白名单ID不能为空");
        Assert.notNull(miniWhitelist.getWarehouseId(), "场库ID不能为空");
        Assert.hasText(miniWhitelist.getPlateNo(), "车牌号不能为空");
        Assert.notNull(miniWhitelist.getBeginTime(), "开始时间不能为空");
        Assert.notNull(miniWhitelist.getEndTime(), "结束时间不能为空");

        // 2. 检查记录是否存在
        MiniWhitelist existingWhitelist = selectMiniWhitelistById(miniWhitelist.getId());
        if (existingWhitelist == null) {
            throw new ServiceException("要修改的白名单记录不存在");
        }

        // 3. 先更新本地数据库
        int updateResult = updateMiniWhitelist(miniWhitelist);
        if (updateResult <= 0) {
            throw new ServiceException("更新本地白名单记录失败");
        }
        logger.info("成功更新本地白名单记录，ID: {}, 车牌号: {}", miniWhitelist.getId(), miniWhitelist.getPlateNo());

        // 4. 再推送到远程道闸系统
        try {
            String warehouseIdStr = miniWhitelist.getWarehouseId().toString();
            Assert.hasText(warehouseIdStr, "场库ID转换失败");

            JSONObject requestData = new JSONObject();
            requestData.put("parkingId", warehouseIdStr);
            requestData.put("plateNum", miniWhitelist.getPlateNo());
            requestData.put("userName", miniWhitelist.getName());
            requestData.put("beginDate", miniWhitelist.getBeginTime());
            requestData.put("endDate", miniWhitelist.getEndTime());
            R<String> pushResult = remoteGateService.saveFreeCar(requestData, SecurityConstants.INNER);

            if (pushResult.getCode() != 200) {
                throw new ServiceException("推送到道闸系统失败：" + pushResult.getMsg());
            }

            logger.info("成功推送白名单到道闸系统，车牌号: {}, 场库ID: {}", miniWhitelist.getPlateNo(), warehouseIdStr);
            return updateResult;

        } catch (Exception e) {
            logger.error("推送白名单车辆到道闸系统异常", e);
            throw new ServiceException("推送到道闸系统失败：" + e.getMessage());
        }
    }

    /**
     * 检查会员是否存在
     */
    private MiniVipMember checkMemberExists(String plateNo, Long warehouseId) {
        return miniVipMemberService.selectMemberByPlateNoAndWarehouse(plateNo, warehouseId);
    }
}
