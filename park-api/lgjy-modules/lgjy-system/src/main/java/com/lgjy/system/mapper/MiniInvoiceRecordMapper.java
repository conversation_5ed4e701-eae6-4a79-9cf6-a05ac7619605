package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniInvoiceRecord;

/**
 * 发票记录管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface MiniInvoiceRecordMapper 
{
    /**
     * 查询发票记录管理
     * 
     * @param id 发票记录管理主键
     * @return 发票记录管理
     */
    public MiniInvoiceRecord selectMiniInvoiceRecordById(Long id);

    /**
     * 查询发票记录管理列表
     * 
     * @param miniInvoiceRecord 发票记录管理
     * @return 发票记录管理集合
     */
    public List<MiniInvoiceRecord> selectMiniInvoiceRecordList(MiniInvoiceRecord miniInvoiceRecord);

    /**
     * 新增发票记录管理
     * 
     * @param miniInvoiceRecord 发票记录管理
     * @return 结果
     */
    public int insertMiniInvoiceRecord(MiniInvoiceRecord miniInvoiceRecord);

    /**
     * 修改发票记录管理
     * 
     * @param miniInvoiceRecord 发票记录管理
     * @return 结果
     */
    public int updateMiniInvoiceRecord(MiniInvoiceRecord miniInvoiceRecord);

    /**
     * 删除发票记录管理
     * 
     * @param id 发票记录管理主键
     * @return 结果
     */
    public int deleteMiniInvoiceRecordById(Long id);

    /**
     * 批量删除发票记录管理
     * 
     * @param ids 需要删除的发票记录管理主键集合
     * @return 结果
     */
    public int deleteMiniInvoiceRecordByIds(Long[] ids);
}
