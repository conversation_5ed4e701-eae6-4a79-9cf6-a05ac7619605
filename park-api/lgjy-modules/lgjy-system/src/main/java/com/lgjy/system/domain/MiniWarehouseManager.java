package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 场库管理人员信息对象 mini_warehouse_manager
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MiniWarehouseManager extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 所属运营商ID */
    @Excel(name = "所属运营商ID")
    @NotNull(message = "所属运营商不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /** 所属运营商名称 */
    @Excel(name = "所属运营商")
    private String operatorName;

    /** 负责的场库ID */
    @Excel(name = "负责的场库ID")
    @NotNull(message = "负责的场库不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    /** 负责的场库名称 */
    @Excel(name = "负责的场库")
    private String warehouseName;

    /** 管理人员姓名 */
    @Excel(name = "管理人员姓名")
    @NotBlank(message = "管理人员姓名不能为空")
    private String managerName;

    /** 管理人员电话 */
    @Excel(name = "管理人员电话")
    @NotBlank(message = "管理人员电话不能为空")
    private String managerPhone;



    /** 删除标识 0-未删除 1-已删除 */
    private Integer deleteFlag;

    /** 创建者姓名 */
    @Excel(name = "创建者")
    private String createdBy;

    /** 更新者姓名 */
    @Excel(name = "更新者")
    private String updatedBy;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("operatorId", getOperatorId())
                .append("operatorName", getOperatorName())
                .append("warehouseId", getWarehouseId())
                .append("warehouseName", getWarehouseName())
                .append("managerName", getManagerName())
                .append("managerPhone", getManagerPhone())
                .append("remark", getRemark())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
