package com.lgjy.system.service;

import com.lgjy.system.domain.MiniWarehouse;

import java.util.List;
import java.util.Map;

/**
 * 管理后台首页数据统计Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface IDashboardService {

    /**
     * 获取首页统计数据
     *
     * @param period      时间周期：week-近7天, month-近一月, halfYear-近半年, year-近一年
     * @param warehouseId 场库ID，可选，为空则统计全部
     * @return 统计数据
     */
    Map<String, Object> getDashboardStatistics(String period, Long warehouseId);

    /**
     * 获取全系统统计数据（不受场库选择影响）
     *
     * @param period 时间周期
     * @return 全系统统计数据
     */
    Map<String, Object> getSystemStatistics(String period);

    /**
     * 获取收入趋势数据
     *
     * @param period      时间周期
     * @param warehouseId 场库ID，可选
     * @return 趋势数据
     */
    Map<String, Object> getRevenueTrend(String period, Long warehouseId);

    /**
     * 获取用户增长趋势数据
     *
     * @param period      时间周期
     * @param warehouseId 场库ID，可选
     * @return 用户趋势数据
     */
    Map<String, Object> getUserTrend(String period, Long warehouseId);

    /**
     * 获取场库列表
     *
     * @return 场库列表
     */
    List<MiniWarehouse> getWarehouseList();

    /**
     * 获取指定场库的详细统计
     *
     * @param warehouseId 场库ID
     * @return 场库统计数据
     */
    Map<String, Object> getWarehouseStatistics(Long warehouseId);

    /**
     * 刷新统计数据缓存
     */
    void refreshStatisticsCache();

    /**
     * 获取总场库数量
     *
     * @param warehouseId 场库ID，可选
     * @return 场库数量
     */
    Long getTotalWarehouses(Long warehouseId);

    /**
     * 获取总车位数量
     *
     * @param warehouseId 场库ID，可选
     * @return 车位数量
     */
    Long getTotalParkingSpaces(Long warehouseId);

    /**
     * 获取会员总数量
     *
     * @param warehouseId 场库ID，可选
     * @return 会员数量
     */
    Long getTotalMembers(Long warehouseId);

    /**
     * 获取小程序用户总数
     *
     * @param warehouseId 场库ID，可选
     * @return 用户数量
     */
    Long getTotalAppUsers(Long warehouseId);

    /**
     * 获取总收入
     *
     * @param period      时间周期
     * @param warehouseId 场库ID，可选
     * @return 收入金额
     */
    java.math.BigDecimal getTotalRevenue(String period, Long warehouseId);

    /**
     * 计算增长率
     *
     * @param currentValue  当前值
     * @param previousValue 之前值
     * @return 增长率百分比
     */
    Double calculateGrowthRate(Number currentValue, Number previousValue);
}
