package com.lgjy.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniSpecialUser;
import com.lgjy.system.service.IMiniSpecialUserService;

/**
 * 特殊会员Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/special/user")
public class MiniSpecialUserController extends BaseController {
    @Autowired
    private IMiniSpecialUserService miniSpecialUserService;

    /**
     * 查询特殊会员列表
     */
    @RequiresPermissions("special:user:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniSpecialUser miniSpecialUser) {
        startPage();
        List<MiniSpecialUser> list = miniSpecialUserService.selectMiniSpecialUserList(miniSpecialUser);
        return getDataTable(list);
    }

    /**
     * 导出特殊会员列表
     */
    @RequiresPermissions("special:user:export")
    @Log(title = "特殊会员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniSpecialUser miniSpecialUser) {
        List<MiniSpecialUser> list = miniSpecialUserService.selectMiniSpecialUserList(miniSpecialUser);
        ExcelUtil<MiniSpecialUser> util = new ExcelUtil<MiniSpecialUser>(MiniSpecialUser.class);
        util.exportExcel(response, list, "特殊会员数据");
    }

    /**
     * 获取特殊会员详细信息
     */
    @RequiresPermissions("special:user:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(miniSpecialUserService.selectMiniSpecialUserById(id));
    }

    /**
     * 新增特殊会员
     */
    @RequiresPermissions("special:user:add")
    @Log(title = "特殊会员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniSpecialUser miniSpecialUser) {
        if (!miniSpecialUserService.checkPhoneNumberUnique(miniSpecialUser)) {
            return error("新增特殊会员'" + miniSpecialUser.getPhoneNumber() + "'失败，手机号码已存在");
        }
        if (!miniSpecialUserService.checkPlateNoUnique(miniSpecialUser)) {
            return error("新增特殊会员'" + miniSpecialUser.getPlateNo() + "'失败，车牌号已存在");
        }
        return toAjax(miniSpecialUserService.insertMiniSpecialUser(miniSpecialUser));
    }

    /**
     * 修改特殊会员
     */
    @RequiresPermissions("special:user:edit")
    @Log(title = "特殊会员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniSpecialUser miniSpecialUser) {
        if (!miniSpecialUserService.checkPhoneNumberUnique(miniSpecialUser)) {
            return error("修改特殊会员'" + miniSpecialUser.getPhoneNumber() + "'失败，手机号码已存在");
        }
        if (!miniSpecialUserService.checkPlateNoUnique(miniSpecialUser)) {
            return error("修改特殊会员'" + miniSpecialUser.getPlateNo() + "'失败，车牌号已存在");
        }
        return toAjax(miniSpecialUserService.updateMiniSpecialUser(miniSpecialUser));
    }

    /**
     * 删除特殊会员
     */
    @RequiresPermissions("special:user:remove")
    @Log(title = "特殊会员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniSpecialUserService.deleteMiniSpecialUserByIds(ids));
    }

    /**
     * 统计特殊会员数量按类型
     */
    @GetMapping("/statistics/type")
    public AjaxResult getSpecialUsersStatisticsByType() {
        List<Map<String, Object>> statistics = miniSpecialUserService.countSpecialUsersByType();
        return success(statistics);
    }
}
