package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.SysAgreement;

/**
 * 系统协议Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface SysAgreementMapper 
{
    /**
     * 查询系统协议
     * 
     * @param id 系统协议主键
     * @return 系统协议
     */
    public SysAgreement selectSysAgreementById(Long id);

    /**
     * 查询系统协议列表
     * 
     * @param sysAgreement 系统协议
     * @return 系统协议集合
     */
    public List<SysAgreement> selectSysAgreementList(SysAgreement sysAgreement);

    /**
     * 新增系统协议
     * 
     * @param sysAgreement 系统协议
     * @return 结果
     */
    public int insertSysAgreement(SysAgreement sysAgreement);

    /**
     * 修改系统协议
     * 
     * @param sysAgreement 系统协议
     * @return 结果
     */
    public int updateSysAgreement(SysAgreement sysAgreement);

    /**
     * 删除系统协议
     * 
     * @param id 系统协议主键
     * @return 结果
     */
    public int deleteSysAgreementById(Long id);

    /**
     * 批量删除系统协议
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAgreementByIds(Long[] ids);
}
