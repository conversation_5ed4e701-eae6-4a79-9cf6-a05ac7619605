package com.lgjy.system.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.Constants;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.system.api.RemoteGateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import com.lgjy.system.mapper.MiniBlacklistMapper;
import com.lgjy.system.domain.MiniBlacklist;
import com.lgjy.system.service.IMiniBlacklistService;

/**
 * 黑名单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class MiniBlacklistServiceImpl implements IMiniBlacklistService
{
    private static final Logger logger = LoggerFactory.getLogger(MiniBlacklistServiceImpl.class);

    @Autowired
    private MiniBlacklistMapper miniBlacklistMapper;

    @Autowired
    private RemoteGateService remoteGateService;

    /**
     * 查询黑名单管理
     * 
     * @param id 黑名单管理主键
     * @return 黑名单管理
     */
    @Override
    public MiniBlacklist selectMiniBlacklistById(Long id)
    {
        return miniBlacklistMapper.selectMiniBlacklistById(id);
    }

    /**
     * 查询黑名单管理列表
     * 
     * @param miniBlacklist 黑名单管理
     * @return 黑名单管理
     */
    @Override
    public List<MiniBlacklist> selectMiniBlacklistList(MiniBlacklist miniBlacklist)
    {
        return miniBlacklistMapper.selectMiniBlacklistList(miniBlacklist);
    }

    /**
     * 新增黑名单管理
     * 
     * @param miniBlacklist 黑名单管理
     * @return 结果
     */
    @Override
    public int insertMiniBlacklist(MiniBlacklist miniBlacklist)
    {
        miniBlacklist.setCreateTime(DateUtils.getNowDate());
        miniBlacklist.setDeleteFlag(0);
        return miniBlacklistMapper.insertMiniBlacklist(miniBlacklist);
    }

    /**
     * 修改黑名单管理
     * 
     * @param miniBlacklist 黑名单管理
     * @return 结果
     */
    @Override
    public int updateMiniBlacklist(MiniBlacklist miniBlacklist)
    {
        miniBlacklist.setUpdateTime(DateUtils.getNowDate());
        return miniBlacklistMapper.updateMiniBlacklist(miniBlacklist);
    }

    /**
     * 批量删除黑名单管理
     * 
     * @param ids 需要删除的黑名单管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniBlacklistByIds(Long[] ids)
    {
        return miniBlacklistMapper.deleteMiniBlacklistByIds(ids);
    }

    /**
     * 删除黑名单管理信息
     * 
     * @param id 黑名单管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniBlacklistById(Long id)
    {
        return miniBlacklistMapper.deleteMiniBlacklistById(id);
    }

    /**
     * 根据车牌号查询黑名单信息
     * 
     * @param plateNo 车牌号
     * @return 黑名单信息
     */
    @Override
    public MiniBlacklist selectMiniBlacklistByPlateNo(String plateNo)
    {
        return miniBlacklistMapper.selectMiniBlacklistByPlateNo(plateNo);
    }

    /**
     * 根据车牌号和场库ID查询黑名单信息
     * 
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 黑名单信息
     */
    @Override
    public MiniBlacklist selectMiniBlacklistByPlateNoAndWarehouse(String plateNo, Long warehouseId)
    {
        return miniBlacklistMapper.selectMiniBlacklistByPlateNoAndWarehouse(plateNo, warehouseId);
    }

    /**
     * 校验车牌号是否唯一
     *
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 结果
     */
    @Override
    public String checkPlateNoUnique(String plateNo, Long warehouseId)
    {
        MiniBlacklist blacklist = miniBlacklistMapper.checkPlateNoUnique(plateNo, warehouseId);
        if (StringUtils.isNotNull(blacklist))
        {
            return "1";
        }
        return "0";
    }

    /**
     * 新增黑名单管理（包含推送到道闸系统）
     *
     * @param miniBlacklist 黑名单管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addBlacklistWithPush(MiniBlacklist miniBlacklist) {
        // 参数校验
        Assert.notNull(miniBlacklist, "黑名单对象不能为空");
        Assert.notNull(miniBlacklist.getWarehouseId(), "场库ID不能为空");
        Assert.hasText(miniBlacklist.getPlateNo(), "车牌号不能为空");
        Assert.notNull(miniBlacklist.getBeginTime(), "开始时间不能为空");
        Assert.notNull(miniBlacklist.getEndTime(), "结束时间不能为空");

        // 检验是否存在（查询单条数据）
        MiniBlacklist existingBlacklist = selectMiniBlacklistByPlateNoAndWarehouse(
                miniBlacklist.getPlateNo(), miniBlacklist.getWarehouseId());
        if (existingBlacklist != null) {
            throw new ServiceException("该车牌号在当前场库已存在黑名单记录");
        }

        // 先保存到本地数据库
        int insertResult = insertMiniBlacklist(miniBlacklist);
        if (insertResult <= 0) {
            throw new ServiceException("保存黑名单记录失败");
        }

        // 再往远程推送
        try {
            JSONObject requestData = new JSONObject();
            requestData.put("parkingId", miniBlacklist.getWarehouseId().toString());
            requestData.put("plateNum", miniBlacklist.getPlateNo());
            requestData.put("endDate", miniBlacklist.getEndTime());
            R<String> pushResult = remoteGateService.saveBlackCar(requestData, SecurityConstants.INNER);

            if (pushResult.getCode() != Constants.SUCCESS) {
                throw new ServiceException("推送到道闸系统失败：" + pushResult.getMsg());
            }

            return insertResult;

        } catch (Exception e) {
            logger.error("推送黑名单车辆到道闸系统异常", e);
            throw new ServiceException("推送到道闸系统失败：" + e.getMessage());
        }
    }

    /**
     * 删除黑名单管理（包含从道闸系统删除）
     *
     * @param id 需要删除的黑名单管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeBlacklistWithPush(Long id) {
        Assert.notNull(id, "删除ID不能为空");

        // 1. 获取黑名单记录
        MiniBlacklist blacklist = selectMiniBlacklistById(id);
        if (blacklist == null) {
            throw new ServiceException("黑名单记录不存在，ID: " + id);
        }

        // 2. 先删除本地数据库记录
        int deleteResult = deleteMiniBlacklistById(id);
        if (deleteResult <= 0) {
            throw new ServiceException("删除本地黑名单记录失败，ID: " + id);
        }
        logger.info("成功删除本地黑名单记录，ID: {}, 车牌号: {}", id, blacklist.getPlateNo());

        // 3. 再删除远程道闸系统
        try {
            String warehouseIdStr = blacklist.getWarehouseId().toString();
            if (StringUtils.isNotEmpty(warehouseIdStr)&& blacklist.getPlateNo() != null) {
                JSONObject requestData = new JSONObject();
                requestData.put("parkingId", warehouseIdStr);
                requestData.put("plateNum", blacklist.getPlateNo());

                R<String> delResult = remoteGateService.delBlackCar(requestData, SecurityConstants.INNER);
                if (delResult.getCode() != Constants.SUCCESS) {
                    throw new ServiceException("从道闸系统删除黑名单失败：" + delResult.getMsg());
                }
                logger.info("成功从道闸系统删除黑名单，车牌号: {}, 场库ID: {}", blacklist.getPlateNo(), warehouseIdStr);
            }
        } catch (Exception e) {
            logger.error("从道闸系统删除黑名单失败，ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new ServiceException("从道闸系统删除黑名单失败：" + e.getMessage());
        }

        return deleteResult;
    }
}
