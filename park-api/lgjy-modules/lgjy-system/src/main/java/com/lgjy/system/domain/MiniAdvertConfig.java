package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 广告配置信息对象 mini_advert_config
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MiniAdvertConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 广告标题 */
    @Excel(name = "广告标题")
    @NotBlank(message = "广告标题不能为空")
    private String advertTitle;

    /** 跳转链接 */
    @Excel(name = "跳转链接")
    private String url;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String picUrl;

    /** 状态：1-生效，2-失效 */
    @Excel(name = "状态", readConverterExp = "1=生效,2=失效")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /** 删除标记：0-正常，1-已删除 */
    private Integer deleteFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("remark", getRemark())
                .append("advertTitle", getAdvertTitle())
                .append("url", getUrl())
                .append("picUrl", getPicUrl())
                .append("status", getStatus())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
