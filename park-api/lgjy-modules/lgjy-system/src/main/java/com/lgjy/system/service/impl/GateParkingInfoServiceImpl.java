package com.lgjy.system.service.impl;

import java.util.List;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.GateParkingInfoMapper;
import com.lgjy.system.domain.GateParkingInfo;
import com.lgjy.system.service.IGateParkingInfoService;

/**
 * 车辆出入场记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class GateParkingInfoServiceImpl implements IGateParkingInfoService {
    @Autowired
    private GateParkingInfoMapper gateParkingInfoMapper;

    /**
     * 查询车辆出入场记录
     *
     * @param id 车辆出入场记录主键
     * @return 车辆出入场记录
     */
    @Override
    public GateParkingInfo selectGateParkingInfoById(String id) {
        return gateParkingInfoMapper.selectGateParkingInfoById(id);
    }

    /**
     * 查询车辆出入场记录列表
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 车辆出入场记录
     */
    @Override
    public List<GateParkingInfo> selectGateParkingInfoList(GateParkingInfo gateParkingInfo) {
        return gateParkingInfoMapper.selectGateParkingInfoList(gateParkingInfo);
    }

    /**
     * 新增车辆出入场记录
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 结果
     */
    @Override
    public int insertGateParkingInfo(GateParkingInfo gateParkingInfo) {
        gateParkingInfo.setCreateTime(DateUtils.getNowDate());
        return gateParkingInfoMapper.insertGateParkingInfo(gateParkingInfo);
    }

    /**
     * 修改车辆出入场记录
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 结果
     */
    @Override
    public int updateGateParkingInfo(GateParkingInfo gateParkingInfo) {
        gateParkingInfo.setUpdateTime(DateUtils.getNowDate());
        return gateParkingInfoMapper.updateGateParkingInfo(gateParkingInfo);
    }

    /**
     * 批量删除车辆出入场记录
     *
     * @param ids 需要删除的车辆出入场记录主键
     * @return 结果
     */
    @Override
    public int deleteGateParkingInfoByIds(String[] ids) {
        return gateParkingInfoMapper.deleteGateParkingInfoByIds(ids);
    }

    /**
     * 删除车辆出入场记录信息
     *
     * @param id 车辆出入场记录主键
     * @return 结果
     */
    @Override
    public int deleteGateParkingInfoById(String id) {
        return gateParkingInfoMapper.deleteGateParkingInfoById(id);
    }

    /**
     * 根据车牌号查询车辆出入场记录
     *
     * @param plateNum 车牌号
     * @return 车辆出入场记录集合
     */
    @Override
    public List<GateParkingInfo> selectGateParkingInfoByPlateNum(String plateNum) {
        return gateParkingInfoMapper.selectGateParkingInfoByPlateNum(plateNum);
    }

    /**
     * 根据停车场ID查询车辆出入场记录
     *
     * @param parkingId 停车场ID
     * @return 车辆出入场记录集合
     */
    @Override
    public List<GateParkingInfo> selectGateParkingInfoByParkingId(String parkingId) {
        return gateParkingInfoMapper.selectGateParkingInfoByParkingId(parkingId);
    }

    /**
     * 根据状态查询车辆出入场记录
     *
     * @param status 状态
     * @return 车辆出入场记录集合
     */
    @Override
    public List<GateParkingInfo> selectGateParkingInfoByStatus(Integer status) {
        return gateParkingInfoMapper.selectGateParkingInfoByStatus(status);
    }

    /**
     * 统计车辆出入场记录数量
     *
     * @param gateParkingInfo 车辆出入场记录
     * @return 结果
     */
    @Override
    public int countGateParkingInfo(GateParkingInfo gateParkingInfo) {
        return gateParkingInfoMapper.countGateParkingInfo(gateParkingInfo);
    }

    /**
     * 获取车辆类型选项
     *
     * @return 车辆类型集合
     */
    @Override
    public List<String> selectCarTypeOptions() {
        return gateParkingInfoMapper.selectCarTypeOptions();
    }

    /**
     * 获取支付类型选项
     *
     * @return 支付类型集合
     */
    @Override
    public List<String> selectPayTypeOptions() {
        return gateParkingInfoMapper.selectPayTypeOptions();
    }

    /**
     * 获取通道名称选项
     *
     * @return 通道名称集合
     */
    @Override
    public List<String> selectChannelNameOptions() {
        return gateParkingInfoMapper.selectChannelNameOptions();
    }
}
