package com.lgjy.system.service;

import com.lgjy.system.domain.ErrorDataLog;

import java.util.List;
import java.util.Map;

/**
 * 错误数据日志Service接口
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
public interface IErrorDataLogService {
    /**
     * 查询错误数据日志
     *
     * @param id 错误数据日志主键
     * @return 错误数据日志
     */
    public ErrorDataLog selectErrorDataLogById(String id);

    /**
     * 查询错误数据日志列表
     *
     * @param errorDataLog 错误数据日志
     * @return 错误数据日志集合
     */
    public List<ErrorDataLog> selectErrorDataLogList(ErrorDataLog errorDataLog);

    /**
     * 新增错误数据日志
     *
     * @param errorDataLog 错误数据日志
     * @return 结果
     */
    public int insertErrorDataLog(ErrorDataLog errorDataLog);

    /**
     * 修改错误数据日志
     *
     * @param errorDataLog 错误数据日志
     * @return 结果
     */
    public int updateErrorDataLog(ErrorDataLog errorDataLog);

    /**
     * 批量删除错误数据日志
     *
     * @param ids 需要删除的错误数据日志主键集合
     * @return 结果
     */
    public int deleteErrorDataLogByIds(String[] ids);

    /**
     * 删除错误数据日志信息
     *
     * @param id 错误数据日志主键
     * @return 结果
     */
    public int deleteErrorDataLogById(String id);

    /**
     * 根据车牌号查询错误数据日志列表
     *
     * @param plateNum 车牌号
     * @return 错误数据日志集合
     */
    public List<ErrorDataLog> selectErrorDataLogByPlateNum(String plateNum);

    /**
     * 根据场库ID查询错误数据日志列表
     *
     * @param parkingId 场库ID
     * @return 错误数据日志集合
     */
    public List<ErrorDataLog> selectErrorDataLogByParkingId(String parkingId);

    /**
     * 根据错误码查询错误数据日志列表
     *
     * @param errCode 错误码
     * @return 错误数据日志集合
     */
    public List<ErrorDataLog> selectErrorDataLogByErrCode(Integer errCode);

    /**
     * 统计错误数据日志数量
     *
     * @param errorDataLog 查询条件
     * @return 统计结果
     */
    public int countErrorDataLog(ErrorDataLog errorDataLog);

    /**
     * 统计错误数据日志按错误码分组
     *
     * @param errorDataLog 查询条件
     * @return 统计结果
     */
    public List<Map<String, Object>> countErrorDataLogByErrCode(ErrorDataLog errorDataLog);

    /**
     * 统计错误数据日志按场库分组
     *
     * @param errorDataLog 查询条件
     * @return 统计结果
     */
    public List<Map<String, Object>> countErrorDataLogByParking(ErrorDataLog errorDataLog);
}
