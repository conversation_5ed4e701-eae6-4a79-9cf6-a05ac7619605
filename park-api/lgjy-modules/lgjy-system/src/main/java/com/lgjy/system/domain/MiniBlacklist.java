package com.lgjy.system.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;

/**
 * 黑名单管理对象 mini_blacklist
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class MiniBlacklist extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 车牌号码 */
    @Excel(name = "车牌号码")
    private String plateNo;

    /** 黑名单生效起始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /** 黑名单失效截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "失效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 关联的场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    /** 车主姓名 */
    @Excel(name = "车主姓名")
    private String name;

    /** 车主联系电话 */
    @Excel(name = "联系电话")
    private String phoneNumber;

    /** 关联证据图片URL */
    @Excel(name = "证据图片")
    private String imgUrl;

    /** 软删除标记（0=正常，1=已删除） */
    private Integer deleteFlag;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 运营商名称 */
    @Excel(name = "运营商名称")
    private String operatorName;

}
