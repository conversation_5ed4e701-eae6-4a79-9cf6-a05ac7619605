package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.lgjy.system.api.domain.WxUser;
import com.lgjy.system.domain.WxUserCar;
import com.lgjy.system.service.IWxUserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.security.annotation.RequiresPermissions;

/**
 * 小程序用户Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/owner/wxuser")
public class WxUserController extends BaseController {
    @Autowired
    private IWxUserService wxUserService;

    /**
     * 查询小程序用户列表
     */
    @RequiresPermissions("owner:wxuser:list")
    @GetMapping("/list")
    public TableDataInfo list(WxUser wxUser) {
        startPage();
        List<WxUser> list = wxUserService.selectWxUserList(wxUser);
        return getDataTable(list);
    }

    /**
     * 导出小程序用户列表
     */
    @RequiresPermissions("owner:wxuser:export")
    @Log(title = "小程序用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WxUser wxUser) {
        List<WxUser> list = wxUserService.selectWxUserList(wxUser);
        ExcelUtil<WxUser> util = new ExcelUtil<WxUser>(WxUser.class);
        util.exportExcel(response, list, "小程序用户数据");
    }

    /**
     * 获取小程序用户详细信息
     */
    @RequiresPermissions("owner:wxuser:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wxUserService.selectWxUserById(id));
    }

    /**
     * 新增小程序用户
     */
    @RequiresPermissions("owner:wxuser:add")
    @Log(title = "小程序用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WxUser wxUser) {
        return toAjax(wxUserService.insertWxUser(wxUser));
    }

    /**
     * 修改小程序用户
     */
    @RequiresPermissions("owner:wxuser:edit")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WxUser wxUser) {
        return toAjax(wxUserService.updateWxUser(wxUser));
    }

    /**
     * 删除小程序用户
     */
    @RequiresPermissions("owner:wxuser:remove")
    @Log(title = "小程序用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wxUserService.deleteWxUserByIds(ids));
    }

    /**
     * 修改用户状态
     */
    @RequiresPermissions("owner:wxuser:edit")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody WxUser wxUser) {
        try {
            // 记录请求参数
            System.out.println("changeStatus请求参数: id=" + wxUser.getId() + ", status=" + wxUser.getStatus());

            // 验证用户是否存在
            WxUser existingUser = wxUserService.selectWxUserById(wxUser.getId());
            if (existingUser == null) {
                System.err.println("用户不存在: id=" + wxUser.getId());
                return AjaxResult.error("用户不存在");
            }

            System.out.println("找到用户: " + existingUser.getUserName() + ", 当前状态: " + existingUser.getStatus());

            int result = wxUserService.updateWxUserStatus(wxUser);
            System.out.println("updateWxUserStatus执行结果: " + result);

            return toAjax(result);
        } catch (Exception e) {
            System.err.println("changeStatus异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("状态修改失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用用户
     */
    @RequiresPermissions("owner:wxuser:edit")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping("/batchEnable/{ids}")
    public AjaxResult batchEnable(@PathVariable Long[] ids) {
        return toAjax(wxUserService.batchUpdateStatus(ids, 0));
    }

    /**
     * 批量停用用户
     */
    @RequiresPermissions("owner:wxuser:edit")
    @Log(title = "小程序用户", businessType = BusinessType.UPDATE)
    @PutMapping("/batchDisable/{ids}")
    public AjaxResult batchDisable(@PathVariable Long[] ids) {
        return toAjax(wxUserService.batchUpdateStatus(ids, 1));
    }

    /**
     * 查询用户车辆信息
     */
    @RequiresPermissions("owner:wxuser:car")
    @GetMapping("/cars/{userId}")
    public AjaxResult getUserCars(@PathVariable("userId") Long userId) {
        List<WxUserCar> cars = wxUserService.selectWxUserCarsByUserId(userId);
        return success(cars);
    }
}
