package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.MiniVipMember;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.domain.MiniVipPackage;

/**
 * 会员信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniVipMemberService {
    /**
     * 查询会员信息
     * 
     * @param id 会员信息主键
     * @return 会员信息
     */
    public MiniVipMember selectMiniVipMemberById(Long id);

    /**
     * 查询会员信息列表
     * 
     * @param miniVipMember 会员信息
     * @return 会员信息集合
     */
    public List<MiniVipMember> selectMiniVipMemberList(MiniVipMember miniVipMember);

    /**
     * 新增会员信息
     * 
     * @param miniVipMember 会员信息
     * @return 结果
     */
    public int insertMiniVipMember(MiniVipMember miniVipMember);

    /**
     * 修改会员信息
     * 
     * @param miniVipMember 会员信息
     * @return 结果
     */
    public int updateMiniVipMember(MiniVipMember miniVipMember);

    /**
     * 批量删除会员信息
     * 
     * @param ids 需要删除的会员信息主键集合
     * @return 结果
     */
    public int deleteMiniVipMemberByIds(Long[] ids);

    /**
     * 删除会员信息
     * 
     * @param id 会员信息主键
     * @return 结果
     */
    public int deleteMiniVipMemberById(Long id);

    /**
     * 根据手机号查询会员信息
     *
     * @param phoneNumber 手机号
     * @return 会员信息
     */
    public MiniVipMember selectMemberByPhoneNumber(String phoneNumber);

    /**
     * 根据车牌号和场库ID查询会员信息
     *
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 会员信息
     */
    public MiniVipMember selectMemberByPlateNoAndWarehouse(String plateNo, Long warehouseId);

    /**
     * 根据场库ID查询会员列表
     *
     * @param warehouseId 场库ID
     * @return 会员列表
     */
    public List<MiniVipMember> selectMembersByWarehouseId(Long warehouseId);

    /**
     * 查询即将到期的会员列表
     *
     * @param days 天数
     * @return 会员列表
     */
    public List<MiniVipMember> selectExpiringMembers(Integer days);

    /**
     * 获取所有子场库选项
     *
     * @return 子场库列表
     */
    public List<MiniWarehouse> selectChildWarehouseOptions();

    /**
     * 根据父场库ID获取子场库选项
     *
     * @param parentId 父场库ID
     * @return 子场库列表
     */
    public List<MiniWarehouse> selectChildWarehouseOptionsByParentId(Long parentId);

    /**
     * 根据停车场ID获取套餐选项
     *
     * @param parkingLotId 停车场ID
     * @return 套餐列表
     */
    public List<MiniVipPackage> getPackageOptionsByParkingLotId(Long parkingLotId);

    /**
     * 获取所有场库选项（包含层级关系）
     *
     * @return 场库列表
     */
    public List<MiniWarehouse> selectAllWarehouseOptions();

}
