package com.lgjy.system.mapper;

import com.lgjy.system.domain.MiniWarehouse;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 管理后台首页数据统计Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface DashboardMapper {

        /**
         * 获取总场库数量
         *
         * @param warehouseId 场库ID，可选
         * @return 场库数量
         */
        Long getTotalWarehouses(@Param("warehouseId") Long warehouseId);

        /**
         * 获取总车位数量
         *
         * @param warehouseId 场库ID，可选
         * @return 车位数量
         */
        Long getTotalParkingSpaces(@Param("warehouseId") Long warehouseId);

        /**
         * 获取会员总数量
         *
         * @param warehouseId 场库ID，可选
         * @return 会员数量
         */
        Long getTotalMembers(@Param("warehouseId") Long warehouseId);

        /**
         * 获取小程序用户总数
         *
         * @param warehouseId 场库ID，可选
         * @return 用户数量
         */
        Long getTotalAppUsers(@Param("warehouseId") Long warehouseId);

        /**
         * 获取总收入
         *
         * @param startDate   开始时间
         * @param endDate     结束时间
         * @param warehouseId 场库ID，可选
         * @return 收入金额
         */
        BigDecimal getTotalRevenue(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);

        /**
         * 获取收入趋势数据
         *
         * @param startDate   开始时间
         * @param endDate     结束时间
         * @param warehouseId 场库ID，可选
         * @param groupBy     分组字段
         * @return 趋势数据
         */
        List<Map<String, Object>> getRevenueTrendData(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId,
                        @Param("groupBy") String groupBy);

        /**
         * 获取会员增长趋势数据
         *
         * @param startDate   开始时间
         * @param endDate     结束时间
         * @param warehouseId 场库ID，可选
         * @param groupBy     分组字段
         * @return 会员趋势数据
         */
        List<Map<String, Object>> getMemberTrendData(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId,
                        @Param("groupBy") String groupBy);

        /**
         * 获取小程序用户增长趋势数据
         *
         * @param startDate   开始时间
         * @param endDate     结束时间
         * @param warehouseId 场库ID，可选
         * @param groupBy     分组字段
         * @return 用户趋势数据
         */
        List<Map<String, Object>> getAppUserTrendData(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId,
                        @Param("groupBy") String groupBy);

        /**
         * 获取场库列表
         *
         * @return 场库列表
         */
        List<MiniWarehouse> getWarehouseList();

        /**
         * 按日期获取收入趋势数据（真实数据）
         *
         * @param startDate   开始日期
         * @param endDate     结束日期
         * @param warehouseId 场库ID，可选
         * @return 收入趋势数据
         */
        List<Map<String, Object>> getRevenueTrendByDate(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);

        /**
         * 按日期获取会员增长数据（真实数据）
         *
         * @param startDate   开始日期
         * @param endDate     结束日期
         * @param warehouseId 场库ID，可选
         * @return 会员增长数据
         */
        List<Map<String, Object>> getMemberGrowthByDate(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);

        /**
         * 按日期获取小程序用户增长数据（真实数据）
         *
         * @param startDate   开始日期
         * @param endDate     结束日期
         * @param warehouseId 场库ID，可选
         * @return 用户增长数据
         */
        List<Map<String, Object>> getAppUserGrowthByDate(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);

        /**
         * 获取历史会员数量（用于计算增长率）
         *
         * @param endDate     截止日期
         * @param warehouseId 场库ID，可选
         * @return 历史会员数量
         */
        Long getTotalMembersHistorical(@Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);

        /**
         * 获取历史小程序用户数量（用于计算增长率）
         *
         * @param endDate     截止日期
         * @param warehouseId 场库ID，可选
         * @return 历史用户数量
         */
        Long getTotalAppUsersHistorical(@Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);

        /**
         * 获取今日新增订单数量
         *
         * @param warehouseId 场库ID，可选
         * @return 订单数量
         */
        Long getTodayNewOrders(@Param("warehouseId") Long warehouseId);

        /**
         * 获取本月新增订单数量
         *
         * @param warehouseId 场库ID，可选
         * @return 订单数量
         */
        Long getMonthNewOrders(@Param("warehouseId") Long warehouseId);

        /**
         * 获取今日新增用户数量
         *
         * @param warehouseId 场库ID，可选
         * @return 用户数量
         */
        Long getTodayNewUsers(@Param("warehouseId") Long warehouseId);

        /**
         * 获取本月新增用户数量
         *
         * @param warehouseId 场库ID，可选
         * @return 用户数量
         */
        Long getMonthNewUsers(@Param("warehouseId") Long warehouseId);

        /**
         * 获取订单状态统计
         *
         * @param warehouseId 场库ID，可选
         * @return 订单状态统计
         */
        List<Map<String, Object>> getOrderStatusStats(@Param("warehouseId") Long warehouseId);

        /**
         * 批量获取基础统计数据 - 性能优化
         *
         * @param warehouseId 场库ID，可选
         * @return 统计数据列表，包含stat_type和stat_value字段
         */
        List<Map<String, Object>> getBatchStatistics(@Param("warehouseId") Long warehouseId);

        /**
         * 获取支付方式统计
         *
         * @param startDate   开始时间
         * @param endDate     结束时间
         * @param warehouseId 场库ID，可选
         * @return 支付方式统计
         */
        List<Map<String, Object>> getPaymentMethodStats(@Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("warehouseId") Long warehouseId);
}
