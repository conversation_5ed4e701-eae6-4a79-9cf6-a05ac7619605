package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.MiniParkingOrder;

/**
 * 停车订单Service接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IMiniParkingOrderService {
    /**
     * 查询停车订单
     *
     * @param id 停车订单主键
     * @return 停车订单
     */
    public MiniParkingOrder selectMiniParkingOrderById(Long id);

    /**
     * 查询停车订单列表
     *
     * @param miniParkingOrder 停车订单
     * @return 停车订单集合
     */
    public List<MiniParkingOrder> selectMiniParkingOrderList(MiniParkingOrder miniParkingOrder);

    /**
     * 新增停车订单
     *
     * @param miniParkingOrder 停车订单
     * @return 结果
     */
    public int insertMiniParkingOrder(MiniParkingOrder miniParkingOrder);

    /**
     * 修改停车订单
     *
     * @param miniParkingOrder 停车订单
     * @return 结果
     */
    public int updateMiniParkingOrder(MiniParkingOrder miniParkingOrder);

    /**
     * 批量删除停车订单
     *
     * @param ids 需要删除的停车订单主键集合
     * @return 结果
     */
    public int deleteMiniParkingOrderByIds(Long[] ids);

    /**
     * 删除停车订单信息
     *
     * @param id 停车订单主键
     * @return 结果
     */
    public int deleteMiniParkingOrderById(Long id);

    /**
     * 根据车牌号查询停车订单
     *
     * @param plateNo 车牌号
     * @return 停车订单列表
     */
    public List<MiniParkingOrder> selectMiniParkingOrderByPlateNo(String plateNo);

    /**
     * 根据场库ID查询停车订单
     *
     * @param warehouseId 场库ID
     * @return 停车订单列表
     */
    public List<MiniParkingOrder> selectMiniParkingOrderByWarehouseId(Long warehouseId);

    /**
     * 根据支付状态查询停车订单
     *
     * @param payStatus 支付状态
     * @return 停车订单列表
     */
    public List<MiniParkingOrder> selectMiniParkingOrderByPayStatus(Integer payStatus);

    /**
     * 统计订单数量
     *
     * @param miniParkingOrder 查询条件
     * @return 订单数量
     */
    public int countMiniParkingOrder(MiniParkingOrder miniParkingOrder);

    /**
     * 统计订单金额
     *
     * @param miniParkingOrder 查询条件
     * @return 订单金额统计
     */
    public java.math.BigDecimal sumMiniParkingOrderAmount(MiniParkingOrder miniParkingOrder);

    /**
     * 导出停车订单数据
     *
     * @param miniParkingOrder 停车订单
     * @return 停车订单集合
     */
    public List<MiniParkingOrder> selectMiniParkingOrderListForExport(MiniParkingOrder miniParkingOrder);

    /**
     * 获取数据库中现有的车辆类型选项
     *
     * @return 车辆类型列表
     */
    public List<String> selectCarTypeOptions();

    /**
     * 根据车牌号和场库ID查询出入场记录
     *
     * @param plateNo     车牌号
     * @param warehouseId 场库ID
     * @return 出入场记录列表
     */
    public List<java.util.Map<String, Object>> selectGateParkingInfo(String plateNo, Long warehouseId);

    /**
     * 根据场库ID、车牌号和停车开始时间查询出入场记录
     *
     * @param warehouseId      场库ID
     * @param plateNo          车牌号
     * @param beginParkingTime 停车开始时间
     * @return 出入场记录
     */
    public java.util.Map<String, Object> selectGateParkingInfoByOrderInfo(Long warehouseId, String plateNo,
            java.util.Date beginParkingTime);

    /**
     * 根据场库ID、车牌号和时间戳查询出入场记录
     *
     * @param warehouseId 场库ID
     * @param plateNo     车牌号
     * @param timestamp   时间戳字符串
     * @return 出入场记录
     */
    public java.util.Map<String, Object> selectGateParkingInfoByTimestamp(Long warehouseId, String plateNo,
            String timestamp);

    /**
     * 停车订单退款
     *
     * @param miniParkingOrder 停车订单
     * @return 结果
     */
    public int refundParkingOrder(MiniParkingOrder miniParkingOrder);
}
