package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniVipMember;

/**
 * 会员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MiniVipMemberMapper {
    /**
     * 查询会员信息
     * 
     * @param id 会员信息主键
     * @return 会员信息
     */
    public MiniVipMember selectMiniVipMemberById(Long id);

    /**
     * 查询会员信息列表
     * 
     * @param miniVipMember 会员信息
     * @return 会员信息集合
     */
    public List<MiniVipMember> selectMiniVipMemberList(MiniVipMember miniVipMember);

    /**
     * 新增会员信息
     * 
     * @param miniVipMember 会员信息
     * @return 结果
     */
    public int insertMiniVipMember(MiniVipMember miniVipMember);

    /**
     * 修改会员信息
     * 
     * @param miniVipMember 会员信息
     * @return 结果
     */
    public int updateMiniVipMember(MiniVipMember miniVipMember);

    /**
     * 删除会员信息
     * 
     * @param id 会员信息主键
     * @return 结果
     */
    public int deleteMiniVipMemberById(Long id);

    /**
     * 批量删除会员信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniVipMemberByIds(Long[] ids);

    /**
     * 校验会员编号是否唯一
     * 
     * @param memberNo 会员编号
     * @return 结果
     */
    public MiniVipMember checkMemberNoUnique(String memberNo);

    /**
     * 根据手机号查询会员信息
     *
     * @param phoneNumber 手机号
     * @return 会员信息
     */
    public MiniVipMember selectMemberByPhoneNumber(String phoneNumber);

    /**
     * 根据车牌号和场库ID查询会员信息
     *
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 会员信息
     */
    public MiniVipMember selectMemberByPlateNoAndWarehouse(String plateNo, Long warehouseId);

    /**
     * 根据场库ID查询会员列表
     * 
     * @param warehouseId 场库ID
     * @return 会员列表
     */
    public List<MiniVipMember> selectMembersByWarehouseId(Long warehouseId);

    /**
     * 查询即将到期的会员列表
     * 
     * @param days 天数
     * @return 会员列表
     */
    public List<MiniVipMember> selectExpiringMembers(Integer days);

    /**
     * 统计会员数量按等级
     * 
     * @param warehouseId 场库ID
     * @return 统计结果
     */
    public List<MiniVipMember> countMembersByLevel(Long warehouseId);

    /**
     * 统计会员数量按状态
     * 
     * @param warehouseId 场库ID
     * @return 统计结果
     */
    public List<MiniVipMember> countMembersByStatus(Long warehouseId);
}
