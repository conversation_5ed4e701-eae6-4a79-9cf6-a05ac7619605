package com.lgjy.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.lgjy.system.domain.MiniUnionPayConfig;

/**
 * 银联配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface MiniUnionPayConfigMapper {
    /**
     * 查询银联配置
     *
     * @param id 银联配置主键
     * @return 银联配置
     */
    public MiniUnionPayConfig selectMiniUnionPayConfigById(Long id);

    /**
     * 查询银联配置列表
     *
     * @param miniUnionPayConfig 银联配置
     * @return 银联配置集合
     */
    public List<MiniUnionPayConfig> selectMiniUnionPayConfigList(MiniUnionPayConfig miniUnionPayConfig);

    /**
     * 新增银联配置
     *
     * @param miniUnionPayConfig 银联配置
     * @return 结果
     */
    public int insertMiniUnionPayConfig(MiniUnionPayConfig miniUnionPayConfig);

    /**
     * 修改银联配置
     *
     * @param miniUnionPayConfig 银联配置
     * @return 结果
     */
    public int updateMiniUnionPayConfig(MiniUnionPayConfig miniUnionPayConfig);

    /**
     * 删除银联配置
     *
     * @param id 银联配置主键
     * @return 结果
     */
    public int deleteMiniUnionPayConfigById(Long id);

    /**
     * 批量删除银联配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniUnionPayConfigByIds(Long[] ids);

    /**
     * 检查商户号是否唯一
     *
     * @param mid 商户号
     * @param id  银联配置ID
     * @return 结果
     */
    public int checkMidUnique(@Param("mid") String mid, @Param("id") Long id);

    /**
     * 检查终端号是否唯一
     *
     * @param tid 终端号
     * @param id  银联配置ID
     * @return 结果
     */
    public int checkTidUnique(@Param("tid") String tid, @Param("id") Long id);

    /**
     * 根据场库ID查询银联配置
     *
     * @param warehouseId 场库ID
     * @return 银联配置
     */
    public MiniUnionPayConfig selectMiniUnionPayConfigByWarehouseId(Long warehouseId);
}
