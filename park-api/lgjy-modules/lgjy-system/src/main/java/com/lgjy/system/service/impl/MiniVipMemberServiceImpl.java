package com.lgjy.system.service.impl;

import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.system.mapper.MiniVipMemberMapper;
import com.lgjy.system.mapper.MiniWarehouseMapper;
import com.lgjy.system.mapper.MiniVipPackageMapper;
import com.lgjy.system.domain.MiniVipMember;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.domain.MiniVipPackage;
import com.lgjy.system.service.IMiniVipMemberService;
import com.lgjy.common.enums.SpecialUserPackageEnum;

/**
 * 会员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniVipMemberServiceImpl implements IMiniVipMemberService {
    @Autowired
    private MiniVipMemberMapper miniVipMemberMapper;

    @Autowired
    private MiniWarehouseMapper miniWarehouseMapper;

    @Autowired
    private MiniVipPackageMapper miniVipPackageMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询会员信息
     *
     * @param id 会员信息主键
     * @return 会员信息
     */
    @Override
    public MiniVipMember selectMiniVipMemberById(Long id) {
        MiniVipMember vipMember = miniVipMemberMapper.selectMiniVipMemberById(id);
        if (vipMember != null) {
            // 填充场库名称
            if (vipMember.getWarehouseId() != null) {
                MiniWarehouse warehouse = miniWarehouseMapper
                        .selectMiniWarehouseById(vipMember.getWarehouseId());
                if (warehouse != null) {
                    vipMember.setWarehouseName(warehouse.getWarehouseName());
                }
            }
            // 根据会员类型填充套餐名称
            fillPackageNameByVipType(vipMember);
        }
        return vipMember;
    }

    /**
     * 查询会员信息列表
     *
     * @param miniVipMember 会员信息
     * @return 会员信息
     */
    @Override
    public List<MiniVipMember> selectMiniVipMemberList(MiniVipMember miniVipMember) {
        List<MiniVipMember> list = miniVipMemberMapper.selectMiniVipMemberList(miniVipMember);
        // 填充关联信息
        for (MiniVipMember vipMember : list) {
            // 填充场库名称
            if (vipMember.getWarehouseId() != null) {
                MiniWarehouse warehouse = miniWarehouseMapper
                        .selectMiniWarehouseById(vipMember.getWarehouseId());
                if (warehouse != null) {
                    vipMember.setWarehouseName(warehouse.getWarehouseName());
                }
            }
            // 根据会员类型填充套餐名称
            fillPackageNameByVipType(vipMember);
        }
        return list;
    }

    /**
     * 根据会员类型填充套餐名称
     *
     * @param vipMember 会员信息
     */
    private void fillPackageNameByVipType(MiniVipMember vipMember) {
        if (vipMember == null || vipMember.getVipType() == null) {
            return;
        }

        String packageName;
        switch (vipMember.getVipType()) {
            case 0:
                packageName = "普通会员套餐";
                break;
            case 1:
                packageName = "集团客户套餐";
                break;
            case 2:
                packageName = "VIP客户套餐";
                break;
            default:
                packageName = "未知套餐类型";
                break;
        }
        vipMember.setPackageName(packageName);
    }

    /**
     * 新增会员信息
     *
     * @param miniVipMember 会员信息
     * @return 结果
     */
    @Override
    public int insertMiniVipMember(MiniVipMember miniVipMember) {
        // 使用雪花算法生成ID
        miniVipMember.setId(snowflakeIdGenerator.nextId());
        miniVipMember.setDeleteFlag(0);
        return miniVipMemberMapper.insertMiniVipMember(miniVipMember);
    }

    /**
     * 修改会员信息
     *
     * @param miniVipMember 会员信息
     * @return 结果
     */
    @Override
    public int updateMiniVipMember(MiniVipMember miniVipMember) {
        return miniVipMemberMapper.updateMiniVipMember(miniVipMember);
    }

    /**
     * 批量删除会员信息
     *
     * @param ids 需要删除的会员信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniVipMemberByIds(Long[] ids) {
        return miniVipMemberMapper.deleteMiniVipMemberByIds(ids);
    }

    /**
     * 删除会员信息
     *
     * @param id 会员信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniVipMemberById(Long id) {
        return miniVipMemberMapper.deleteMiniVipMemberById(id);
    }

    /**
     * 根据手机号查询会员信息
     *
     * @param phoneNumber 手机号
     * @return 会员信息
     */
    @Override
    public MiniVipMember selectMemberByPhoneNumber(String phoneNumber) {
        return miniVipMemberMapper.selectMemberByPhoneNumber(phoneNumber);
    }

    /**
     * 根据车牌号和场库ID查询会员信息
     *
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 会员信息
     */
    @Override
    public MiniVipMember selectMemberByPlateNoAndWarehouse(String plateNo, Long warehouseId) {
        return miniVipMemberMapper.selectMemberByPlateNoAndWarehouse(plateNo, warehouseId);
    }

    /**
     * 根据停车场ID查询会员列表
     *
     * @param warehouseId 停车场ID
     * @return 会员列表
     */
    @Override
    public List<MiniVipMember> selectMembersByWarehouseId(Long warehouseId) {
        return miniVipMemberMapper.selectMembersByWarehouseId(warehouseId);
    }

    /**
     * 查询即将到期的会员列表
     *
     * @param days 天数
     * @return 会员列表
     */
    @Override
    public List<MiniVipMember> selectExpiringMembers(Integer days) {
        return miniVipMemberMapper.selectExpiringMembers(days);
    }

    /**
     * 获取所有子场库选项
     *
     * @return 子场库列表
     */
    @Override
    public List<MiniWarehouse> selectChildWarehouseOptions() {
        return miniWarehouseMapper.selectChildWarehouseOptions();
    }

    /**
     * 根据父场库ID获取子场库选项
     *
     * @param parentId 父场库ID
     * @return 子场库列表
     */
    @Override
    public List<MiniWarehouse> selectChildWarehouseOptionsByParentId(Long parentId) {
        return miniWarehouseMapper.selectChildWarehouseOptionsByParentId(parentId);
    }

    /**
     * 根据停车场ID获取套餐选项
     *
     * @param parkingLotId 停车场ID
     * @return 套餐列表
     */
    @Override
    public List<MiniVipPackage> getPackageOptionsByParkingLotId(Long parkingLotId) {
        return miniVipPackageMapper.selectPackagesByWarehouseId(parkingLotId);
    }

    /**
     * 获取所有场库选项（包含层级关系）
     *
     * @return 场库列表
     */
    @Override
    public List<MiniWarehouse> selectAllWarehouseOptions() {
        return miniWarehouseMapper.selectAllWarehouseOptions();
    }

}
