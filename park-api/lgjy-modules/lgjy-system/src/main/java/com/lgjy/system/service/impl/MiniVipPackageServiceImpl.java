package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.mapper.MiniVipPackageMapper;
import com.lgjy.system.mapper.MiniWarehouseMapper;
import com.lgjy.system.domain.MiniVipPackage;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.service.IMiniVipPackageService;

/**
 * 会员套餐配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniVipPackageServiceImpl implements IMiniVipPackageService {
    @Autowired
    private MiniVipPackageMapper miniVipPackageMapper;

    @Autowired
    private MiniWarehouseMapper miniWarehouseMapper;

    /**
     * 查询会员套餐配置
     *
     * @param id 会员套餐配置主键
     * @return 会员套餐配置
     */
    @Override
    public MiniVipPackage selectMiniVipPackageById(Long id) {
        MiniVipPackage vipPackage = miniVipPackageMapper.selectMiniVipPackageById(id);
        if (vipPackage != null && vipPackage.getWarehouseId() != null) {
            MiniWarehouse warehouse = miniWarehouseMapper
                    .selectMiniWarehouseById(vipPackage.getWarehouseId());
            if (warehouse != null) {
                vipPackage.setWarehouseName(warehouse.getWarehouseName());
            }
        }
        return vipPackage;
    }

    /**
     * 查询会员套餐配置列表
     *
     * @param miniVipPackage 会员套餐配置
     * @return 会员套餐配置
     */
    @Override
    public List<MiniVipPackage> selectMiniVipPackageList(MiniVipPackage miniVipPackage) {
        List<MiniVipPackage> list = miniVipPackageMapper.selectMiniVipPackageList(miniVipPackage);
        // 填充场库名称
        for (MiniVipPackage vipPackage : list) {
            if (vipPackage.getWarehouseId() != null) {
                MiniWarehouse warehouse = miniWarehouseMapper
                        .selectMiniWarehouseById(vipPackage.getWarehouseId());
                if (warehouse != null) {
                    vipPackage.setWarehouseName(warehouse.getWarehouseName());
                }
            }
        }
        return list;
    }

    /**
     * 新增会员套餐配置
     *
     * @param miniVipPackage 会员套餐配置
     * @return 结果
     */
    @Override
    public int insertMiniVipPackage(MiniVipPackage miniVipPackage) {
        miniVipPackage.setDeleteFlag(0);
        return miniVipPackageMapper.insertMiniVipPackage(miniVipPackage);
    }

    /**
     * 修改会员套餐配置
     *
     * @param miniVipPackage 会员套餐配置
     * @return 结果
     */
    @Override
    public int updateMiniVipPackage(MiniVipPackage miniVipPackage) {
        return miniVipPackageMapper.updateMiniVipPackage(miniVipPackage);
    }

    /**
     * 批量删除会员套餐配置
     *
     * @param ids 需要删除的会员套餐配置主键
     * @return 结果
     */
    @Override
    public int deleteMiniVipPackageByIds(Long[] ids) {
        return miniVipPackageMapper.deleteMiniVipPackageByIds(ids);
    }

    /**
     * 删除会员套餐配置信息
     *
     * @param id 会员套餐配置主键
     * @return 结果
     */
    @Override
    public int deleteMiniVipPackageById(Long id) {
        return miniVipPackageMapper.deleteMiniVipPackageById(id);
    }

    /**
     * 根据停车场ID查询套餐列表
     *
     * @param warehouseId 停车场ID
     * @return 套餐列表
     */
    @Override
    public List<MiniVipPackage> selectPackagesByWarehouseId(Long warehouseId) {
        return miniVipPackageMapper.selectPackagesByWarehouseId(warehouseId);
    }

    /**
     * 获取所有场库和停车场选项（包含层级关系）
     *
     * @return 场库和停车场列表
     */
    @Override
    public List<MiniWarehouse> selectAllWarehouseOptions() {
        return miniWarehouseMapper.selectAllWarehouseOptions();
    }

}
