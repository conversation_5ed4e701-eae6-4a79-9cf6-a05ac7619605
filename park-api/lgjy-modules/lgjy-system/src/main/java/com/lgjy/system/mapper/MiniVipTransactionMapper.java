package com.lgjy.system.mapper;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import com.lgjy.system.domain.MiniVipTransaction;

/**
 * 会员交易记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MiniVipTransactionMapper {
    /**
     * 查询会员交易记录
     * 
     * @param id 会员交易记录主键
     * @return 会员交易记录
     */
    public MiniVipTransaction selectMiniVipTransactionById(Long id);

    /**
     * 查询会员交易记录列表
     * 
     * @param miniVipTransaction 会员交易记录
     * @return 会员交易记录集合
     */
    public List<MiniVipTransaction> selectMiniVipTransactionList(MiniVipTransaction miniVipTransaction);

    /**
     * 新增会员交易记录
     * 
     * @param miniVipTransaction 会员交易记录
     * @return 结果
     */
    public int insertMiniVipTransaction(MiniVipTransaction miniVipTransaction);

    /**
     * 修改会员交易记录
     * 
     * @param miniVipTransaction 会员交易记录
     * @return 结果
     */
    public int updateMiniVipTransaction(MiniVipTransaction miniVipTransaction);

    /**
     * 删除会员交易记录
     * 
     * @param id 会员交易记录主键
     * @return 结果
     */
    public int deleteMiniVipTransactionById(Long id);

    /**
     * 批量删除会员交易记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniVipTransactionByIds(Long[] ids);

    /**
     * 校验交易流水号是否唯一
     * 
     * @param transactionNo 交易流水号
     * @return 结果
     */
    public MiniVipTransaction checkTransactionNoUnique(String transactionNo);

    /**
     * 根据会员ID查询交易记录
     * 
     * @param memberId 会员ID
     * @return 交易记录列表
     */
    public List<MiniVipTransaction> selectTransactionsByMemberId(Long memberId);

    /**
     * 根据套餐ID查询交易记录
     * 
     * @param packageId 套餐ID
     * @return 交易记录列表
     */
    public List<MiniVipTransaction> selectTransactionsByPackageId(Long packageId);

    /**
     * 统计交易金额按状态
     *
     * @param params 查询参数（可包含warehouseId和parkingLotId）
     * @return 统计结果
     */
    public List<Map<String, Object>> sumAmountByStatus(Map<String, Object> params);

    /**
     * 统计交易数量按类型
     *
     * @param params 查询参数（可包含warehouseId和parkingLotId）
     * @return 统计结果
     */
    public List<Map<String, Object>> countTransactionsByType(Map<String, Object> params);

    /**
     * 查询指定时间范围内的交易记录
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 交易记录列表
     */
    public List<MiniVipTransaction> selectTransactionsByTimeRange(String startTime, String endTime);

    /**
     * 计算会员总消费金额
     * 
     * @param memberId 会员ID
     * @return 总消费金额
     */
    public BigDecimal calculateMemberTotalConsumption(Long memberId);
}
