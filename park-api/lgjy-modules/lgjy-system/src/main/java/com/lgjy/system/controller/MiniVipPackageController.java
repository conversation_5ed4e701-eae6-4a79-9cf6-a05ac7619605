package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniVipPackage;
import com.lgjy.system.service.IMiniVipPackageService;

/**
 * 会员套餐配置Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/vip/package")
public class MiniVipPackageController extends BaseController {
    @Autowired
    private IMiniVipPackageService miniVipPackageService;

    /**
     * 查询会员套餐配置列表
     */
    @RequiresPermissions("vip:package:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniVipPackage miniVipPackage) {
        startPage();
        List<MiniVipPackage> list = miniVipPackageService.selectMiniVipPackageList(miniVipPackage);
        return getDataTable(list);
    }

    /**
     * 导出会员套餐配置列表
     */
    @RequiresPermissions("vip:package:export")
    @Log(title = "会员套餐配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniVipPackage miniVipPackage) {
        List<MiniVipPackage> list = miniVipPackageService.selectMiniVipPackageList(miniVipPackage);
        ExcelUtil<MiniVipPackage> util = new ExcelUtil<MiniVipPackage>(MiniVipPackage.class);
        util.exportExcel(response, list, "会员套餐配置数据");
    }

    /**
     * 获取会员套餐配置详细信息
     */
    @RequiresPermissions("vip:package:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(miniVipPackageService.selectMiniVipPackageById(id));
    }

    /**
     * 新增会员套餐配置
     */
    @RequiresPermissions("vip:package:add")
    @Log(title = "会员套餐配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniVipPackage miniVipPackage) {
        return toAjax(miniVipPackageService.insertMiniVipPackage(miniVipPackage));
    }

    /**
     * 修改会员套餐配置
     */
    @RequiresPermissions("vip:package:edit")
    @Log(title = "会员套餐配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniVipPackage miniVipPackage) {
        return toAjax(miniVipPackageService.updateMiniVipPackage(miniVipPackage));
    }

    /**
     * 删除会员套餐配置
     */
    @RequiresPermissions("vip:package:remove")
    @Log(title = "会员套餐配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable(value = "ids") Long[] ids) {
        return toAjax(miniVipPackageService.deleteMiniVipPackageByIds(ids));
    }

    /**
     * 根据场库ID获取套餐列表
     */
    @GetMapping("/warehouse/{warehouseId}")
    public AjaxResult getPackagesByWarehouse(@PathVariable(value = "warehouseId") Long warehouseId) {
        List<MiniVipPackage> packages = miniVipPackageService.selectPackagesByWarehouseId(warehouseId);
        return success(packages);
    }

    /**
     * 获取所有场库选项（包含层级关系）
     */
    @GetMapping("/warehouseOptions")
    public AjaxResult getAllWarehouseOptions() {
        return success(miniVipPackageService.selectAllWarehouseOptions());
    }

}
