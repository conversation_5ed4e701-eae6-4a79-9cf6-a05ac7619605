package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniUnionPayConfig;
import com.lgjy.system.service.IMiniUnionPayConfigService;
import com.lgjy.system.service.IMiniWarehouseService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 银联配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@RestController
@RequestMapping("/platform/unionPayConfig")
public class MiniUnionPayConfigController extends BaseController {
    @Autowired
    private IMiniUnionPayConfigService miniUnionPayConfigService;

    @Autowired
    private IMiniWarehouseService miniWarehouseService;

    /**
     * 查询银联配置列表
     */
    @RequiresPermissions("platform:unionPayConfig:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniUnionPayConfig miniUnionPayConfig) {
        startPage();
        List<MiniUnionPayConfig> list = miniUnionPayConfigService.selectMiniUnionPayConfigList(miniUnionPayConfig);
        return getDataTable(list);
    }

    /**
     * 导出银联配置列表
     */
    @RequiresPermissions("platform:unionPayConfig:export")
    @Log(title = "银联配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniUnionPayConfig miniUnionPayConfig) {
        List<MiniUnionPayConfig> list = miniUnionPayConfigService.exportMiniUnionPayConfig(miniUnionPayConfig);
        ExcelUtil<MiniUnionPayConfig> util = new ExcelUtil<MiniUnionPayConfig>(MiniUnionPayConfig.class);
        util.exportExcel(response, list, "银联配置数据");
    }

    /**
     * 获取银联配置详细信息
     */
    @RequiresPermissions("platform:unionPayConfig:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("data", miniUnionPayConfigService.selectMiniUnionPayConfigById(id));
        ajax.put("warehouses", miniWarehouseService.selectMiniWarehouseAll());
        return ajax;
    }

    /**
     * 新增银联配置
     */
    @RequiresPermissions("platform:unionPayConfig:add")
    @Log(title = "银联配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniUnionPayConfig miniUnionPayConfig) {
        return toAjax(miniUnionPayConfigService.insertMiniUnionPayConfig(miniUnionPayConfig));
    }

    /**
     * 修改银联配置
     */
    @RequiresPermissions("platform:unionPayConfig:edit")
    @Log(title = "银联配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniUnionPayConfig miniUnionPayConfig) {
        return toAjax(miniUnionPayConfigService.updateMiniUnionPayConfig(miniUnionPayConfig));
    }

    /**
     * 删除银联配置
     */
    @RequiresPermissions("platform:unionPayConfig:remove")
    @Log(title = "银联配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniUnionPayConfigService.deleteMiniUnionPayConfigByIds(ids));
    }

    /**
     * 校验商户号是否唯一
     */
    @GetMapping("/checkMidUnique")
    public AjaxResult checkMidUnique(String mid, Long id) {
        boolean unique = miniUnionPayConfigService.checkMidUnique(mid, id);
        return AjaxResult.success(unique);
    }

    /**
     * 校验终端号是否唯一
     */
    @GetMapping("/checkTidUnique")
    public AjaxResult checkTidUnique(String tid, Long id) {
        boolean unique = miniUnionPayConfigService.checkTidUnique(tid, id);
        return AjaxResult.success(unique);
    }

    /**
     * 根据场库ID查询银联配置
     */
    @GetMapping("/getByWarehouseId/{warehouseId}")
    public AjaxResult getByWarehouseId(@PathVariable("warehouseId") Long warehouseId) {
        MiniUnionPayConfig config = miniUnionPayConfigService.selectMiniUnionPayConfigByWarehouseId(warehouseId);
        return AjaxResult.success(config);
    }
}
