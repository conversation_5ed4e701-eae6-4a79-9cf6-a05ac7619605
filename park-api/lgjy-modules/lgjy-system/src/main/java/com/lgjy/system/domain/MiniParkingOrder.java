package com.lgjy.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 停车订单对象 mini_parking_order
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MiniParkingOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 场库管理ID */
    @Excel(name = "场库管理ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parkingManageId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    /** 开始停车时间 */
    @Excel(name = "开始停车时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginParkingTime;

    /** 结束停车时间 */
    @Excel(name = "结束停车时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endParkingTime;

    /** 停车时长（分钟） */
    @Excel(name = "停车时长(分钟)")
    private Integer parkingDuration;

    /** 应付金额 */
    @Excel(name = "应付金额")
    private BigDecimal paymentAmount;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;

    /** 实付金额 */
    @Excel(name = "实付金额")
    private BigDecimal actualPayment;

    /** 支付方式（0-银联支付，1-微信支付，2-支付宝，3-现金，4-免费） */
    @Excel(name = "支付方式", readConverterExp = "0=银联支付,1=微信支付,2=支付宝,3=现金,4=免费")
    private Integer payType;

    /** 停车预约ID */
    @Excel(name = "停车预约ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parkingReservationId;

    /** 发票ID */
    @Excel(name = "发票ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long invoiceId;

    /** 交易ID */
    @Excel(name = "交易ID")
    private String tradeId;

    /** 支付状态（0-未支付，1-已支付，2-支付失败，3-已退款） */
    @Excel(name = "支付状态", readConverterExp = "0=未支付,1=已支付,2=支付失败,3=已退款")
    @NotNull(message = "支付状态不能为空")
    private Integer payStatus;

    /** 支付时间 */
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /** 微信openId */
    @Excel(name = "微信openId")
    private String openId;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String carType;

    /** 删除标志（0代表存在 1代表删除） */
    private Integer deleteFlag;

    /** 退款原因（临时字段，用于退款接口） */
    private String refundReason;

    /** 搜索参数 - 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /** 搜索参数 - 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public void setRefundReason(String refundReason)
    {
        this.refundReason = refundReason;
    }

    public String getRefundReason()
    {
        return refundReason;
    }

    @Override
    public String toString() {
        return "MiniParkingOrder{" +
                "id=" + id +
                ", warehouseId=" + warehouseId +
                ", warehouseName='" + warehouseName + '\'' +
                ", parkingManageId=" + parkingManageId +
                ", userId=" + userId +
                ", plateNo='" + plateNo + '\'' +
                ", beginParkingTime=" + beginParkingTime +
                ", endParkingTime=" + endParkingTime +
                ", parkingDuration=" + parkingDuration +
                ", paymentAmount=" + paymentAmount +
                ", discountAmount=" + discountAmount +
                ", actualPayment=" + actualPayment +
                ", payType=" + payType +
                ", parkingReservationId=" + parkingReservationId +
                ", invoiceId=" + invoiceId +
                ", tradeId='" + tradeId + '\'' +
                ", payStatus=" + payStatus +
                ", paymentTime=" + paymentTime +
                ", openId='" + openId + '\'' +
                ", carType='" + carType + '\'' +
                ", deleteFlag=" + deleteFlag +
                ", beginTime=" + beginTime +
                ", endTime=" + endTime +
                '}';
    }
}
