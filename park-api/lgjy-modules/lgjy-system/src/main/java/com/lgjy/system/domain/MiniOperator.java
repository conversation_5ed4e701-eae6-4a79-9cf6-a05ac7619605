package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 运营商信息对象 mini_operator
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MiniOperator extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 运营商公司名称 */
    @Excel(name = "运营商公司名称")
    @NotBlank(message = "运营商公司名称不能为空")
    private String companyName;

    /** 主要联系人姓名 */
    @Excel(name = "主要联系人姓名")
    @NotBlank(message = "主要联系人姓名不能为空")
    private String primaryContactName;

    /** 主要联系人电话 */
    @Excel(name = "主要联系人电话")
    @NotBlank(message = "主要联系人电话不能为空")
    private String primaryContactPhone;

    /** 创建者用户ID */
    @Excel(name = "创建者")
    private Long userId;

    /** 删除标识 0-未删除 1-已删除 */
    private Integer deleteFlag;

    /** 创建者姓名 */
    @Excel(name = "创建者")
    private String createdBy;

    /** 更新者姓名 */
    @Excel(name = "更新者")
    private String updatedBy;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("primaryContactName", getPrimaryContactName())
                .append("primaryContactPhone", getPrimaryContactPhone())
                .append("remark", getRemark())
                .append("userId", getUserId())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
