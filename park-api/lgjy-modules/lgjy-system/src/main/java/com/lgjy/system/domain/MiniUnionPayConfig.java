package com.lgjy.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 银联配置对象 mini_union_pay_config
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class MiniUnionPayConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 支付类型 */
    @Excel(name = "支付类型", readConverterExp = "1=银联支付,2=微信支付,3=支付宝")
    private Integer payType;

    /** 商户号 */
    @Excel(name = "商户号")
    private String mid;

    /** 终端号 */
    @Excel(name = "终端号")
    private String tid;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 删除标志（0代表存在 1代表删除） */
    private Integer deleteFlag;

    /** 创建者姓名 */
    @Excel(name = "创建者")
    private String createByName;

    /** 更新者姓名 */
    @Excel(name = "更新者")
    private String updateByName;

    @Override
    public String toString() {
        return "MiniUnionPayConfig{" +
                "id=" + id +
                ", warehouseId=" + warehouseId +
                ", warehouseName='" + warehouseName + '\'' +
                ", payType=" + payType +
                ", mid='" + mid + '\'' +
                ", tid='" + tid + '\'' +
                ", remark='" + remark + '\'' +
                ", deleteFlag=" + deleteFlag +
                '}';
    }
}
