package com.lgjy.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 会员套餐配置对象 mini_vip_package
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class MiniVipPackage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 场库ID */
    @Excel(name = "场库ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotNull(message = "场库ID不能为空")
    private Long warehouseId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 父场库名称 */
    @Excel(name = "父场库名称")
    private String parentWarehouseName;

    /** 套餐类型 */
    @Excel(name = "套餐类型", readConverterExp = "1=一天,3=三天,5=五天,7=七天,10=十天,15=十五天,30=包月,60=两个月,90=包季,180=半年,270=九个月,365=包年")
    @NotNull(message = "套餐类型不能为空")
    private Integer packageType;

    /** 停车类型 */
    @Excel(name = "停车类型", readConverterExp = "0=否,1=是")
    private Integer parkType;

    /** 套餐名称 */
    @Excel(name = "套餐名称")
    @NotBlank(message = "套餐名称不能为空")
    private String packageName;

    /** 套餐价格 */
    @Excel(name = "套餐价格")
    @NotNull(message = "套餐价格不能为空")
    @DecimalMin(value = "0.01", message = "套餐价格必须大于0")
    private BigDecimal packagePrice;

    /** 赠送天数 */
    @Excel(name = "赠送天数")
    private Integer giveDays;

    /** 套餐总会员时长（基础天数+赠送天数） */
    @Excel(name = "总会员时长")
    private Integer totalDays;

    /** 套餐规则 */
    private String packageRule;

    /** 规则类型 */
    private Integer ruleType;

    /** 优惠开始时间 */
    @Excel(name = "优惠开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preferBeginTime;

    /** 优惠结束时间 */
    @Excel(name = "优惠结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preferEndTime;



    /** 删除标识 */
    private Integer deleteFlag;

    /**
     * 自定义warehouseId的JSON反序列化处理
     * 兼容处理前端级联选择器传递的数组格式和单值格式
     */
    @JsonSetter("warehouseId")
    public void setWarehouseIdFromJson(Object warehouseId) {
        if (warehouseId instanceof List) {
            // 处理数组格式：取最后一个元素（用户实际选中的场库ID）
            List<?> list = (List<?>) warehouseId;
            if (!list.isEmpty()) {
                this.warehouseId = Long.valueOf(list.get(list.size() - 1).toString());
            }
        } else if (warehouseId != null) {
            // 处理单值格式
            this.warehouseId = Long.valueOf(warehouseId.toString());
        }
    }

    /**
     * 标准的warehouseId setter方法，忽略JSON反序列化
     */
    @JsonIgnore
    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("warehouseId", getWarehouseId())
                .append("warehouseName", getWarehouseName())
                .append("parentWarehouseName", getParentWarehouseName())
                .append("packageType", getPackageType())
                .append("parkType", getParkType())
                .append("packageName", getPackageName())
                .append("packagePrice", getPackagePrice())
                .append("giveDays", getGiveDays())
                .append("totalDays", getTotalDays())
                .append("packageRule", getPackageRule())
                .append("ruleType", getRuleType())
                .append("preferBeginTime", getPreferBeginTime())
                .append("preferEndTime", getPreferEndTime())
                .append("remark", getRemark())
                .append("deleteFlag", getDeleteFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
