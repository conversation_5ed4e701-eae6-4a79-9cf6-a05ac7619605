package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniInvoiceRecord;
import com.lgjy.system.service.IMiniInvoiceRecordService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 发票记录管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/operation/invoiceRecord")
public class MiniInvoiceRecordController extends BaseController
{
    @Autowired
    private IMiniInvoiceRecordService miniInvoiceRecordService;

    /**
     * 查询发票记录管理列表
     */
    @RequiresPermissions("operation:invoiceRecord:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniInvoiceRecord miniInvoiceRecord)
    {
        startPage();
        List<MiniInvoiceRecord> list = miniInvoiceRecordService.selectMiniInvoiceRecordList(miniInvoiceRecord);
        return getDataTable(list);
    }

    /**
     * 导出发票记录管理列表
     */
    @RequiresPermissions("operation:invoiceRecord:export")
    @Log(title = "发票记录管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniInvoiceRecord miniInvoiceRecord)
    {
        List<MiniInvoiceRecord> list = miniInvoiceRecordService.selectMiniInvoiceRecordList(miniInvoiceRecord);
        ExcelUtil<MiniInvoiceRecord> util = new ExcelUtil<MiniInvoiceRecord>(MiniInvoiceRecord.class);
        util.exportExcel(response, list, "发票记录管理数据");
    }

    /**
     * 获取发票记录管理详细信息
     */
    @RequiresPermissions("operation:invoiceRecord:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(miniInvoiceRecordService.selectMiniInvoiceRecordById(id));
    }

    /**
     * 新增发票记录管理
     */
    @RequiresPermissions("operation:invoiceRecord:add")
    @Log(title = "发票记录管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniInvoiceRecord miniInvoiceRecord)
    {
        return toAjax(miniInvoiceRecordService.insertMiniInvoiceRecord(miniInvoiceRecord));
    }

    /**
     * 修改发票记录管理
     */
    @RequiresPermissions("operation:invoiceRecord:edit")
    @Log(title = "发票记录管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniInvoiceRecord miniInvoiceRecord)
    {
        return toAjax(miniInvoiceRecordService.updateMiniInvoiceRecord(miniInvoiceRecord));
    }

    /**
     * 删除发票记录管理
     */
    @RequiresPermissions("operation:invoiceRecord:remove")
    @Log(title = "发票记录管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(miniInvoiceRecordService.deleteMiniInvoiceRecordByIds(ids));
    }

    /**
     * 发票重开
     */
    @RequiresPermissions("operation:invoiceRecord:reopen")
    @Log(title = "发票重开", businessType = BusinessType.UPDATE)
    @PostMapping("/reopen/{id}")
    public AjaxResult reopenInvoice(@PathVariable("id") Long id)
    {
        return toAjax(miniInvoiceRecordService.reopenInvoice(id));
    }

    /**
     * 发票冲红
     */
    @RequiresPermissions("operation:invoiceRecord:reverse")
    @Log(title = "发票冲红", businessType = BusinessType.UPDATE)
    @PostMapping("/reverse/{id}")
    public AjaxResult reverseInvoice(@PathVariable("id") Long id)
    {
        return toAjax(miniInvoiceRecordService.reverseInvoice(id));
    }
}
