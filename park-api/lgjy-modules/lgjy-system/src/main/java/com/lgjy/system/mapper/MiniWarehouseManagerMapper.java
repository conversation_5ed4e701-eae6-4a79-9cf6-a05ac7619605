package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniWarehouseManager;

/**
 * 场库管理人员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MiniWarehouseManagerMapper {
    /**
     * 查询场库管理人员信息
     *
     * @param id 场库管理人员信息主键
     * @return 场库管理人员信息
     */
    public MiniWarehouseManager selectMiniWarehouseManagerById(Long id);

    /**
     * 查询场库管理人员信息列表
     *
     * @param miniWarehouseManager 场库管理人员信息
     * @return 场库管理人员信息集合
     */
    public List<MiniWarehouseManager> selectMiniWarehouseManagerList(MiniWarehouseManager miniWarehouseManager);

    /**
     * 新增场库管理人员信息
     *
     * @param miniWarehouseManager 场库管理人员信息
     * @return 结果
     */
    public int insertMiniWarehouseManager(MiniWarehouseManager miniWarehouseManager);

    /**
     * 修改场库管理人员信息
     *
     * @param miniWarehouseManager 场库管理人员信息
     * @return 结果
     */
    public int updateMiniWarehouseManager(MiniWarehouseManager miniWarehouseManager);

    /**
     * 删除场库管理人员信息
     *
     * @param id 场库管理人员信息主键
     * @return 结果
     */
    public int deleteMiniWarehouseManagerById(Long id);

    /**
     * 批量删除场库管理人员信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniWarehouseManagerByIds(Long[] ids);



    /**
     * 根据场库ID查询管理人员数量
     *
     * @param warehouseId 场库ID
     * @return 管理人员数量
     */
    public int countManagerByWarehouseId(Long warehouseId);

    /**
     * 根据运营商ID查询管理人员数量
     *
     * @param operatorId 运营商ID
     * @return 管理人员数量
     */
    public int countManagerByOperatorId(Long operatorId);
}
