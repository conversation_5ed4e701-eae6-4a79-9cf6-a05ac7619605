package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniSpecialUser;
import org.apache.ibatis.annotations.Param;

/**
 * 特殊会员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface MiniSpecialUserMapper {
    /**
     * 查询特殊会员
     * 
     * @param id 特殊会员主键
     * @return 特殊会员
     */
    public MiniSpecialUser selectMiniSpecialUserById(Long id);

    /**
     * 查询特殊会员列表
     * 
     * @param miniSpecialUser 特殊会员
     * @return 特殊会员集合
     */
    public List<MiniSpecialUser> selectMiniSpecialUserList(MiniSpecialUser miniSpecialUser);

    /**
     * 新增特殊会员
     * 
     * @param miniSpecialUser 特殊会员
     * @return 结果
     */
    public int insertMiniSpecialUser(MiniSpecialUser miniSpecialUser);

    /**
     * 修改特殊会员
     * 
     * @param miniSpecialUser 特殊会员
     * @return 结果
     */
    public int updateMiniSpecialUser(MiniSpecialUser miniSpecialUser);

    /**
     * 批量删除特殊会员
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniSpecialUserByIds(Long[] ids);



    /**
     * 校验手机号是否唯一
     * 
     * @param phoneNumber 手机号
     * @return 结果
     */
    public MiniSpecialUser checkPhoneNumberUnique(String phoneNumber);

    /**
     * 校验车牌号是否唯一
     * 
     * @param plateNo 车牌号
     * @return 结果
     */
    public MiniSpecialUser checkPlateNoUnique(String plateNo);

    /**
     * 统计特殊会员数量按类型
     *
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> countSpecialUsersByType();

    /**
     * 更新特殊车辆表的手机号（处理156,256,356排序方式）
     *
     * @param oldPhoneSuffix 旧手机号后10位
     * @param newPhoneSuffix 新手机号后10位
     * @return 更新记录数
     */
    int updateSpecialUserCarPhoneNumbers(@Param("oldPhoneSuffix") String oldPhoneSuffix, @Param("newPhoneSuffix") String newPhoneSuffix);

    /**
     * 根据特殊用户ID统计特殊车辆数量
     *
     * @param specialUserId 特殊用户ID
     * @return 车辆数量
     */
    int countSpecialUserCarsByUserId(@Param("specialUserId") Long specialUserId);

    /**
     * 根据手机号统计特殊车辆数量（处理156,256,356排序方式）
     *
     * @param phoneNumber 手机号
     * @return 车辆数量
     */
    int countSpecialUserCarsByPhoneNumber(@Param("phoneNumber") String phoneNumber);
}
