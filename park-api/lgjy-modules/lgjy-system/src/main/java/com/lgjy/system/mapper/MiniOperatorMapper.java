package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.MiniOperator;

/**
 * 运营商信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MiniOperatorMapper 
{
    /**
     * 查询运营商信息
     * 
     * @param id 运营商信息主键
     * @return 运营商信息
     */
    public MiniOperator selectMiniOperatorById(Long id);

    /**
     * 查询运营商信息列表
     * 
     * @param miniOperator 运营商信息
     * @return 运营商信息集合
     */
    public List<MiniOperator> selectMiniOperatorList(MiniOperator miniOperator);

    /**
     * 新增运营商信息
     * 
     * @param miniOperator 运营商信息
     * @return 结果
     */
    public int insertMiniOperator(MiniOperator miniOperator);

    /**
     * 修改运营商信息
     * 
     * @param miniOperator 运营商信息
     * @return 结果
     */
    public int updateMiniOperator(MiniOperator miniOperator);

    /**
     * 删除运营商信息
     * 
     * @param id 运营商信息主键
     * @return 结果
     */
    public int deleteMiniOperatorById(Long id);

    /**
     * 批量删除运营商信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniOperatorByIds(Long[] ids);

    /**
     * 校验运营商公司名称是否唯一
     * 
     * @param companyName 运营商公司名称
     * @return 结果
     */
    public MiniOperator checkCompanyNameUnique(String companyName);

    /**
     * 查询所有运营商列表（用于下拉选择）
     * 
     * @return 运营商列表
     */
    public List<MiniOperator> selectMiniOperatorAll();
}
