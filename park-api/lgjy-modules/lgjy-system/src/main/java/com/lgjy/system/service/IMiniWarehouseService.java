package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.MiniWarehouse;

/**
 * 场库信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniWarehouseService {
    /**
     * 查询场库信息
     *
     * @param id 场库信息主键
     * @return 场库信息
     */
    public MiniWarehouse selectMiniWarehouseById(Long id);

    /**
     * 查询场库信息列表
     *
     * @param miniWarehouse 场库信息
     * @return 场库信息集合
     */
    public List<MiniWarehouse> selectMiniWarehouseList(MiniWarehouse miniWarehouse);

    /**
     * 新增场库信息
     *
     * @param miniWarehouse 场库信息
     * @return 结果
     */
    public int insertMiniWarehouse(MiniWarehouse miniWarehouse);

    /**
     * 修改场库信息
     *
     * @param miniWarehouse 场库信息
     * @return 结果
     */
    public int updateMiniWarehouse(MiniWarehouse miniWarehouse);

    /**
     * 批量删除场库信息
     *
     * @param ids 需要删除的场库信息主键集合
     * @return 结果
     */
    public int deleteMiniWarehouseByIds(Long[] ids);

    /**
     * 删除场库信息
     *
     * @param id 场库信息主键
     * @return 结果
     */
    public int deleteMiniWarehouseById(Long id);

    /**
     * 查询所有场库列表（用于下拉选择）
     *
     * @return 场库列表
     */
    public List<MiniWarehouse> selectMiniWarehouseAll();

    /**
     * 根据运营商ID查询场库列表
     *
     * @param operatorId 运营商ID
     * @return 场库列表
     */
    public List<MiniWarehouse> selectMiniWarehouseByOperatorId(Long operatorId);

    /**
     * 查询所有子场库列表（用于下拉选择）
     *
     * @return 子场库列表
     */
    public List<MiniWarehouse> selectChildWarehouseOptions();

    /**
     * 根据父场库ID查询子场库列表
     *
     * @param parentId 父场库ID
     * @return 子场库列表
     */
    public List<MiniWarehouse> selectChildWarehouseOptionsByParentId(Long parentId);

    /**
     * 统计场库下的子场库数量
     *
     * @param parentId 父场库ID
     * @return 子场库数量
     */
    public int countChildWarehousesByParentId(Long parentId);

    /**
     * 获取所有场库选项（包含层级关系）
     *
     * @return 场库列表
     */
    public List<MiniWarehouse> selectAllWarehouseOptions();

    /**
     * 构建场库树结构
     *
     * @param warehouses 场库列表
     * @return 树结构列表
     */
    public List<MiniWarehouse> buildWarehouseTree(List<MiniWarehouse> warehouses);

    /**
     * 查询场库树结构信息
     *
     * @param miniWarehouse 场库信息
     * @return 场库树信息集合
     */
    public List<MiniWarehouse> selectWarehouseTreeList(MiniWarehouse miniWarehouse);
}
