package com.lgjy.system.service.impl;

import java.util.List;
import java.util.Date;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.MiniInvoiceRecordMapper;
import com.lgjy.system.domain.MiniInvoiceRecord;
import com.lgjy.system.service.IMiniInvoiceRecordService;

/**
 * 发票记录管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class MiniInvoiceRecordServiceImpl implements IMiniInvoiceRecordService 
{
    @Autowired
    private MiniInvoiceRecordMapper miniInvoiceRecordMapper;

    /**
     * 查询发票记录管理
     * 
     * @param id 发票记录管理主键
     * @return 发票记录管理
     */
    @Override
    public MiniInvoiceRecord selectMiniInvoiceRecordById(Long id)
    {
        return miniInvoiceRecordMapper.selectMiniInvoiceRecordById(id);
    }

    /**
     * 查询发票记录管理列表
     * 
     * @param miniInvoiceRecord 发票记录管理
     * @return 发票记录管理
     */
    @Override
    public List<MiniInvoiceRecord> selectMiniInvoiceRecordList(MiniInvoiceRecord miniInvoiceRecord)
    {
        return miniInvoiceRecordMapper.selectMiniInvoiceRecordList(miniInvoiceRecord);
    }

    /**
     * 新增发票记录管理
     * 
     * @param miniInvoiceRecord 发票记录管理
     * @return 结果
     */
    @Override
    public int insertMiniInvoiceRecord(MiniInvoiceRecord miniInvoiceRecord)
    {
        miniInvoiceRecord.setCreateTime(DateUtils.getNowDate());
        return miniInvoiceRecordMapper.insertMiniInvoiceRecord(miniInvoiceRecord);
    }

    /**
     * 修改发票记录管理
     * 
     * @param miniInvoiceRecord 发票记录管理
     * @return 结果
     */
    @Override
    public int updateMiniInvoiceRecord(MiniInvoiceRecord miniInvoiceRecord)
    {
        miniInvoiceRecord.setUpdateTime(DateUtils.getNowDate());
        return miniInvoiceRecordMapper.updateMiniInvoiceRecord(miniInvoiceRecord);
    }

    /**
     * 批量删除发票记录管理
     * 
     * @param ids 需要删除的发票记录管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniInvoiceRecordByIds(Long[] ids)
    {
        return miniInvoiceRecordMapper.deleteMiniInvoiceRecordByIds(ids);
    }

    /**
     * 删除发票记录管理信息
     * 
     * @param id 发票记录管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniInvoiceRecordById(Long id)
    {
        return miniInvoiceRecordMapper.deleteMiniInvoiceRecordById(id);
    }

    /**
     * 发票重开
     * 
     * @param id 发票记录ID
     * @return 结果
     */
    @Override
    public int reopenInvoice(Long id)
    {
        MiniInvoiceRecord invoice = miniInvoiceRecordMapper.selectMiniInvoiceRecordById(id);
        if (invoice == null) {
            throw new ServiceException("发票记录不存在");
        }
        
        // 检查发票状态是否允许重开
        if (!"ISSUED".equals(invoice.getStatus())) {
            throw new ServiceException("只有已开具的发票才能重开");
        }
        
        // 设置重开标识
        invoice.setReopenSign(1);
        invoice.setUpdateTime(DateUtils.getNowDate());
        
        // TODO: 这里应该调用银联发票重开接口
        // 暂时只更新数据库状态
        
        return miniInvoiceRecordMapper.updateMiniInvoiceRecord(invoice);
    }

    /**
     * 发票冲红
     * 
     * @param id 发票记录ID
     * @return 结果
     */
    @Override
    public int reverseInvoice(Long id)
    {
        MiniInvoiceRecord invoice = miniInvoiceRecordMapper.selectMiniInvoiceRecordById(id);
        if (invoice == null) {
            throw new ServiceException("发票记录不存在");
        }
        
        // 检查发票状态是否允许冲红
        if (!"ISSUED".equals(invoice.getStatus())) {
            throw new ServiceException("只有已开具的发票才能冲红");
        }
        
        // 设置冲红日期和状态
        invoice.setReverseDate(new Date());
        invoice.setStatus("REVERSED");
        invoice.setUpdateTime(DateUtils.getNowDate());
        
        // TODO: 这里应该调用银联发票冲红接口
        // 暂时只更新数据库状态
        
        return miniInvoiceRecordMapper.updateMiniInvoiceRecord(invoice);
    }
}
