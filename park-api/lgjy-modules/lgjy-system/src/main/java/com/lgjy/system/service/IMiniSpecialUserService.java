package com.lgjy.system.service;

import java.util.List;
import java.util.Map;
import com.lgjy.system.domain.MiniSpecialUser;

/**
 * 特殊会员Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IMiniSpecialUserService {
    /**
     * 查询特殊会员
     * 
     * @param id 特殊会员主键
     * @return 特殊会员
     */
    public MiniSpecialUser selectMiniSpecialUserById(Long id);

    /**
     * 查询特殊会员列表
     * 
     * @param miniSpecialUser 特殊会员
     * @return 特殊会员集合
     */
    public List<MiniSpecialUser> selectMiniSpecialUserList(MiniSpecialUser miniSpecialUser);

    /**
     * 新增特殊会员
     * 
     * @param miniSpecialUser 特殊会员
     * @return 结果
     */
    public int insertMiniSpecialUser(MiniSpecialUser miniSpecialUser);

    /**
     * 修改特殊会员
     * 
     * @param miniSpecialUser 特殊会员
     * @return 结果
     */
    public int updateMiniSpecialUser(MiniSpecialUser miniSpecialUser);

    /**
     * 批量删除特殊会员
     *
     * @param ids 需要删除的特殊会员主键集合
     * @return 结果
     */
    public int deleteMiniSpecialUserByIds(Long[] ids);


    /**
     * 校验手机号是否唯一
     *
     * @param miniSpecialUser 特殊会员信息
     * @return 结果
     */
    public boolean checkPhoneNumberUnique(MiniSpecialUser miniSpecialUser);

    /**
     * 校验车牌号是否唯一
     *
     * @param miniSpecialUser 特殊会员信息
     * @return 结果
     */
    public boolean checkPlateNoUnique(MiniSpecialUser miniSpecialUser);

    /**
     * 统计特殊会员数量按类型
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> countSpecialUsersByType();
}
