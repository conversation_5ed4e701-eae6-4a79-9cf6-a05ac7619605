package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.system.domain.SysArea;
import com.lgjy.system.service.ISysAreaService;

/**
 * 行政区域Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/area")
public class SysAreaController extends BaseController
{
    @Autowired
    private ISysAreaService sysAreaService;

    /**
     * 查询行政区域列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysArea sysArea)
    {
        startPage();
        List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
        return getDataTable(list);
    }

    /**
     * 根据父级代码查询子区域列表
     */
    @GetMapping("/children/{parentCode}")
    public AjaxResult getChildren(@PathVariable("parentCode") String parentCode)
    {
        List<SysArea> list = sysAreaService.selectSysAreaByParentCode(parentCode);
        return success(list);
    }

    /**
     * 根据区域级别查询区域列表
     */
    @GetMapping("/level/{areaLevel}")
    public AjaxResult getByLevel(@PathVariable("areaLevel") Integer areaLevel)
    {
        List<SysArea> list = sysAreaService.selectSysAreaByLevel(areaLevel);
        return success(list);
    }

    /**
     * 获取省份列表
     */
    @GetMapping("/provinces")
    public AjaxResult getProvinces()
    {
        List<SysArea> list = sysAreaService.selectSysAreaByLevel(1);
        return success(list);
    }

    /**
     * 根据省份代码获取城市列表
     */
    @GetMapping("/cities/{provinceCode}")
    public AjaxResult getCities(@PathVariable("provinceCode") String provinceCode)
    {
        List<SysArea> list = sysAreaService.selectSysAreaByParentCode(provinceCode);
        return success(list);
    }

    /**
     * 根据城市代码获取区县列表
     */
    @GetMapping("/districts/{cityCode}")
    public AjaxResult getDistricts(@PathVariable("cityCode") String cityCode)
    {
        List<SysArea> list = sysAreaService.selectSysAreaByParentCode(cityCode);
        return success(list);
    }

    /**
     * 导出行政区域列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysArea sysArea)
    {
        List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
        ExcelUtil<SysArea> util = new ExcelUtil<SysArea>(SysArea.class);
        util.exportExcel(response, list, "行政区域数据");
    }

    /**
     * 获取行政区域详细信息
     */
    @GetMapping(value = "/{areaCode}")
    public AjaxResult getInfo(@PathVariable("areaCode") String areaCode)
    {
        return success(sysAreaService.selectSysAreaByAreaCode(areaCode));
    }

    /**
     * 新增行政区域
     */
    @PostMapping
    public AjaxResult add(@RequestBody SysArea sysArea)
    {
        return toAjax(sysAreaService.insertSysArea(sysArea));
    }

    /**
     * 修改行政区域
     */
    @PutMapping
    public AjaxResult edit(@RequestBody SysArea sysArea)
    {
        return toAjax(sysAreaService.updateSysArea(sysArea));
    }

    /**
     * 删除行政区域
     */
    @DeleteMapping("/{areaCodes}")
    public AjaxResult remove(@PathVariable String[] areaCodes)
    {
        return toAjax(sysAreaService.deleteSysAreaByAreaCodes(areaCodes));
    }
}
