package com.lgjy.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.api.domain.WxUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.system.mapper.MiniSpecialUserMapper;
import com.lgjy.system.mapper.WxUserMapper;
import com.lgjy.system.domain.MiniSpecialUser;
import com.lgjy.system.service.IMiniSpecialUserService;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * 特殊会员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@Slf4j
public class MiniSpecialUserServiceImpl implements IMiniSpecialUserService {
    @Autowired
    private MiniSpecialUserMapper miniSpecialUserMapper;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询特殊会员
     * 
     * @param id 特殊会员主键
     * @return 特殊会员
     */
    @Override
    public MiniSpecialUser selectMiniSpecialUserById(Long id) {
        return miniSpecialUserMapper.selectMiniSpecialUserById(id);
    }

    /**
     * 查询特殊会员列表
     * 
     * @param miniSpecialUser 特殊会员
     * @return 特殊会员
     */
    @Override
    public List<MiniSpecialUser> selectMiniSpecialUserList(MiniSpecialUser miniSpecialUser) {
        return miniSpecialUserMapper.selectMiniSpecialUserList(miniSpecialUser);
    }

    /**
     * 新增特殊会员
     *
     * @param miniSpecialUser 特殊会员
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertMiniSpecialUser(MiniSpecialUser miniSpecialUser) {
        miniSpecialUser.setId(snowflakeIdGenerator.nextId());
        miniSpecialUser.setCreateBy(SecurityUtils.getUserId());
        int result = miniSpecialUserMapper.insertMiniSpecialUser(miniSpecialUser);

        if (result > 0) {
            // 更新wx_user表的user_type字段
            Integer userType = convertUserTypeToNumber(miniSpecialUser.getUserType());
            int updateResult = wxUserMapper.updateUserTypeByPhoneNumber(miniSpecialUser.getPhoneNumber(), userType);
            if (updateResult == 0) {
                throw new ServiceException("新增失败，手机号 " + miniSpecialUser.getPhoneNumber() + " 对应的小程序用户不存在");
            }
        }

        return result;
    }

    /**
     * 修改特殊会员
     *
     * @param miniSpecialUser 特殊会员
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMiniSpecialUser(MiniSpecialUser miniSpecialUser) {
        // 获取修改前的特殊会员信息
        MiniSpecialUser oldSpecialUser = miniSpecialUserMapper.selectMiniSpecialUserById(miniSpecialUser.getId());
        if (oldSpecialUser == null) {
            throw new ServiceException("特殊会员不存在");
        }

        String oldPhoneNumber = oldSpecialUser.getPhoneNumber();
        String newPhoneNumber = miniSpecialUser.getPhoneNumber();
        String oldUserType = oldSpecialUser.getUserType();
        String newUserType = miniSpecialUser.getUserType();

        boolean phoneNumberChanged = !oldPhoneNumber.equals(newPhoneNumber);
        boolean userTypeChanged = !oldUserType.equals(newUserType);

        miniSpecialUser.setUpdateBy(SecurityUtils.getUserId());
        int result = miniSpecialUserMapper.updateMiniSpecialUser(miniSpecialUser);

        if (result > 0) {
            // 如果手机号发生变化，需要同步更新相关表
            if (phoneNumberChanged) {
                // 1. 检查新手机号是否已被其他用户使用
                WxUser existingUser = wxUserMapper.selectUserByPhoneNumber(newPhoneNumber);
                if (existingUser != null) {
                    throw new ServiceException("修改失败，手机号 " + newPhoneNumber + " 已被其他用户使用");
                }

                // 2. 更新wx_user表的手机号（从旧手机号更新到新手机号）
                int phoneUpdateResult = updateWxUserPhoneNumber(oldPhoneNumber, newPhoneNumber);
                if (phoneUpdateResult == 0) {
                    throw new ServiceException("修改失败，更新用户手机号失败，原手机号 " + oldPhoneNumber + " 对应的用户不存在");
                }

                // 3. 更新特殊车辆表的手机号（处理156,256,356排序方式）
                updateSpecialUserCarPhoneNumbers(oldPhoneNumber, newPhoneNumber);
            }

            // 无论是否修改手机号，都需要更新用户类型（使用最终的手机号）
            if (userTypeChanged || phoneNumberChanged) {
                Integer userType = convertUserTypeToNumber(newUserType);
                int userTypeUpdateResult = wxUserMapper.updateUserTypeByPhoneNumber(newPhoneNumber, userType);
                if (userTypeUpdateResult == 0) {
                    throw new ServiceException("修改失败，更新用户类型失败，手机号 " + newPhoneNumber + " 对应的用户不存在");
                }
            }
        }

        return result;
    }

    /**
     * 批量删除特殊会员
     *
     * @param ids 需要删除的特殊会员主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMiniSpecialUserByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new ServiceException("删除的特殊会员ID不能为空");
        }

        // 一次性获取所有要删除的特殊会员信息，避免重复查询
        List<MiniSpecialUser> specialUsersToDelete = new ArrayList<>();
        List<String> phoneNumbers = new ArrayList<>();

        for (Long id : ids) {
            MiniSpecialUser specialUser = miniSpecialUserMapper.selectMiniSpecialUserById(id);
            if (specialUser == null) {
                throw new ServiceException("特殊会员ID[" + id + "]不存在，删除失败");
            }

            // 检查是否有特殊车辆
            int carCount = miniSpecialUserMapper.countSpecialUserCarsByUserId(id);
            if (carCount > 0) {
                throw new ServiceException(String.format("特殊会员[%s]下存在%d辆特殊车辆，不能删除。请先删除该用户的所有特殊车辆",
                    specialUser.getNickName(), carCount));
            }

            specialUsersToDelete.add(specialUser);
            if (StringUtils.isNotEmpty(specialUser.getPhoneNumber())) {
                phoneNumbers.add(specialUser.getPhoneNumber());
            }
        }

        // 执行批量删除
        int result = miniSpecialUserMapper.deleteMiniSpecialUserByIds(ids);

        // 检查删除结果是否符合预期
        if (result != ids.length) {
            throw new ServiceException(String.format("批量删除失败，预期删除%d条记录，实际删除%d条记录",
                ids.length, result));
        }

        // 删除成功后，重置用户类型
        for (String phoneNumber : phoneNumbers) {
            try {
                resetUserTypeIfNoActiveSpecialUser(phoneNumber);
            } catch (Exception e) {
                log.error("重置手机号{}的用户类型失败：{}", phoneNumber, e.getMessage());
                throw new ServiceException("删除特殊会员成功，但重置用户类型失败，手机号：" + phoneNumber);
            }
        }

        return result;
    }





    /**
     * 校验手机号是否唯一
     *
     * @param miniSpecialUser 特殊会员信息
     * @return 结果
     */
    @Override
    public boolean checkPhoneNumberUnique(MiniSpecialUser miniSpecialUser) {
        Long specialUserId = StringUtils.isNull(miniSpecialUser.getId()) ? -1L : miniSpecialUser.getId();
        MiniSpecialUser info = miniSpecialUserMapper.checkPhoneNumberUnique(miniSpecialUser.getPhoneNumber());
        if (StringUtils.isNotNull(info)&& info.getId().longValue() != specialUserId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 校验车牌号是否唯一
     *
     * @param miniSpecialUser 特殊会员信息
     * @return 结果
     */
    @Override
    public boolean checkPlateNoUnique(MiniSpecialUser miniSpecialUser) {
        if (StringUtils.isEmpty(miniSpecialUser.getPlateNo())) {
            return true; // 车牌号为空时不校验
        }
        Long specialUserId = StringUtils.isNull(miniSpecialUser.getId()) ? -1L : miniSpecialUser.getId();
        MiniSpecialUser info = miniSpecialUserMapper.checkPlateNoUnique(miniSpecialUser.getPlateNo());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != specialUserId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 统计特殊会员数量按类型
     *
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> countSpecialUsersByType() {
        return miniSpecialUserMapper.countSpecialUsersByType();
    }

    /**
     * 将特殊会员用户类型字符串转换为数字
     *
     * @param userTypeStr 用户类型字符串
     * @return 用户类型数字 0-普通用户 1-集团客户 2-VIP客户
     */
    private Integer convertUserTypeToNumber(String userTypeStr) {
        if (StringUtils.isEmpty(userTypeStr)) {
            return 0; // 默认普通用户
        }

        switch (userTypeStr) {
            case "VIP客户":
                return 2;
            case "集团客户":
                return 1;
            default:
                return 0; // 默认普通用户
        }
    }

    /**
     * 检查用户是否还有其他有效的特殊会员记录，如果没有则重置user_type为0
     *
     * @param phoneNumber 手机号
     * @throws ServiceException 重置失败时抛出异常
     */
    private void resetUserTypeIfNoActiveSpecialUser(String phoneNumber) {
        // 查询该手机号是否还有其他有效的特殊会员记录
        MiniSpecialUser queryParam = new MiniSpecialUser();
        queryParam.setPhoneNumber(phoneNumber);
        List<MiniSpecialUser> activeSpecialUsers = miniSpecialUserMapper.selectMiniSpecialUserList(queryParam);

        // 如果没有其他有效的特殊会员记录，重置wx_user的user_type为0
        if (activeSpecialUsers == null || activeSpecialUsers.isEmpty()) {
            int updateResult = wxUserMapper.updateUserTypeByPhoneNumber(phoneNumber, 0);
            if (updateResult > 0) {
                log.info("已重置手机号{}的用户类型为普通用户", phoneNumber);
            } else {
                throw new ServiceException("重置用户类型失败，手机号 " + phoneNumber + " 对应的用户不存在");
            }
        } else {
            log.info("手机号{}仍有其他有效的特殊会员记录，不重置用户类型", phoneNumber);
        }
    }

    /**
     * 更新wx_user表的手机号
     *
     * @param oldPhoneNumber 旧手机号
     * @param newPhoneNumber 新手机号
     * @return 受影响的行数
     */
    private int updateWxUserPhoneNumber(String oldPhoneNumber, String newPhoneNumber) {
        try {
            int updateCount = wxUserMapper.updatePhoneNumberByOldPhone(oldPhoneNumber, newPhoneNumber);
            if (updateCount > 0) {
                log.info("成功更新wx_user表手机号：{} -> {}", oldPhoneNumber, newPhoneNumber);
            }
            return updateCount;
        } catch (Exception e) {
            log.error("更新wx_user表手机号失败：{} -> {}，原因：{}", oldPhoneNumber, newPhoneNumber, e.getMessage());
            throw new ServiceException("更新用户手机号失败：" + e.getMessage());
        }
    }

    /**
     * 更新特殊车辆表的手机号（处理156,256,356排序方式）
     *
     * @param oldPhoneNumber 旧手机号
     * @param newPhoneNumber 新手机号
     */
    private void updateSpecialUserCarPhoneNumbers(String oldPhoneNumber, String newPhoneNumber) {
        try {
            // 获取旧手机号的后10位
            String oldPhoneSuffix = oldPhoneNumber.length() >= 10 ?
                oldPhoneNumber.substring(oldPhoneNumber.length() - 10) : oldPhoneNumber;
            String newPhoneSuffix = newPhoneNumber.length() >= 10 ?
                newPhoneNumber.substring(newPhoneNumber.length() - 10) : newPhoneNumber;

            // 更新所有匹配的特殊车辆记录（包括156,256,356等变体）
            int updateCount = miniSpecialUserMapper.updateSpecialUserCarPhoneNumbers(oldPhoneSuffix, newPhoneSuffix);
            if (updateCount > 0) {
                log.info("成功更新{}条特殊车辆记录的手机号：{} -> {}", updateCount, oldPhoneNumber, newPhoneNumber);
            }
        } catch (Exception e) {
            log.error("更新特殊车辆表手机号失败：{} -> {}，原因：{}", oldPhoneNumber, newPhoneNumber, e.getMessage());
            throw new ServiceException("更新特殊车辆信息失败：" + e.getMessage());
        }
    }
}
