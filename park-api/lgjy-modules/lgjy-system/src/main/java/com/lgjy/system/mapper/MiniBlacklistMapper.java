package com.lgjy.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.lgjy.system.domain.MiniBlacklist;

/**
 * 黑名单管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface MiniBlacklistMapper {
    /**
     * 查询黑名单管理
     * 
     * @param id 黑名单管理主键
     * @return 黑名单管理
     */
    public MiniBlacklist selectMiniBlacklistById(Long id);

    /**
     * 查询黑名单管理列表
     * 
     * @param miniBlacklist 黑名单管理
     * @return 黑名单管理集合
     */
    public List<MiniBlacklist> selectMiniBlacklistList(MiniBlacklist miniBlacklist);

    /**
     * 新增黑名单管理
     * 
     * @param miniBlacklist 黑名单管理
     * @return 结果
     */
    public int insertMiniBlacklist(MiniBlacklist miniBlacklist);

    /**
     * 修改黑名单管理
     * 
     * @param miniBlacklist 黑名单管理
     * @return 结果
     */
    public int updateMiniBlacklist(MiniBlacklist miniBlacklist);

    /**
     * 删除黑名单管理
     * 
     * @param id 黑名单管理主键
     * @return 结果
     */
    public int deleteMiniBlacklistById(Long id);

    /**
     * 批量删除黑名单管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniBlacklistByIds(Long[] ids);

    /**
     * 根据车牌号查询黑名单信息
     * 
     * @param plateNo 车牌号
     * @return 黑名单信息
     */
    public MiniBlacklist selectMiniBlacklistByPlateNo(String plateNo);

    /**
     * 根据车牌号和场库ID查询黑名单信息
     *
     * @param plateNo     车牌号
     * @param warehouseId 场库ID
     * @return 黑名单信息
     */
    public MiniBlacklist selectMiniBlacklistByPlateNoAndWarehouse(@Param("plateNo") String plateNo,
            @Param("warehouseId") Long warehouseId);

    /**
     * 校验车牌号是否唯一
     *
     * @param plateNo     车牌号
     * @param warehouseId 场库ID
     * @return 结果
     */
    public MiniBlacklist checkPlateNoUnique(@Param("plateNo") String plateNo, @Param("warehouseId") Long warehouseId);
}
