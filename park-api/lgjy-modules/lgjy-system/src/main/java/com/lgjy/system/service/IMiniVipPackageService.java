package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.MiniVipPackage;
import com.lgjy.system.domain.MiniWarehouse;

/**
 * 会员套餐配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMiniVipPackageService {
    /**
     * 查询会员套餐配置
     * 
     * @param id 会员套餐配置主键
     * @return 会员套餐配置
     */
    public MiniVipPackage selectMiniVipPackageById(Long id);

    /**
     * 查询会员套餐配置列表
     * 
     * @param miniVipPackage 会员套餐配置
     * @return 会员套餐配置集合
     */
    public List<MiniVipPackage> selectMiniVipPackageList(MiniVipPackage miniVipPackage);

    /**
     * 新增会员套餐配置
     * 
     * @param miniVipPackage 会员套餐配置
     * @return 结果
     */
    public int insertMiniVipPackage(MiniVipPackage miniVipPackage);

    /**
     * 修改会员套餐配置
     * 
     * @param miniVipPackage 会员套餐配置
     * @return 结果
     */
    public int updateMiniVipPackage(MiniVipPackage miniVipPackage);

    /**
     * 批量删除会员套餐配置
     * 
     * @param ids 需要删除的会员套餐配置主键集合
     * @return 结果
     */
    public int deleteMiniVipPackageByIds(Long[] ids);

    /**
     * 删除会员套餐配置信息
     * 
     * @param id 会员套餐配置主键
     * @return 结果
     */
    public int deleteMiniVipPackageById(Long id);

    /**
     * 根据场库ID查询套餐列表
     *
     * @param warehouseId 场库ID
     * @return 套餐列表
     */
    public List<MiniVipPackage> selectPackagesByWarehouseId(Long warehouseId);

    /**
     * 获取所有场库选项（包含层级关系）
     *
     * @return 场库列表
     */
    public List<MiniWarehouse> selectAllWarehouseOptions();
}
