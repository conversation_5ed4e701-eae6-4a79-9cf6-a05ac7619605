package com.lgjy.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆出入场记录对象 gate_parking_info
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GateParkingInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 停车场ID */
    @Excel(name = "停车场ID")
    private String parkingId;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String parkingName;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNum;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String carType;

    /** 入场时间 */
    @Excel(name = "入场时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String inTime;

    /** 入场时间（转换后的日期格式） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inDateTime;

    /** 入场通道ID */
    @Excel(name = "入场通道ID")
    private String inChannelId;

    /** 入场通道名称 */
    @Excel(name = "入场通道")
    private String inChannelName;

    /** 入场照片 */
    @Excel(name = "入场照片")
    private String inPic;

    /** 出场时间 */
    @Excel(name = "出场时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String outTime;

    /** 出场时间（转换后的日期格式） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outDateTime;

    /** 出场通道ID */
    @Excel(name = "出场通道ID")
    private String outChannelId;

    /** 出场通道名称 */
    @Excel(name = "出场通道")
    private String outChannelName;

    /** 出场照片 */
    @Excel(name = "出场照片")
    private String outPic;

    /** 停车费用 */
    @Excel(name = "停车费用")
    private BigDecimal money;

    /** 支付类型 */
    @Excel(name = "支付类型", readConverterExp = "1=现金,2=支付宝,3=微信,4=银联,5=免费")
    private String payType;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=在场,1=已出场")
    private Integer status;

    /** 最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "最后更新时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdate;

    /** 停车时长（分钟） */
    private Long parkingDuration;

    /** 停车时长（格式化显示） */
    private String parkingDurationText;

    // 查询条件字段
    /** 入场开始时间 */
    private String inBeginTime;

    /** 入场结束时间 */
    private String inEndTime;

    /** 出场开始时间 */
    private String outBeginTime;

    /** 出场结束时间 */
    private String outEndTime;

    @Override
    public String toString() {
        return "GateParkingInfo{" +
                "id='" + id + '\'' +
                ", parkingId='" + parkingId + '\'' +
                ", parkingName='" + parkingName + '\'' +
                ", plateNum='" + plateNum + '\'' +
                ", carType='" + carType + '\'' +
                ", inTime='" + inTime + '\'' +
                ", inDateTime=" + inDateTime +
                ", inChannelId='" + inChannelId + '\'' +
                ", inChannelName='" + inChannelName + '\'' +
                ", inPic='" + inPic + '\'' +
                ", outTime='" + outTime + '\'' +
                ", outDateTime=" + outDateTime +
                ", outChannelId='" + outChannelId + '\'' +
                ", outChannelName='" + outChannelName + '\'' +
                ", outPic='" + outPic + '\'' +
                ", money=" + money +
                ", payType='" + payType + '\'' +
                ", status=" + status +
                ", lastUpdate=" + lastUpdate +
                ", parkingDuration=" + parkingDuration +
                ", parkingDurationText='" + parkingDurationText + '\'' +
                '}';
    }
}
