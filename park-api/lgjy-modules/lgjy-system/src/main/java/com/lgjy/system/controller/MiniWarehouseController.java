package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniWarehouse;
import com.lgjy.system.domain.MiniOperator;
import com.lgjy.system.service.IMiniWarehouseService;
import com.lgjy.system.service.IMiniOperatorService;

/**
 * 场库信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/platform/warehouse")
public class MiniWarehouseController extends BaseController {
    @Autowired
    private IMiniWarehouseService miniWarehouseService;

    @Autowired
    private IMiniOperatorService miniOperatorService;

    /**
     * 查询场库信息列表
     */
    @RequiresPermissions("platform:warehouse:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniWarehouse miniWarehouse) {
        startPage();
        List<MiniWarehouse> list = miniWarehouseService.selectMiniWarehouseList(miniWarehouse);
        return getDataTable(list);
    }

    /**
     * 查询场库树形结构列表
     */
    @RequiresPermissions("platform:warehouse:list")
    @GetMapping("/treeList")
    public AjaxResult treeList(MiniWarehouse miniWarehouse) {
        List<MiniWarehouse> list = miniWarehouseService.selectWarehouseTreeList(miniWarehouse);
        return success(list);
    }

    /**
     * 导出场库信息列表
     */
    @RequiresPermissions("platform:warehouse:export")
    @Log(title = "场库信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniWarehouse miniWarehouse) {
        List<MiniWarehouse> list = miniWarehouseService.selectMiniWarehouseList(miniWarehouse);
        ExcelUtil<MiniWarehouse> util = new ExcelUtil<MiniWarehouse>(MiniWarehouse.class);
        util.exportExcel(response, list, "场库信息数据");
    }

    /**
     * 获取场库信息详细信息
     */
    @RequiresPermissions("platform:warehouse:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("data", miniWarehouseService.selectMiniWarehouseById(id));
        ajax.put("operators", miniOperatorService.selectMiniOperatorAll());
        return ajax;
    }

    /**
     * 新增场库信息
     */
    @RequiresPermissions("platform:warehouse:add")
    @Log(title = "场库信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniWarehouse miniWarehouse) {
        return toAjax(miniWarehouseService.insertMiniWarehouse(miniWarehouse));
    }

    /**
     * 修改场库信息
     */
    @RequiresPermissions("platform:warehouse:edit")
    @Log(title = "场库信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniWarehouse miniWarehouse) {
        return toAjax(miniWarehouseService.updateMiniWarehouse(miniWarehouse));
    }

    /**
     * 删除场库信息
     */
    @RequiresPermissions("platform:warehouse:remove")
    @Log(title = "场库信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniWarehouseService.deleteMiniWarehouseByIds(ids));
    }

    /**
     * 获取场库下拉列表
     */
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect() {
        List<MiniWarehouse> warehouses = miniWarehouseService.selectMiniWarehouseAll();
        return success(warehouses);
    }

    /**
     * 根据运营商ID获取场库下拉列表
     */
    @GetMapping("/optionSelectByOperator")
    public AjaxResult optionSelectByOperator(@RequestParam("operatorId") Long operatorId) {
        List<MiniWarehouse> warehouses = miniWarehouseService.selectMiniWarehouseByOperatorId(operatorId);
        return success(warehouses);
    }

    /**
     * 获取子场库下拉列表
     */
    @GetMapping("/optionSelectChildWarehouse")
    public AjaxResult optionSelectChildWarehouse() {
        List<MiniWarehouse> childWarehouses = miniWarehouseService.selectChildWarehouseOptions();
        return success(childWarehouses);
    }

    /**
     * 根据父场库ID获取子场库下拉列表
     */
    @GetMapping("/optionSelectChildWarehouseByParent")
    public AjaxResult optionSelectChildWarehouseByParent(@RequestParam("parentId") Long parentId) {
        List<MiniWarehouse> childWarehouses = miniWarehouseService.selectChildWarehouseOptionsByParentId(parentId);
        return success(childWarehouses);
    }

    /**
     * 检查场库的子场库数量
     */
    @GetMapping("/checkChildWarehouseCount/{warehouseId}")
    public AjaxResult checkChildWarehouseCount(@PathVariable("warehouseId") Long warehouseId) {
        int count = miniWarehouseService.countChildWarehousesByParentId(warehouseId);
        return success(count);
    }
}
