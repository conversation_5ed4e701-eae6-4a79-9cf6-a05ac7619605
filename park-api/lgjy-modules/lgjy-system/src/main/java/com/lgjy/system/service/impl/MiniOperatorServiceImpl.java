package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.system.mapper.MiniOperatorMapper;
import com.lgjy.system.mapper.MiniWarehouseMapper;
import com.lgjy.system.domain.MiniOperator;
import com.lgjy.system.service.IMiniOperatorService;
import com.lgjy.common.core.exception.ServiceException;

/**
 * 运营商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniOperatorServiceImpl implements IMiniOperatorService {
    @Autowired
    private MiniOperatorMapper miniOperatorMapper;

    @Autowired
    private MiniWarehouseMapper miniWarehouseMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询运营商信息
     *
     * @param id 运营商信息主键
     * @return 运营商信息
     */
    @Override
    public MiniOperator selectMiniOperatorById(Long id) {
        return miniOperatorMapper.selectMiniOperatorById(id);
    }

    /**
     * 查询运营商信息列表
     *
     * @param miniOperator 运营商信息
     * @return 运营商信息
     */
    @Override
    public List<MiniOperator> selectMiniOperatorList(MiniOperator miniOperator) {
        return miniOperatorMapper.selectMiniOperatorList(miniOperator);
    }

    /**
     * 新增运营商信息
     *
     * @param miniOperator 运营商信息
     * @return 结果
     */
    @Override
    public int insertMiniOperator(MiniOperator miniOperator) {
        miniOperator.setCreateBy(SecurityUtils.getUserId());
        miniOperator.setUserId(SecurityUtils.getUserId()); // 设置创建者用户ID
        miniOperator.setDeleteFlag(0);
        return miniOperatorMapper.insertMiniOperator(miniOperator);
    }

    /**
     * 修改运营商信息
     *
     * @param miniOperator 运营商信息
     * @return 结果
     */
    @Override
    public int updateMiniOperator(MiniOperator miniOperator) {
        miniOperator.setUpdateBy(SecurityUtils.getUserId());
        return miniOperatorMapper.updateMiniOperator(miniOperator);
    }

    /**
     * 批量删除运营商信息
     *
     * @param ids 需要删除的运营商信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniOperatorByIds(Long[] ids) {
        for (Long id : ids) {
            // 检查是否有关联的场库
            int warehouseCount = miniWarehouseMapper.countWarehouseByOperatorId(id);
            if (warehouseCount > 0) {
                MiniOperator operator = selectMiniOperatorById(id);
                throw new ServiceException(String.format("运营商[%s]下存在场库，不能删除", operator.getCompanyName()));
            }
        }
        return miniOperatorMapper.deleteMiniOperatorByIds(ids);
    }

    /**
     * 删除运营商信息信息
     *
     * @param id 运营商信息主键
     * @return 结果
     */
    @Override
    public int deleteMiniOperatorById(Long id) {
        // 检查是否有关联的场库
        int warehouseCount = miniWarehouseMapper.countWarehouseByOperatorId(id);
        if (warehouseCount > 0) {
            MiniOperator operator = selectMiniOperatorById(id);
            throw new ServiceException(String.format("运营商[%s]下存在场库，不能删除", operator.getCompanyName()));
        }
        return miniOperatorMapper.deleteMiniOperatorById(id);
    }

    /**
     * 校验运营商公司名称是否唯一
     *
     * @param miniOperator 运营商信息
     * @return 结果
     */
    @Override
    public boolean checkCompanyNameUnique(MiniOperator miniOperator) {
        Long id = StringUtils.isNull(miniOperator.getId()) ? -1L : miniOperator.getId();
        MiniOperator info = miniOperatorMapper.checkCompanyNameUnique(miniOperator.getCompanyName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查询所有运营商列表（用于下拉选择）
     *
     * @return 运营商列表
     */
    @Override
    public List<MiniOperator> selectMiniOperatorAll() {
        return miniOperatorMapper.selectMiniOperatorAll();
    }
}
