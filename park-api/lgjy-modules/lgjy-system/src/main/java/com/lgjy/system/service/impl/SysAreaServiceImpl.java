package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.SysAreaMapper;
import com.lgjy.system.domain.SysArea;
import com.lgjy.system.service.ISysAreaService;

/**
 * 行政区域Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class SysAreaServiceImpl implements ISysAreaService 
{
    @Autowired
    private SysAreaMapper sysAreaMapper;

    /**
     * 查询行政区域
     * 
     * @param areaCode 行政区域主键
     * @return 行政区域
     */
    @Override
    public SysArea selectSysAreaByAreaCode(String areaCode)
    {
        return sysAreaMapper.selectSysAreaByAreaCode(areaCode);
    }

    /**
     * 查询行政区域列表
     * 
     * @param sysArea 行政区域
     * @return 行政区域
     */
    @Override
    public List<SysArea> selectSysAreaList(SysArea sysArea)
    {
        return sysAreaMapper.selectSysAreaList(sysArea);
    }

    /**
     * 根据父级代码查询子区域列表
     * 
     * @param parentCode 父级代码
     * @return 子区域集合
     */
    @Override
    public List<SysArea> selectSysAreaByParentCode(String parentCode)
    {
        return sysAreaMapper.selectSysAreaByParentCode(parentCode);
    }

    /**
     * 根据区域级别查询区域列表
     * 
     * @param areaLevel 区域级别
     * @return 区域集合
     */
    @Override
    public List<SysArea> selectSysAreaByLevel(Integer areaLevel)
    {
        return sysAreaMapper.selectSysAreaByLevel(areaLevel);
    }

    /**
     * 新增行政区域
     * 
     * @param sysArea 行政区域
     * @return 结果
     */
    @Override
    public int insertSysArea(SysArea sysArea)
    {
        return sysAreaMapper.insertSysArea(sysArea);
    }

    /**
     * 修改行政区域
     * 
     * @param sysArea 行政区域
     * @return 结果
     */
    @Override
    public int updateSysArea(SysArea sysArea)
    {
        return sysAreaMapper.updateSysArea(sysArea);
    }

    /**
     * 批量删除行政区域
     * 
     * @param areaCodes 需要删除的行政区域主键
     * @return 结果
     */
    @Override
    public int deleteSysAreaByAreaCodes(String[] areaCodes)
    {
        return sysAreaMapper.deleteSysAreaByAreaCodes(areaCodes);
    }

    /**
     * 删除行政区域信息
     * 
     * @param areaCode 行政区域主键
     * @return 结果
     */
    @Override
    public int deleteSysAreaByAreaCode(String areaCode)
    {
        return sysAreaMapper.deleteSysAreaByAreaCode(areaCode);
    }
}
