package com.lgjy.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.lgjy.system.domain.MiniWhitelist;

/**
 * 白名单管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface MiniWhitelistMapper {
    /**
     * 查询白名单管理
     * 
     * @param id 白名单管理主键
     * @return 白名单管理
     */
    public MiniWhitelist selectMiniWhitelistById(Long id);

    /**
     * 查询白名单管理列表
     * 
     * @param miniWhitelist 白名单管理
     * @return 白名单管理集合
     */
    public List<MiniWhitelist> selectMiniWhitelistList(MiniWhitelist miniWhitelist);

    /**
     * 新增白名单管理
     * 
     * @param miniWhitelist 白名单管理
     * @return 结果
     */
    public int insertMiniWhitelist(MiniWhitelist miniWhitelist);

    /**
     * 修改白名单管理
     * 
     * @param miniWhitelist 白名单管理
     * @return 结果
     */
    public int updateMiniWhitelist(MiniWhitelist miniWhitelist);

    /**
     * 删除白名单管理
     * 
     * @param id 白名单管理主键
     * @return 结果
     */
    public int deleteMiniWhitelistById(Long id);

    /**
     * 批量删除白名单管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniWhitelistByIds(Long[] ids);

    /**
     * 根据车牌号查询白名单信息
     * 
     * @param plateNo 车牌号
     * @return 白名单信息
     */
    public MiniWhitelist selectMiniWhitelistByPlateNo(String plateNo);

    /**
     * 根据车牌号和场库ID查询白名单信息
     *
     * @param plateNo     车牌号
     * @param warehouseId 场库ID
     * @return 白名单信息
     */
    public MiniWhitelist selectMiniWhitelistByPlateNoAndWarehouse(@Param("plateNo") String plateNo,
            @Param("warehouseId") Long warehouseId);

    /**
     * 校验车牌号是否唯一
     *
     * @param plateNo     车牌号
     * @param warehouseId 场库ID
     * @return 结果
     */
    public MiniWhitelist checkPlateNoUnique(@Param("plateNo") String plateNo, @Param("warehouseId") Long warehouseId);
}
