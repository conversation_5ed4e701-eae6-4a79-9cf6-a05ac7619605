package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.MiniWarehouseManager;
import com.lgjy.system.service.IMiniWarehouseManagerService;
import com.lgjy.system.service.IMiniOperatorService;
import com.lgjy.system.service.IMiniWarehouseService;

/**
 * 场库管理人员信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/platform/warehouseManager")
public class MiniWarehouseManagerController extends BaseController {
    @Autowired
    private IMiniWarehouseManagerService miniWarehouseManagerService;

    @Autowired
    private IMiniOperatorService miniOperatorService;

    @Autowired
    private IMiniWarehouseService miniWarehouseService;

    /**
     * 查询场库管理人员信息列表
     */
    @RequiresPermissions("platform:warehouseManager:list")
    @GetMapping("/list")
    public TableDataInfo list(MiniWarehouseManager miniWarehouseManager) {
        startPage();
        List<MiniWarehouseManager> list = miniWarehouseManagerService
                .selectMiniWarehouseManagerList(miniWarehouseManager);
        return getDataTable(list);
    }

    /**
     * 导出场库管理人员信息列表
     */
    @RequiresPermissions("platform:warehouseManager:export")
    @Log(title = "场库管理人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniWarehouseManager miniWarehouseManager) {
        List<MiniWarehouseManager> list = miniWarehouseManagerService
                .selectMiniWarehouseManagerList(miniWarehouseManager);
        ExcelUtil<MiniWarehouseManager> util = new ExcelUtil<MiniWarehouseManager>(MiniWarehouseManager.class);
        util.exportExcel(response, list, "场库管理人员信息数据");
    }

    /**
     * 获取场库管理人员信息详细信息
     */
    @RequiresPermissions("platform:warehouseManager:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("data", miniWarehouseManagerService.selectMiniWarehouseManagerById(id));
        ajax.put("operators", miniOperatorService.selectMiniOperatorAll());
        ajax.put("warehouses", miniWarehouseService.selectMiniWarehouseAll());
        return ajax;
    }

    /**
     * 新增场库管理人员信息
     */
    @RequiresPermissions("platform:warehouseManager:add")
    @Log(title = "场库管理人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MiniWarehouseManager miniWarehouseManager) {
        return toAjax(miniWarehouseManagerService.insertMiniWarehouseManager(miniWarehouseManager));
    }

    /**
     * 修改场库管理人员信息
     */
    @RequiresPermissions("platform:warehouseManager:edit")
    @Log(title = "场库管理人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MiniWarehouseManager miniWarehouseManager) {
        return toAjax(miniWarehouseManagerService.updateMiniWarehouseManager(miniWarehouseManager));
    }

    /**
     * 删除场库管理人员信息
     */
    @RequiresPermissions("platform:warehouseManager:remove")
    @Log(title = "场库管理人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(miniWarehouseManagerService.deleteMiniWarehouseManagerByIds(ids));
    }
}
