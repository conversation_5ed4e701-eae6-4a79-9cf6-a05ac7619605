package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.GateParkingInfo;
import com.lgjy.system.service.IGateParkingInfoService;
import com.lgjy.system.service.IMiniWarehouseService;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 车辆出入场记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/gateParkingInfo")
public class GateParkingInfoController extends BaseController {
    @Autowired
    private IGateParkingInfoService gateParkingInfoService;

    @Autowired
    private IMiniWarehouseService miniWarehouseService;

    /**
     * 查询车辆出入场记录列表
     */
    @RequiresPermissions("order:gateParkingInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(GateParkingInfo gateParkingInfo) {
        startPage();
        List<GateParkingInfo> list = gateParkingInfoService.selectGateParkingInfoList(gateParkingInfo);
        return getDataTable(list);
    }

    /**
     * 导出车辆出入场记录列表
     */
    @RequiresPermissions("order:gateParkingInfo:export")
    @Log(title = "车辆出入场记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GateParkingInfo gateParkingInfo) {
        List<GateParkingInfo> list = gateParkingInfoService.selectGateParkingInfoList(gateParkingInfo);
        ExcelUtil<GateParkingInfo> util = new ExcelUtil<GateParkingInfo>(GateParkingInfo.class);
        util.exportExcel(response, list, "车辆出入场记录数据");
    }

    /**
     * 获取车辆出入场记录详细信息
     */
    @RequiresPermissions("order:gateParkingInfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        AjaxResult ajax = AjaxResult.success();
        GateParkingInfo info = gateParkingInfoService.selectGateParkingInfoById(id);
        ajax.put("data", info);
        ajax.put("warehouses", miniWarehouseService.selectMiniWarehouseAll());
        return ajax;
    }

    /**
     * 新增车辆出入场记录
     */
    @RequiresPermissions("order:gateParkingInfo:add")
    @Log(title = "车辆出入场记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GateParkingInfo gateParkingInfo) {
        return toAjax(gateParkingInfoService.insertGateParkingInfo(gateParkingInfo));
    }

    /**
     * 修改车辆出入场记录
     */
    @RequiresPermissions("order:gateParkingInfo:edit")
    @Log(title = "车辆出入场记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GateParkingInfo gateParkingInfo) {
        return toAjax(gateParkingInfoService.updateGateParkingInfo(gateParkingInfo));
    }

    /**
     * 删除车辆出入场记录
     */
    @RequiresPermissions("order:gateParkingInfo:remove")
    @Log(title = "车辆出入场记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(gateParkingInfoService.deleteGateParkingInfoByIds(ids));
    }

    /**
     * 根据车牌号查询车辆出入场记录
     */
    @RequiresPermissions("order:gateParkingInfo:query")
    @GetMapping("/plateNum/{plateNum}")
    public AjaxResult getByPlateNum(@PathVariable("plateNum") String plateNum) {
        List<GateParkingInfo> list = gateParkingInfoService.selectGateParkingInfoByPlateNum(plateNum);
        return AjaxResult.success(list);
    }

    /**
     * 根据停车场ID查询车辆出入场记录
     */
    @RequiresPermissions("order:gateParkingInfo:query")
    @GetMapping("/parkingId/{parkingId}")
    public AjaxResult getByParkingId(@PathVariable("parkingId") String parkingId) {
        List<GateParkingInfo> list = gateParkingInfoService.selectGateParkingInfoByParkingId(parkingId);
        return AjaxResult.success(list);
    }

    /**
     * 根据状态查询车辆出入场记录
     */
    @RequiresPermissions("order:gateParkingInfo:query")
    @GetMapping("/status/{status}")
    public AjaxResult getByStatus(@PathVariable("status") Integer status) {
        List<GateParkingInfo> list = gateParkingInfoService.selectGateParkingInfoByStatus(status);
        return AjaxResult.success(list);
    }

    /**
     * 统计车辆出入场记录数量
     */
    @RequiresPermissions("order:gateParkingInfo:query")
    @GetMapping("/count")
    public AjaxResult count(GateParkingInfo gateParkingInfo) {
        int count = gateParkingInfoService.countGateParkingInfo(gateParkingInfo);
        return AjaxResult.success(count);
    }

    /**
     * 获取车辆类型选项
     */
    @GetMapping("/carTypeOptions")
    public AjaxResult getCarTypeOptions() {
        List<String> carTypes = gateParkingInfoService.selectCarTypeOptions();
        return AjaxResult.success(carTypes);
    }

    /**
     * 获取支付类型选项
     */
    @GetMapping("/payTypeOptions")
    public AjaxResult getPayTypeOptions() {
        List<String> payTypes = gateParkingInfoService.selectPayTypeOptions();
        return AjaxResult.success(payTypes);
    }

    /**
     * 获取通道名称选项
     */
    @GetMapping("/channelNameOptions")
    public AjaxResult getChannelNameOptions() {
        List<String> channelNames = gateParkingInfoService.selectChannelNameOptions();
        return AjaxResult.success(channelNames);
    }
}
