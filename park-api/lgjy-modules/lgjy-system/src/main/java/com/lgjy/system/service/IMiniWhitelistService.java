package com.lgjy.system.service;

import java.util.List;
import com.lgjy.system.domain.MiniWhitelist;

/**
 * 白名单管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IMiniWhitelistService 
{
    /**
     * 查询白名单管理
     * 
     * @param id 白名单管理主键
     * @return 白名单管理
     */
    public MiniWhitelist selectMiniWhitelistById(Long id);

    /**
     * 查询白名单管理列表
     * 
     * @param miniWhitelist 白名单管理
     * @return 白名单管理集合
     */
    public List<MiniWhitelist> selectMiniWhitelistList(MiniWhitelist miniWhitelist);

    /**
     * 新增白名单管理
     * 
     * @param miniWhitelist 白名单管理
     * @return 结果
     */
    public int insertMiniWhitelist(MiniWhitelist miniWhitelist);

    /**
     * 修改白名单管理
     * 
     * @param miniWhitelist 白名单管理
     * @return 结果
     */
    public int updateMiniWhitelist(MiniWhitelist miniWhitelist);

    /**
     * 批量删除白名单管理
     * 
     * @param ids 需要删除的白名单管理主键集合
     * @return 结果
     */
    public int deleteMiniWhitelistByIds(Long[] ids);

    /**
     * 删除白名单管理信息
     * 
     * @param id 白名单管理主键
     * @return 结果
     */
    public int deleteMiniWhitelistById(Long id);

    /**
     * 根据车牌号查询白名单信息
     * 
     * @param plateNo 车牌号
     * @return 白名单信息
     */
    public MiniWhitelist selectMiniWhitelistByPlateNo(String plateNo);

    /**
     * 根据车牌号和场库ID查询白名单信息
     * 
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 白名单信息
     */
    public MiniWhitelist selectMiniWhitelistByPlateNoAndWarehouse(String plateNo, Long warehouseId);

    /**
     * 校验车牌号是否唯一
     *
     * @param plateNo 车牌号
     * @param warehouseId 场库ID
     * @return 结果
     */
    public String checkPlateNoUnique(String plateNo, Long warehouseId);

    /**
     * 新增白名单管理（包含推送到道闸系统）
     *
     * @param miniWhitelist 白名单管理对象
     * @return 结果
     */
    public int addWhitelistWithPush(MiniWhitelist miniWhitelist);

    /**
     * 修改白名单管理（包含推送到道闸系统）
     *
     * @param miniWhitelist 白名单管理对象
     * @return 结果
     */
    public int updateWhitelistWithPush(MiniWhitelist miniWhitelist);

    /**
     * 删除白名单管理（包含从道闸系统删除）
     *
     * @param id 需要删除的白名单管理主键
     * @return 结果
     */
    public int removeWhitelistWithPush(Long id);
}
