package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.MiniOrderRefundMapper;
import com.lgjy.system.domain.MiniOrderRefund;
import com.lgjy.system.service.IMiniOrderRefundService;

/**
 * 退款管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class MiniOrderRefundServiceImpl implements IMiniOrderRefundService 
{
    @Autowired
    private MiniOrderRefundMapper miniOrderRefundMapper;

    /**
     * 查询退款记录
     * 
     * @param id 退款记录主键
     * @return 退款记录
     */
    @Override
    public MiniOrderRefund selectMiniOrderRefundById(Long id)
    {
        return miniOrderRefundMapper.selectMiniOrderRefundById(id);
    }

    /**
     * 查询退款记录列表
     * 
     * @param miniOrderRefund 退款记录
     * @return 退款记录
     */
    @Override
    public List<MiniOrderRefund> selectMiniOrderRefundList(MiniOrderRefund miniOrderRefund)
    {
        return miniOrderRefundMapper.selectMiniOrderRefundList(miniOrderRefund);
    }

    /**
     * 根据订单号查询退款记录
     * 
     * @param tradeId 订单号
     * @return 退款记录列表
     */
    @Override
    public List<MiniOrderRefund> selectMiniOrderRefundByTradeId(String tradeId)
    {
        return miniOrderRefundMapper.selectMiniOrderRefundByTradeId(tradeId);
    }
}
