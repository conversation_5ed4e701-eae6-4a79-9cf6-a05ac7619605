package com.lgjy.system.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 场库信息对象 mini_warehouse
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class MiniWarehouse extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 所属运营商ID */
    @Excel(name = "所属运营商ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /** 所属运营商名称 */
    @Excel(name = "所属运营商")
    private String operatorName;

    /** 智能类型 */
    @Excel(name = "智能类型", readConverterExp = "1=普通停车场,2=智能停车场,3=无人值守")
    private Integer smartsType;

    /** 总停车位数量 */
    @Excel(name = "总停车位数量")
    private Integer totalParking;

    /** 省份代码 */
    @Excel(name = "省份代码")
    private String provinceCode;

    /** 城市代码 */
    @Excel(name = "城市代码")
    private String cityCode;

    /** 区域代码 */
    @Excel(name = "区域代码")
    private String areaCode;

    /** 省份名称 */
    @Excel(name = "省份名称")
    private String provinceName;

    /** 城市名称 */
    @Excel(name = "城市名称")
    private String cityName;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String areaName;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=停用,1=正常")
    private Integer status;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 删除标识 0-未删除 1-已删除 */
    private Integer deleteFlag;

    /** 轮播图片(JSON格式) */
    @Excel(name = "轮播图片")
    private String carouselImages;

    /** 父级ID，0表示顶级场库 */
    @Excel(name = "父级ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 楼层信息（如：B1、B2、1F、2F等） */
    @Excel(name = "楼层信息")
    private String floorInfo;

    /** 创建者姓名 */
    @Excel(name = "创建者")
    private String createdBy;

    /** 更新者姓名 */
    @Excel(name = "更新者")
    private String updatedBy;

    /** 租赁物业编号 */
    @Excel(name = "租赁物业编号")
    private String leasePropertyNo;

    /** 租赁地址 */
    @Excel(name = "租赁地址")
    private String leaseAddress;

    /** 租赁详细地址 */
    @Excel(name = "租赁详细地址")
    private String leaseDetailAddress;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    /** 子场库/停车场 */
    private List<MiniWarehouse> children = new ArrayList<MiniWarehouse>();

    public List<MiniWarehouse> getChildren() {
        return children;
    }

    public void setChildren(List<MiniWarehouse> children) {
        this.children = children;
    }

    public String getLeasePropertyNo() {
        return leasePropertyNo;
    }

    public void setLeasePropertyNo(String leasePropertyNo) {
        this.leasePropertyNo = leasePropertyNo;
    }

    public String getLeaseAddress() {
        return leaseAddress;
    }

    public void setLeaseAddress(String leaseAddress) {
        this.leaseAddress = leaseAddress;
    }

    public String getLeaseDetailAddress() {
        return leaseDetailAddress;
    }

    public void setLeaseDetailAddress(String leaseDetailAddress) {
        this.leaseDetailAddress = leaseDetailAddress;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("projectName", getProjectName())
                .append("warehouseName", getWarehouseName())
                .append("operatorId", getOperatorId())
                .append("operatorName", getOperatorName())
                .append("smartsType", getSmartsType())
                .append("totalParking", getTotalParking())
                .append("provinceCode", getProvinceCode())
                .append("cityCode", getCityCode())
                .append("areaCode", getAreaCode())
                .append("address", getAddress())
                .append("longitude", getLongitude())
                .append("latitude", getLatitude())
                .append("remark", getRemark())
                .append("status", getStatus())
                .append("userId", getUserId())
                .append("deleteFlag", getDeleteFlag())
                .append("carouselImages", getCarouselImages())
                .append("parentId", getParentId())
                .append("floorInfo", getFloorInfo())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("leasePropertyNo", getLeasePropertyNo())
                .append("leaseAddress", getLeaseAddress())
                .append("leaseDetailAddress", getLeaseDetailAddress())
                .toString();
    }
}
