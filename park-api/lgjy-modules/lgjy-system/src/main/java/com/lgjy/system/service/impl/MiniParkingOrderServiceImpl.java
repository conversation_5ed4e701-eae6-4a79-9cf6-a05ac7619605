package com.lgjy.system.service.impl;

import java.util.List;
import java.math.BigDecimal;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.system.api.RemoteWxParkingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.MiniParkingOrderMapper;
import com.lgjy.system.domain.MiniParkingOrder;
import com.lgjy.system.service.IMiniParkingOrderService;

/**
 * 停车订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class MiniParkingOrderServiceImpl implements IMiniParkingOrderService {
    @Autowired
    private MiniParkingOrderMapper miniParkingOrderMapper;

    @Autowired
    private RemoteWxParkingOrderService remoteWxParkingOrderService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询停车订单
     *
     * @param id 停车订单主键
     * @return 停车订单
     */
    @Override
    public MiniParkingOrder selectMiniParkingOrderById(Long id) {
        MiniParkingOrder order = miniParkingOrderMapper.selectMiniParkingOrderById(id);
        if (order != null && order.getWarehouseId() != null && order.getPlateNo() != null
                && order.getBeginParkingTime() != null) {
            // 通过场库ID、车牌号和停车开始时间查询关联的出入场记录
            java.util.Map<String, Object> gateParkingInfo = selectGateParkingInfoByOrderInfo(
                    order.getWarehouseId(),
                    order.getPlateNo(),
                    order.getBeginParkingTime());
            if (gateParkingInfo != null) {
                // 将出入场记录信息设置到订单对象中（如果需要的话）
                // 这里可以根据需要设置相关字段
            }
        }
        return order;
    }

    /**
     * 查询停车订单列表
     *
     * @param miniParkingOrder 停车订单
     * @return 停车订单
     */
    @Override
    public List<MiniParkingOrder> selectMiniParkingOrderList(MiniParkingOrder miniParkingOrder) {
        return miniParkingOrderMapper.selectMiniParkingOrderList(miniParkingOrder);
    }

    /**
     * 新增停车订单
     *
     * @param miniParkingOrder 停车订单
     * @return 结果
     */
    @Override
    public int insertMiniParkingOrder(MiniParkingOrder miniParkingOrder) {
        // 使用雪花算法生成ID
        miniParkingOrder.setId(snowflakeIdGenerator.nextId());
        miniParkingOrder.setCreateTime(DateUtils.getNowDate());
        miniParkingOrder.setDeleteFlag(0);
        return miniParkingOrderMapper.insertMiniParkingOrder(miniParkingOrder);
    }

    /**
     * 修改停车订单
     *
     * @param miniParkingOrder 停车订单
     * @return 结果
     */
    @Override
    public int updateMiniParkingOrder(MiniParkingOrder miniParkingOrder) {
        miniParkingOrder.setUpdateTime(DateUtils.getNowDate());
        return miniParkingOrderMapper.updateMiniParkingOrder(miniParkingOrder);
    }

    /**
     * 批量删除停车订单
     *
     * @param ids 需要删除的停车订单主键
     * @return 结果
     */
    @Override
    public int deleteMiniParkingOrderByIds(Long[] ids) {
        return miniParkingOrderMapper.deleteMiniParkingOrderByIds(ids);
    }

    /**
     * 删除停车订单信息
     *
     * @param id 停车订单主键
     * @return 结果
     */
    @Override
    public int deleteMiniParkingOrderById(Long id) {
        return miniParkingOrderMapper.deleteMiniParkingOrderById(id);
    }

    /**
     * 根据车牌号查询停车订单
     *
     * @param plateNo 车牌号
     * @return 停车订单列表
     */
    @Override
    public List<MiniParkingOrder> selectMiniParkingOrderByPlateNo(String plateNo) {
        return miniParkingOrderMapper.selectMiniParkingOrderByPlateNo(plateNo);
    }

    /**
     * 根据场库ID查询停车订单
     *
     * @param warehouseId 场库ID
     * @return 停车订单列表
     */
    @Override
    public List<MiniParkingOrder> selectMiniParkingOrderByWarehouseId(Long warehouseId) {
        return miniParkingOrderMapper.selectMiniParkingOrderByWarehouseId(warehouseId);
    }

    /**
     * 根据支付状态查询停车订单
     *
     * @param payStatus 支付状态
     * @return 停车订单列表
     */
    @Override
    public List<MiniParkingOrder> selectMiniParkingOrderByPayStatus(Integer payStatus) {
        return miniParkingOrderMapper.selectMiniParkingOrderByPayStatus(payStatus);
    }

    /**
     * 统计订单数量
     *
     * @param miniParkingOrder 查询条件
     * @return 订单数量
     */
    @Override
    public int countMiniParkingOrder(MiniParkingOrder miniParkingOrder) {
        return miniParkingOrderMapper.countMiniParkingOrder(miniParkingOrder);
    }

    /**
     * 统计订单金额
     *
     * @param miniParkingOrder 查询条件
     * @return 订单金额统计
     */
    @Override
    public java.math.BigDecimal sumMiniParkingOrderAmount(MiniParkingOrder miniParkingOrder) {
        return miniParkingOrderMapper.sumMiniParkingOrderAmount(miniParkingOrder);
    }

    /**
     * 导出停车订单数据
     *
     * @param miniParkingOrder 停车订单
     * @return 停车订单集合
     */
    @Override
    public List<MiniParkingOrder> selectMiniParkingOrderListForExport(MiniParkingOrder miniParkingOrder) {
        return miniParkingOrderMapper.selectMiniParkingOrderList(miniParkingOrder);
    }

    /**
     * 获取数据库中现有的车辆类型选项
     *
     * @return 车辆类型列表
     */
    @Override
    public List<String> selectCarTypeOptions() {
        return miniParkingOrderMapper.selectCarTypeOptions();
    }

    /**
     * 根据车牌号和场库ID查询出入场记录
     *
     * @param plateNo     车牌号
     * @param warehouseId 场库ID
     * @return 出入场记录列表
     */
    @Override
    public List<java.util.Map<String, Object>> selectGateParkingInfo(String plateNo, Long warehouseId) {
        return miniParkingOrderMapper.selectGateParkingInfo(plateNo, warehouseId);
    }

    /**
     * 根据场库ID、车牌号和停车开始时间查询出入场记录
     *
     * @param warehouseId      场库ID
     * @param plateNo          车牌号
     * @param beginParkingTime 停车开始时间
     * @return 出入场记录
     */
    @Override
    public java.util.Map<String, Object> selectGateParkingInfoByOrderInfo(Long warehouseId, String plateNo,
            java.util.Date beginParkingTime) {
        return miniParkingOrderMapper.selectGateParkingInfoByOrderInfo(warehouseId, plateNo, beginParkingTime);
    }

    /**
     * 根据场库ID、车牌号和时间戳查询出入场记录
     *
     * @param warehouseId 场库ID
     * @param plateNo     车牌号
     * @param timestamp   时间戳字符串
     * @return 出入场记录
     */
    @Override
    public java.util.Map<String, Object> selectGateParkingInfoByTimestamp(Long warehouseId, String plateNo,
            String timestamp) {
        return miniParkingOrderMapper.selectGateParkingInfoByTimestamp(warehouseId, plateNo, timestamp);
    }

    /**
     * 停车订单退款
     */
    @Override
    public int refundParkingOrder(MiniParkingOrder miniParkingOrder) {
        // 直接调用wx模块退款接口，所有校验都在wx模块进行
        try {
            // 退款原因处理：如果为空或空字符串，使用默认值
            String refundReason = miniParkingOrder.getRefundReason();
            if (refundReason == null || refundReason.trim().isEmpty()) {
                refundReason = "管理员退款";
            }

            R<JSONObject> result = remoteWxParkingOrderService.refundParkingOrder(
                    miniParkingOrder.getTradeId(),
                    miniParkingOrder.getActualPayment(),
                    refundReason,
                    SecurityConstants.INNER
            );

            if (!R.isSuccess(result)) {
                throw new ServiceException("退款失败：" + result.getMsg());
            }

            // 退款成功后，更新本地订单状态
            MiniParkingOrder existOrder = miniParkingOrderMapper.selectMiniParkingOrderByTradeId(miniParkingOrder.getTradeId());
            if (existOrder != null) {
                existOrder.setPayStatus(4); // 已退款状态
                existOrder.setUpdateTime(DateUtils.getNowDate());
                existOrder.setUpdateBy(SecurityUtils.getUserId());
                return miniParkingOrderMapper.updateMiniParkingOrder(existOrder);
            }

            return 1; // 退款成功

        } catch (Exception e) {
            throw new ServiceException("退款失败：" + e.getMessage());
        }
    }
}
