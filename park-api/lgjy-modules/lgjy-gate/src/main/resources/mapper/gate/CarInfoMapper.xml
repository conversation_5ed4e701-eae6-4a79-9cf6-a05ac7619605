<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.gate.mapper.CarInfoMapper">
    <sql id="selectCarInfoVo">
        select id, parkingId,plateNum, beginTime, endTime, userName, lastUpdate
        from gate_car_info
    </sql>

    <select id="findCarInfo" resultType="com.lgjy.gate.pojo.CarInfoPojo">
        <include refid="selectCarInfoVo"/>、
        where plateNum = #{plate} and parkingId = #{parkingId}
    </select>

    <update id="updateCarInfo">
        update gate_car_info
        <set>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="beginTime != null">
                beginTime = #{beginTime},
            </if>
            <if test="endTime != null">
                endTime = #{endTime},
            </if>
            <if test="userName != null">
                userName = #{userName},
            </if>
            <if test="lastUpdate != null">
                lastUpdate = #{lastUpdate}
            </if>
        </set>
        where warehouseId = #{warehouseId} and plateNum = #{plateNum}
    </update>

    <insert id="addCarInfo">
        insert into gate_car_info(id, parkingId, plateNum, beginTime, endTime, userName, lastUpdate)
        values (#{id}, #{parkingId}, #{plateNum}, #{beginTime}, #{endTime}, #{userName}, #{lastUpdate})
    </insert>
</mapper>