# Tomcat
server:
  port: ${SERVER_PORT:9203}

# Spring
spring:
  application:
    # 应用名称
    name: ${SPRING_APPLICATION_NAME:park-gate}
  profiles:
    # 环境配置
    active: ${SPRING_PROFILES_ACTIVE:dev}
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR:127.0.0.1:8848}
        username: ${SPRING_CLOUD_NACOS_DISCOVERY_USERNAME:nacos}
        password: ${SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD:nacos}
      config:
        # 配置中心地址
        server-addr: ${SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR:127.0.0.1:8848}
        username: ${SPRING_CLOUD_NACOS_CONFIG_USERNAME:nacos}
        password: ${SPRING_CLOUD_NACOS_CONFIG_PASSWORD:nacos}
        # 配置文件格式
        file-extension: yml
        # 配置组
        group: DEFAULT_GROUP
        # 命名空间
        namespace: ${SPRING_CLOUD_NACOS_CONFIG_NAMESPACE:}
        # 是否启用配置中心
        enabled: true
        # 是否启用远程同步配置
        refresh-enabled: true
        # 共享配置
        shared-configs:
          - data-id: common-${spring.profiles.active}.yml
            group: DEFAULT_GROUP
            refresh: true
        # 扩展配置 - 品牌配置文件
        extension-configs:
          - data-id: sizhuo-config.yml
            group: DEFAULT_GROUP
            refresh: true
