package com.lgjy.gate.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class FieldFilterUtils {
    /**
     * 删除JSON字符串中的某些字段
     * @param jsonString
     * @return
     */
    public static String removeKey(String jsonString){
        JSONObject jsonObject = JSON.parseObject(jsonString);
        jsonObject.remove("SmallInPicUrl");
        jsonObject.remove("SmallOutPicUrl");
        jsonObject.remove("InPicUrl");
        jsonObject.remove("OutPicUrl");
        return jsonObject.toJSONString();
    }
}
