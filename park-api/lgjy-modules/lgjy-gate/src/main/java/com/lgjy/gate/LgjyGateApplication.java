package com.lgjy.gate;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import io.seata.spring.boot.autoconfigure.SeataAutoConfiguration;

import com.lgjy.common.security.annotation.EnableCustomConfig;
import com.lgjy.common.security.annotation.EnableRyFeignClients;

/**
 * 道闸设备控制微服务启动类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@EnableDiscoveryClient
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication(exclude = { SeataAutoConfiguration.class })
public class LgjyGateApplication {

    public static void main(String[] args) {
        SpringApplication.run(LgjyGateApplication.class, args);
        System.out.println("道闸服务启动成功");
    }
}