package com.lgjy.gate.service;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;

/**
 * 道闸查询服务统一接口
 * 所有品牌的道闸查询服务都需要实现此接口
 * 
 * <AUTHOR>
 */
public interface GateSearchService {

    /**
     * 根据车牌获取缴费金额
     * 
     * @param requestData 请求数据，包含：
     *                   - parkingId: 停车场ID
     *                   - plateNum: 车牌号
     * @return 查询结果
     */
    R<String> findParkingRate(JSONObject requestData);

    /**
     * 道闸出口查询停车费
     * 
     * @param requestData 请求数据，包含：
     *                   - parkingId: 停车场ID
     *                   - gateNo: 道闸号
     * @return 查询结果
     */
    R<String> outPayQuery(JSONObject requestData);

    /**
     * 查询车辆套餐
     * @param requestParams 请求数据JSON对象
     * @return
     */
    R<String> queryVehicle(JSONObject requestParams);

}
