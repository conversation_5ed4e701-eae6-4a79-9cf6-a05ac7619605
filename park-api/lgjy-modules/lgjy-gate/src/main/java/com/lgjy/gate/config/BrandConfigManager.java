package com.lgjy.gate.config;

import com.lgjy.common.core.utils.StringUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 品牌配置管理器
 * 负责管理所有品牌的配置信息和停车场映射关系
 * 
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@RequiredArgsConstructor
@RefreshScope
public class BrandConfigManager {

    private final SiZhuoParkConfig siZhuoParkConfig;

    // 品牌代码 -> 品牌配置 映射
    private final Map<String, BrandConfig> brandConfigs = new ConcurrentHashMap<>();
    
    // 逻辑停车场ID -> 品牌代码 映射
    private final Map<String, String> parkingBrandMapping = new ConcurrentHashMap<>();
    
    // 逻辑停车场ID -> 物理停车场ID 映射
    private final Map<String, String> logicalToPhysicalMapping = new ConcurrentHashMap<>();
    
    // 物理停车场ID -> 逻辑停车场ID 映射
    private final Map<String, String> physicalToLogicalMapping = new ConcurrentHashMap<>();

    @PostConstruct
    public void initConfigs() {
        log.info("开始初始化品牌配置...");

        // 注册思卓品牌配置
        registerBrandConfig(siZhuoParkConfig);

        log.info("品牌配置初始化完成，共注册 {} 个品牌", brandConfigs.size());
    }

    /**
     * 监听配置刷新事件
     */
    @EventListener(RefreshScopeRefreshedEvent.class)
    public void onConfigRefresh() {
        log.info("检测到配置刷新事件，重新初始化品牌配置");
        clearAllMappings();
        initConfigs();
    }

    /**
     * 注册品牌配置
     */
    private void registerBrandConfig(BrandConfig brandConfig) {
        if (brandConfig == null) {
            log.warn("品牌配置为空，跳过注册");
            return;
        }

        String brandCode = brandConfig.getBrandId();
        if (!StringUtils.hasText(brandCode)) {
            log.warn("品牌配置中brandId为空，跳过注册: {}", brandConfig.getBrandName());
            return;
        }

        brandConfigs.put(brandCode, brandConfig);

        // 建立停车场映射关系
        List<? extends BrandConfig.ParkArea> areas = brandConfig.getAreas();
        if (areas != null) {
            for (BrandConfig.ParkArea area : areas) {
                String logicalId = area.getLogicalId();
                String physicalId = area.getPhysicalId();

                if (StringUtils.hasText(logicalId) && StringUtils.hasText(physicalId)) {
                    // 建立品牌映射
                    parkingBrandMapping.put(logicalId, brandCode);

                    // 建立ID映射
                    logicalToPhysicalMapping.put(logicalId, physicalId);
                    physicalToLogicalMapping.put(physicalId, logicalId);

                    log.debug("注册停车场映射: 逻辑ID={}, 物理ID={}, 品牌={}",
                            logicalId, physicalId, brandCode);
                }
            }
        }

        log.info("成功注册品牌配置: {} - {}", brandCode, brandConfig.getBrandName());
    }

    /**
     * 根据停车场ID获取品牌代码
     */
    public String getBrandCode(String parkingId) {
        if (!StringUtils.hasText(parkingId)) {
            throw new IllegalArgumentException("停车场ID不能为空");
        }
        
        String brandCode = parkingBrandMapping.get(parkingId);
        if (brandCode == null) {
            throw new IllegalArgumentException("未找到停车场对应的品牌: " + parkingId);
        }
        
        return brandCode;
    }

    /**
     * 根据品牌代码获取品牌配置
     */
    public BrandConfig getBrandConfig(String brandCode) {
        if (!StringUtils.hasText(brandCode)) {
            throw new IllegalArgumentException("品牌代码不能为空");
        }
        
        BrandConfig config = brandConfigs.get(brandCode);
        if (config == null) {
            throw new IllegalArgumentException("未找到品牌配置: " + brandCode);
        }
        
        return config;
    }

    /**
     * 根据停车场ID获取品牌配置
     */
    public BrandConfig getBrandConfigByParkingId(String parkingId) {
        String brandCode = getBrandCode(parkingId);
        return getBrandConfig(brandCode);
    }

    /**
     * 逻辑ID转物理ID
     */
    public String getPhysicalId(String logicalId) {
        if (!StringUtils.hasText(logicalId)) {
            throw new IllegalArgumentException("逻辑ID不能为空");
        }
        
        String physicalId = logicalToPhysicalMapping.get(logicalId);
        if (physicalId == null) {
            throw new IllegalArgumentException("无效的逻辑ID: " + logicalId);
        }
        
        return physicalId;
    }

    /**
     * 物理ID转逻辑ID
     */
    public String getLogicalId(String physicalId) {
        if (!StringUtils.hasText(physicalId)) {
            throw new IllegalArgumentException("物理ID不能为空");
        }
        
        String logicalId = physicalToLogicalMapping.get(physicalId);
        if (logicalId == null) {
            throw new IllegalArgumentException("无效的物理ID: " + physicalId);
        }
        
        return logicalId;
    }

    /**
     * 检查是否为指定品牌的停车场
     */
    public boolean isBrandParking(String parkingId, String brandCode) {
        try {
            String actualBrandCode = getBrandCode(parkingId);
            return brandCode.equals(actualBrandCode);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取所有已注册的品牌代码
     */
    public java.util.Set<String> getAllBrandCodes() {
        return brandConfigs.keySet();
    }

    /**
     * 清空所有映射关系
     */
    private void clearAllMappings() {
        brandConfigs.clear();
        parkingBrandMapping.clear();
        logicalToPhysicalMapping.clear();
        physicalToLogicalMapping.clear();
    }
}
