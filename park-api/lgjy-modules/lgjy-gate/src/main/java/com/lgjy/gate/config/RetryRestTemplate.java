package com.lgjy.gate.config;

import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.protocol.HttpContext;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.ConnectException;
import java.util.Arrays;

public class RetryRestTemplate {
    public static RestTemplate build(int retryTimes, long retryIntervalTime, int connectTimeout, int readTimeout) {
        RestTemplate restTemplate = new RestTemplate();
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();

        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(
                MediaType.TEXT_HTML,
                MediaType.TEXT_PLAIN));
        restTemplate.getMessageConverters().add(mappingJackson2HttpMessageConverter);

        // 只有io异常才会触发重试
        HttpRequestRetryHandler handler = new HttpRequestRetryHandler() {
            @Override
            public boolean retryRequest(IOException exception, int curRetryCount, HttpContext context) {
                // curRetryCount 每一次都会递增，从1开始
                if (curRetryCount > retryTimes) {
                    return false;
                }
                try {
                    // 重试延迟
                    Thread.sleep(curRetryCount * retryIntervalTime);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (exception instanceof ConnectTimeoutException || exception instanceof NoHttpResponseException
                        || exception instanceof ConnectException) {
                    return true;
                }
                HttpClientContext clientContext = HttpClientContext.adapt(context);
                org.apache.http.HttpRequest request = clientContext.getRequest();
                boolean idempotent = !(request instanceof HttpEntityEnclosingRequest);
                if (idempotent) {
                    // 如果请求被认为是幂等的，那么就重试。 即重复执行不影响程序其他效果的
                    return true;
                }
                return false;
            }
        };
        httpClientBuilder.setRetryHandler(handler).setMaxConnTotal(400);
        // httpClient连接配置，底层是配置RequestConfig
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectTimeout)
                .setConnectionRequestTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        httpClientBuilder.setDefaultRequestConfig(requestConfig);
        CloseableHttpClient httpClient = httpClientBuilder.build();
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(
                httpClient);
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        return restTemplate;
    }
}
