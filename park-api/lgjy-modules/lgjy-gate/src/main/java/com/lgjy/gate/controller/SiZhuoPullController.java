package com.lgjy.gate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.gate.service.SiZhuoPullService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;

@RestController
@RequestMapping("/sizhuo/push")
public class SiZhuoPullController {

    @Resource
    private SiZhuoPullService siZhuoPullService;

    /**
     * 车辆入场完成时推送(入场记录)
     * @param jsonObject
     * @param request
     * @return
     */
    @PostMapping("/CarIn")
    public JSONObject CarIn(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        siZhuoPullService.CarIn(jsonObject,request.getRemoteAddr());
        JSONObject json = new JSONObject();
        json.put("Code",1);
        json.put("ErrMsg","success");
        return json;
    }

    /**
     * 车辆出厂完成时推送(出场记录)
     * @param jsonObject
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     * @throws NoSuchAlgorithmException
     */
    @PostMapping("/CarOut")
    public JSONObject CarOut(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        siZhuoPullService.CarOut(jsonObject,request.getRemoteAddr());
        JSONObject json = new JSONObject();
        json.put("Code",1);
        json.put("ErrMsg","success");
        return json;
    }

}
