package com.lgjy.gate.utils;

import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

public class DateUtils {
    private static final DateTimeFormatter DEFAULT_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DEFAULT_FORMATTER_DATE =
            DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 字符串转Date（默认格式）
     * @param dateStr 时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @return Date对象
     * @throws ParseException 当格式不匹配时抛出
     */
    public static Date parseDate(String dateStr) throws ParseException {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DEFAULT_FORMATTER);
            return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } catch (DateTimeParseException e) {
            throw new ParseException("格式不匹配: " + dateStr, e.getErrorIndex());
        }
    }

    /**
     * Date转字符串（默认格式）
     * @param date Date对象
     * @return 格式化后的时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDate(Date date) {
        Instant instant = date.toInstant();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return DEFAULT_FORMATTER.format(localDateTime);
    }

    /**
     * 将时间字符串（yyyy-MM-dd HH:mm:ss）转换为 Unix 秒级时间戳
     * @param dateStr 时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @return Unix 时间戳（秒级）
     * @throws DateTimeParseException 当格式不匹配时抛出
     */
    public static long parseUnixTime(String dateStr) throws DateTimeParseException {
        LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DEFAULT_FORMATTER);
        return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 将 Unix 秒级时间戳转换为时间字符串（yyyy-MM-dd HH:mm:ss）
     * @param unixTimestamp Unix 时间戳（秒级）
     * @return 格式化后的时间字符串
     */
    public static String formatUnixTime(long unixTimestamp) {
        Instant instant = Instant.ofEpochSecond(unixTimestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return DEFAULT_FORMATTER.format(localDateTime);
    }

    /**
     * 将 Unix 秒级时间戳转换为时间字符串（yyyy-MM-dd）
     * @param unixTimestamp Unix 时间戳（秒级）
     * @return 格式化后的时间字符串
     */
    public static String formatUnixTimeDate(long unixTimestamp) {
        Instant instant = Instant.ofEpochSecond(unixTimestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return DEFAULT_FORMATTER_DATE.format(localDateTime);
    }
}
