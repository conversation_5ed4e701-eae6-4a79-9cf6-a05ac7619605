package com.lgjy.gate.pojo;

import lombok.Data;

@Data
public class ErrorDataPojo {
    // 错误数据id
    private String id;
    // 错误code
    private int errCode;
    // 场库id
    private String parkingId;
    // 车牌号
    private String plateNum;
    // 入库时间
    private String inTime;
    // 入库通道名称
    private String inChannelName;
    // 出库时间
    private String outTime;
    // 出库通道名称
    private String outChannelName;
    // 金额
    private String money;
    // 入场图片路径
    private String imgPath;
    // 备注
    private String remark;
    // 最后更新时间
    private String lastUpdate;
}
