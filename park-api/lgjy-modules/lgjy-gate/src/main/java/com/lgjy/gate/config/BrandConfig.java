package com.lgjy.gate.config;

import java.util.List;

/**
 * 品牌配置接口
 * 所有品牌配置都需要实现此接口
 * 
 * <AUTHOR>
 */
public interface BrandConfig {
    
    /**
     * 获取品牌ID
     */
    String getBrandId();
    
    /**
     * 获取品牌名称
     */
    String getBrandName();
    
    /**
     * 获取请求基础URL
     */
    String getRequestUrl();
    
    /**
     * 获取停车场区域列表
     */
    List<? extends ParkArea> getAreas();
    
    /**
     * 停车场区域接口
     */
    interface ParkArea {
        /**
         * 获取物理停车场ID
         */
        String getPhysicalId();
        
        /**
         * 获取逻辑停车场ID
         */
        String getLogicalId();
        
        /**
         * 获取停车场名称
         */
        String getName();
        
        /**
         * 获取通道规则
         */
        ChannelRules getChannelRules();
    }
    
    /**
     * 通道规则接口
     */
    interface ChannelRules {
        /**
         * 获取入场不收费通道列表
         */
        List<String> getEntryDisabledChannels();
        
        /**
         * 获取出场不收费通道列表
         */
        List<String> getExitDisabledChannels();
    }
}
