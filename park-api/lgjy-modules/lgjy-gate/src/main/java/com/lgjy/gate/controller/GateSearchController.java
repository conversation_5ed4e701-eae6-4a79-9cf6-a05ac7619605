package com.lgjy.gate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.security.annotation.InnerAuth;
import com.lgjy.gate.factory.GateSearchServiceFactory;
import com.lgjy.gate.service.GateSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;

/**
 * 道闸查询
 * 统一接口，支持多品牌自动路由
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
public class GateSearchController {

    private final GateSearchServiceFactory gateSearchServiceFactory;

    /**
     * 通用的查询服务调用方法
     *
     * @param requestData 请求数据
     * @param request HTTP请求
     * @param serviceMethod 服务方法
     * @param operationName 操作名称（用于日志）
     * @return 处理结果
     */
    private R<String> executeSearchService(JSONObject requestData, HttpServletRequest request,
                                         java.util.function.Function<GateSearchService, R<String>> serviceMethod,
                                         String operationName) {
        try {
            // 添加远程地址到请求数据中
            requestData.put("remoteAddr", request.getRemoteAddr());

            // 根据parkingId获取对应的服务实现
            GateSearchService service = gateSearchServiceFactory.getServiceByParkingId(
                    requestData.getString("parkingId"));

            return serviceMethod.apply(service);
        } catch (Exception e) {
            log.error("{}失败: {}", operationName, e.getMessage(), e);
            return R.fail(operationName + "失败: " + e.getMessage());
        }
    }

    /**
     * 查询车辆套餐
     * 保持原有接口兼容性，直接调用思卓服务
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/queryVehicle")
    public R<String> queryVehicle(@RequestBody JSONObject requestData, HttpServletRequest request) {
        log.info("queryVehicle接口被调用,remoteAddr:" + request.getRemoteAddr()
                + "parkingId=" + requestData.getString("parkingId") + "plateNum=" + requestData.getString("plateNum"));
        return executeSearchService(requestData, request,
                service -> service.findParkingRate(requestData), "查询停车费");
    }

    /**
     * 根据车牌获取缴费金额
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/findParkingRate")
    public R<String> findParkingRate(@RequestBody JSONObject requestData, HttpServletRequest request) {
        log.info("findParkingRate接口被调用,remoteAddr:" + request.getRemoteAddr()
                + "parkingId=" + requestData.getString("parkingId") + ",plateNum=" + requestData.getString("plateNum"));

        return executeSearchService(requestData, request,
                service -> service.findParkingRate(requestData), "查询停车费");
    }
    /**
     * 道闸出口查询车费
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/outPayQuery")
    public R<String> outPayQuery(@RequestBody JSONObject requestData, HttpServletRequest request) {
        log.info("outPayQuery接口被调用,remoteAddr:" + request.getRemoteAddr()
                + "parkingId=" + requestData.getString("parkingId") + ",gateNo=" + requestData.getString("gateNo"));

        return executeSearchService(requestData, request,
                service -> service.outPayQuery(requestData), "道闸出口查询");
    }
}
