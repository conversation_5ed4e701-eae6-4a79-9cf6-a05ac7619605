package com.lgjy.gate.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class CarPullPojo {
    // 车场编号
    @JsonProperty("ParkID")
    private String parkId;

    // 车牌号
    @JsonProperty("Plate")
    private String plate;

    // 车牌类型
    @JsonProperty("PlateType")
    private String plateType;

    // 车辆类型
    @JsonProperty("VehicleType")
    private String vehicleType;

    // 用户名称
    @JsonProperty("UserName")
    private String userName;

    // 入场(出场）通道名称
    @JsonProperty("ChannelName")
    private String channelName;

    // 入场（出场）通道编号
    @JsonProperty("ChannelNo")
    private String channelNo;

    // 入场时间
    @JsonProperty("InTime")
    private String inTime;

    // 出场时间
    @JsonProperty("OutTime")
    private String outTime;

    // 支付金额
    @JsonProperty("PayMoney")
    private BigDecimal payMony;

    // 支付类型
    @JsonProperty("PayType")
    private String payType;

}
