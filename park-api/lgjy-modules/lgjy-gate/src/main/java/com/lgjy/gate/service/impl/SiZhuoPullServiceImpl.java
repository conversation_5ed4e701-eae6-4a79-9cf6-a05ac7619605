package com.lgjy.gate.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.gate.config.BrandConfigManager;
import com.lgjy.gate.constants.ErrorType;
import com.lgjy.gate.constants.PassConstants;
import com.lgjy.gate.mapper.ErrorDataLogMapper;
import com.lgjy.gate.mapper.GatePullLogMapper;
import com.lgjy.gate.mapper.ParkingInfoMapper;
import com.lgjy.gate.pojo.*;
import com.lgjy.gate.service.SiZhuoPullService;
import com.lgjy.gate.utils.*;
import com.lgjy.system.api.domain.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class SiZhuoPullServiceImpl implements SiZhuoPullService {

    @Resource
    private Base64ToImage base64ToImage;

    @Resource
    private GatePullLogMapper gatePullLogMapper;

    @Resource
    private ErrorDataLogMapper errorDataLogMapper;

    @Resource
    private ParkingInfoMapper parkingInfoMapper;

    @Resource
    private BrandConfigManager brandConfigManager;

    @Resource
    private SiZhuoSearchServiceImpl siZhuoSearchServiceImpl;

    /**
     * 车辆入场完成时推送(入场记录)
     * @param jsonObject
     * @param remoteAddr
     */
    @Override
    public void CarIn(JSONObject jsonObject, String remoteAddr) {
        String brandName=brandConfigManager.getSiZhuoParkConfig().getBrandName();
        try {
            // 1. 记录请求日志
            logRequest(remoteAddr, jsonObject, brandName,"CarIn");

            // 2. 转换并验证输入车辆入场数据
            CarPullPojo carPullPojo = convertAndValidateInput(jsonObject);

            // 3. 上传入场图片
            String filePath = uploadImage(jsonObject,"InPicUrl","carIn");

            // 4. 获取本系统停车场ID
            String logicalParkId = brandConfigManager.getLogicalId(carPullPojo.getParkId());

            // 5. 检查入场通道是否收费
            if (isEntryChannelAllowed(logicalParkId, carPullPojo.getChannelNo())) {
                // 6. 处理入场数据
                processParkingEntry(carPullPojo, logicalParkId, filePath);
            }
        } catch (Exception e) {
            log.error(brandName+"CarIn处理异常 - remoteAddr: {}, input: {}",
                    remoteAddr,
                    jsonObject != null ? FieldFilterUtils.removeKey(jsonObject.toJSONString()) : "null",
                    e);
        }
    }

    /**
     * 车辆出场完成时推送(入场记录)
     * @param jsonObject
     * @param remoteAddr
     */
    @Override
    public void CarOut(JSONObject jsonObject, String remoteAddr) {
        String brandName=brandConfigManager.getSiZhuoParkConfig().getBrandName();
        try {
        // 1. 记录请求日志
        logRequest(remoteAddr, jsonObject,brandName,"CarOut");
        // 2. 转换并验证输入数据
        CarPullPojo carPullPojo = convertAndValidateInput(jsonObject);
        // 3. 上传图片
        String filePath = uploadImage(jsonObject,"OutPicUrl","carOut");
        // 4. 获取逻辑停车场ID
        String logicalParkId = brandConfigManager.getLogicalId(carPullPojo.getParkId());
        // 5. 检查出口通道是否收费
        if (isExitChannelAllowed(carPullPojo.getParkId(), carPullPojo.getChannelNo())) {
            // 6. 处理出场数据
            processParkingExit(carPullPojo, logicalParkId, filePath);
        }
        } catch (Exception e) {
            log.error(brandName+"CarOut处理异常 - remoteAddr: {}, input: {}",
                    remoteAddr,
                    jsonObject != null ? jsonObject.toJSONString() : "null",
                    e);
        }
    }

    /**
     * 记录请求日志
     */
    private void logRequest(String accessAddress, JSONObject jsonObject,String parkingName,String address) {
        try {
            GatePullLogPojo gatePullLogPojo =new GatePullLogPojo();
            gatePullLogPojo.setId(UUID.randomUUID().toString().replace("-", ""));
            gatePullLogPojo.setParkingName(parkingName);
            gatePullLogPojo.setAccessAddress(accessAddress);
            gatePullLogPojo.setAddress(address);
            gatePullLogPojo.setData(FieldFilterUtils.removeKey(jsonObject.toJSONString()));
            gatePullLogPojo.setLastUpdate(DateUtils.formatDate(new Date()));
            gatePullLogMapper.gatePullLogInsert(gatePullLogPojo);
        } catch (Exception e) {
            log.error(parkingName + ":CarIn记录请求日志失败:", e);
        }
    }

    /**
     * 车辆数据转换校验
     * @param jsonObject
     * @return
     */
    private CarPullPojo convertAndValidateInput(JSONObject jsonObject) {
        if (jsonObject == null) {
            throw new IllegalArgumentException("输入参数不能为null");
        }

        CarPullPojo carPullPojo = jsonObject.to(CarPullPojo.class);

        if (StringUtils.isBlank(carPullPojo.getParkId())) {
            throw new IllegalArgumentException("停车场ID不能为空");
        }

        if (StringUtils.isBlank(carPullPojo.getChannelNo())) {
            throw new IllegalArgumentException("通道号不能为空");
        }

        return carPullPojo;
    }

    /**
     * 上传图片
     * @param jsonObject
     * @return
     */
    private String uploadImage(JSONObject jsonObject,String imageType,String path) {
        try {
            String imageUrl = jsonObject.getString(imageType);
            if (StringUtils.isBlank(imageUrl)) {
                log.warn(imageType+"图片URL为空");
                return "";
            }

            R<SysFile> remoteFileResult = base64ToImage.uploadBase64Image(imageUrl,path);
            if (remoteFileResult == null || R.isError(remoteFileResult)) {
                log.error(imageType+"图片上传失败：{}", remoteFileResult != null ? remoteFileResult.getMsg() : "返回结果为null");
                return "";
            }

            return Optional.ofNullable(remoteFileResult.getData())
                    .map(SysFile::getUrl)
                    .orElse("");
        } catch (Exception e) {
            log.error(imageType+"上传入场图片异常", e);
            return "";
        }
    }


    /**
     * 场库某些入场通道不计费，不处理入场数据
     * @param logicalParkId
     * @param channelNo
     * @return
     */
    private boolean isEntryChannelAllowed(String logicalParkId, String channelNo) {
        return brandConfigManager.getSiZhuoParkConfig().getAreas().stream()
                .filter(area -> area.getLogicalId().equals(logicalParkId))
                .findFirst()
                .map(area -> !area.getChannelRules().getEntryDisabledChannels().contains(channelNo))
                .orElse(true); // 未配置的停车场默认允许
    }
    /**
     * 场库出场通道不允许
     * @param logicalParkId
     * @param channelNo
     * @return
     */
    private boolean isExitChannelAllowed(String logicalParkId, String channelNo) {
        return brandConfigManager.getSiZhuoParkConfig().getAreas().stream()
                .filter(area -> area.getLogicalId().equals(logicalParkId))
                .findFirst()
                .map(area -> !area.getChannelRules().getExitDisabledChannels().contains(channelNo))
                .orElse(true);
    }

    /**
     * 处理入场数据
     * @param carPullPojo
     * @param logicalParkId
     * @param filePath
     */
    private void processParkingEntry(CarPullPojo carPullPojo, String logicalParkId, String filePath) {
        try {
            // 转换时间格式 时间字符Unix 时间戳（秒级）
            String inTime = carPullPojo.getInTime();
            long inTimeLong = DateUtils.parseUnixTime(inTime);

            // 验证数据
            ErrorDataPojo errorDataPojo = buildErrorDataPojoEntry(carPullPojo, logicalParkId, inTime, filePath);
            dataVerify(PassConstants.ENTRY, errorDataPojo);

            // 保存停车信息
            ParkingInfoPojo parkingInfoPojo = buildInParkingInfoPojo(carPullPojo, logicalParkId, inTimeLong, filePath);
            parkingInfoMapper.ParkingInfoInsert(parkingInfoPojo);
        } catch (Exception e) {
            log.error("处理入场数据异常", e);
        }
    }
    /**
     * 处理出场数据
     * @param carPullPojo
     * @param logicalParkId
     * @param filePath
     */
    private void processParkingExit(CarPullPojo carPullPojo, String logicalParkId, String filePath) {
        try {
            // 转换时间格式
            String inTime = carPullPojo.getInTime();
            String outTime = carPullPojo.getOutTime();
            long inTimeLong = DateUtils.parseUnixTime(inTime);
            long outTimeLong= DateUtils.parseUnixTime(outTime);

            // 验证数据
            Thread thread = new Thread(()->{
            ErrorDataPojo errorDataPojo = buildErrorDataPojoExit(carPullPojo, logicalParkId, inTime,outTime,filePath);
            dataVerify(PassConstants.EXIT, errorDataPojo);
            dataVerify(PassConstants.PAYMENT, errorDataPojo);
            });
            thread.start();
            List<ParkingInfoPojo> parkingInfoPojoList =
                    parkingInfoMapper.findParkingInfoByPlateNumAndInTime(logicalParkId, carPullPojo.getPlate(), inTimeLong);
            if(parkingInfoPojoList!=null&&parkingInfoPojoList.size()>0){
                String payType = "";
                if(carPullPojo.getPayType().equals("支付宝")){
                    payType = "1";
                }else if(carPullPojo.getPayType().equals("微信")) {
                    payType = "2";
                }else {
                    payType = "99";
                }
                ParkingInfoPojo parkingInfoPojo= parkingInfoPojoList.get(0);
                parkingInfoPojo.setPayType(payType);
                parkingInfoPojo.setOutPic(filePath);
                parkingInfoPojo.setOutTime(outTimeLong);
                parkingInfoPojo.setOutChannelId(carPullPojo.getChannelNo());
                parkingInfoPojo.setOutChannelName(carPullPojo.getChannelName());
                parkingInfoPojo.setMoney(Arith.fenToYuanString(carPullPojo.getPayMony()));
                parkingInfoPojo.setStatus(1);
                parkingInfoMapper.updateParkingInfo(parkingInfoPojo);
            }
        } catch (Exception e) {
            log.error("处理出场数据异常", e);
        }
    }

    /**
     * 构建入场错误数据日志
     * @param carPullPojo
     * @param logicalParkId
     * @param filePath
     * @return
     */
    private ErrorDataPojo buildErrorDataPojoExit(CarPullPojo carPullPojo, String logicalParkId, String inTime, String outTime,String filePath) {
        ErrorDataPojo errorDataPojo = new ErrorDataPojo();
        errorDataPojo.setParkingId(logicalParkId);
        errorDataPojo.setPlateNum(carPullPojo.getPlate());
        errorDataPojo.setInTime(inTime);
        errorDataPojo.setOutTime(outTime);
        errorDataPojo.setOutChannelName(carPullPojo.getChannelName());
        errorDataPojo.setImgPath(filePath);
        // 收费金额
        errorDataPojo.setMoney(Arith.fenToYuanString(carPullPojo.getPayMony()));
        return errorDataPojo;
    }
    /**
     * 构建入场记录错误数据日志
     * @param carPullPojo
     * @param logicalParkId
     * @param inTimeString
     * @param filePath
     * @return
     */
    private ErrorDataPojo buildErrorDataPojoEntry(CarPullPojo carPullPojo, String logicalParkId, String inTimeString, String filePath) {
        ErrorDataPojo errorDataPojo = new ErrorDataPojo();
        errorDataPojo.setParkingId(logicalParkId);
        errorDataPojo.setPlateNum(carPullPojo.getPlate());
        errorDataPojo.setInTime(inTimeString);
        errorDataPojo.setInChannelName(carPullPojo.getChannelName());
        errorDataPojo.setImgPath(filePath);
        return errorDataPojo;
    }

    /**
     * 构建出入场信息
     * @param carPullPojo
     * @param logicalParkId
     * @param inTime
     * @param filePath
     * @return
     */
    private ParkingInfoPojo buildInParkingInfoPojo(CarPullPojo carPullPojo, String logicalParkId, long inTime, String filePath) {
        ParkingInfoPojo parkingInfoPojo = new ParkingInfoPojo();
        parkingInfoPojo.setId(UUID.randomUUID().toString().replace("-", ""));
        parkingInfoPojo.setParkingId(logicalParkId);
        parkingInfoPojo.setPlateNum(carPullPojo.getPlate());
        parkingInfoPojo.setCarType(carPullPojo.getVehicleType());
        parkingInfoPojo.setInTime(inTime);
        parkingInfoPojo.setInChannelId(carPullPojo.getChannelNo());
        parkingInfoPojo.setInChannelName(carPullPojo.getChannelName());
        parkingInfoPojo.setInPic(filePath);
        parkingInfoPojo.setOutTime(0);
        parkingInfoPojo.setOutChannelId("");
        parkingInfoPojo.setOutChannelName("");
        parkingInfoPojo.setMoney("0");
        parkingInfoPojo.setStatus(0);
        parkingInfoPojo.setLastUpdate(new Date());

        return parkingInfoPojo;
    }

    /**
     * 推送数据验证
     * @param passType
     * @param errorDataPojo
     */
    public void dataVerify(int passType, ErrorDataPojo errorDataPojo) {
        if (errorDataPojo == null) {
            log.warn("ErrorDataPojo is null");
            return;
        }
        // 无论什么情况，都先验证车牌是否识别
//        if(errorDataPojo.getPlateNum().equals("未识别")){
//            passType=PassConstants.PLATE_ERROR;
//        }
        try {
            switch (passType) {
                // 车牌未识别
                case PassConstants.PLATE_ERROR:
                    handlePlateNotRecognized(errorDataPojo);
                    break;
                // 入场验证
                case PassConstants.ENTRY:
                    handleEntryVerification(errorDataPojo);
                    break;
                // 出场验证
                case PassConstants.EXIT:
                    handleExitVerification(errorDataPojo);
                    break;
                // 出场收费验证
                case PassConstants.PAYMENT:
                    handlePaymentVerification(errorDataPojo);
                    break;
                default:
                    log.warn("不知道的推送验证类型 {}", passType);
            }
        } catch (Exception e) {
            log.error("错误推送验证数据，车牌: {}", errorDataPojo.getPlateNum(), e);
        }
    }

    // 未识别车牌（errorCode=3)
    private void handlePlateNotRecognized(ErrorDataPojo errorDataPojo) {
        prepareErrorData(errorDataPojo, ErrorType.PLATE_ERROR, ErrorType.PLATE_ERROR.getDescription());
    }

    // 处理入场验证，如果该车辆入场时，有入场记录还没出场，则返记录错误数据
    private void handleEntryVerification(ErrorDataPojo errorDataPojo) {
        // 查询该车辆最近一次是否未出场
        List<ParkingInfoPojo> list = parkingInfoMapper.findParkingInfo(
                errorDataPojo.getParkingId(),
                errorDataPojo.getPlateNum());
        // 最近一次有未出场的记录
        if (CollectionUtils.isNotEmpty(list)) {
            ParkingInfoPojo parkingInfoPojo = list.get(0);
            // 之前未出场的入场时间
            String startTime = DateUtils.formatUnixTime(parkingInfoPojo.getInTime());
            // 现在入场时间
            String endTime = errorDataPojo.getInTime();
            String remark = String.format("车辆入场库%s时,%s有之前的入场记录没有出场,出场时间约%s到%s",
                    errorDataPojo.getParkingId(),
                    errorDataPojo.getPlateNum(),
                    startTime,
                    endTime);
            // 添加错误数据记录
            prepareErrorData(errorDataPojo, ErrorType.ENTRY_ERROR, remark);
        }
    }

    // 处理出场验证，如果该车辆出场时，本系统没有入场记录，则记录错误数据
    private void handleExitVerification(ErrorDataPojo errorDataPojo) {
        long inTime = DateUtils.parseUnixTime(errorDataPojo.getInTime());
        // 查询入场记录
        List<ParkingInfoPojo> list = parkingInfoMapper.findParkingInfoByPlateNumAndInTime(
                errorDataPojo.getParkingId(),
                errorDataPojo.getPlateNum(),
                inTime);

        if (CollectionUtils.isEmpty(list)) {
            String endTime = errorDataPojo.getOutTime();
            String remark = String.format("车辆%s没有入场记录,出场时间为%s", errorDataPojo.getPlateNum(), endTime);
            prepareErrorData(errorDataPojo, ErrorType.EXIT_ERROR, remark);
        }
    }

    // 处理收费验证
    private void handlePaymentVerification(ErrorDataPojo errorDataPojo) {
        // 构建请求参数
        JSONObject requestParams = new JSONObject();
        requestParams.put("parkingId", errorDataPojo.getParkingId());
        requestParams.put("plateNum", errorDataPojo.getPlateNum());
        requestParams.put("remoteAddr", "0.0.0.0");

        R<String> res = siZhuoSearchServiceImpl.queryVehicle(requestParams);
        // 出场的时候查询（月租车和免费车 vehicleType=1 不校验）  临停校验是否停车超出1小时，没付费
        if (R.isSuccess(res)&& res.getData() != null) {
            JSONObject json = JSON.parseObject(res.getData());
            int vehicleType = json.getIntValue("vehicleType");
            long durationMillis = DateUtils.parseUnixTime(errorDataPojo.getOutTime())
                    -DateUtils.parseUnixTime(errorDataPojo.getInTime());
            long oneHourInMillis = 1000L * 60 * 60;

            String money = errorDataPojo.getMoney();
            BigDecimal moneyDecimal = new BigDecimal(money);

            if (vehicleType == 0 && durationMillis > oneHourInMillis&& moneyDecimal.compareTo(BigDecimal.ZERO)>0) {
                String startTime = errorDataPojo.getInTime();
                String endTime = errorDataPojo.getOutTime();
                String remark = String.format("车辆%s收费异常,入场时间为%s,出场时间为%s,收费金额0元",
                        errorDataPojo.getPlateNum(),
                        startTime,
                        endTime);

                prepareErrorData(errorDataPojo, ErrorType.PAYMENT_ERROR, remark);
            }
        }
    }

    // 处理最终错误数据插入
    private void prepareErrorData(ErrorDataPojo errorDataPojo, ErrorType errorType, String remark) {
        errorDataPojo.setId(UUID.randomUUID().toString().replace("-", ""));
        errorDataPojo.setErrCode(errorType.ordinal());
        errorDataPojo.setRemark(remark);
        errorDataPojo.setLastUpdate(DateUtils.formatDate(new Date()));

        errorDataLogMapper.errorDataLogAdd(errorDataPojo);
    }
}
