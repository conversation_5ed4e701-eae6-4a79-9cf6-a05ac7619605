package com.lgjy.gate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.security.annotation.InnerAuth;
import com.lgjy.gate.factory.GateControlServiceFactory;
import com.lgjy.gate.service.GateControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 道闸控制
 * 统一接口，支持多品牌自动路由
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/control")
@RequiredArgsConstructor
public class GateControlController {

    private final GateControlServiceFactory gateControlServiceFactory;

    /**
     * 通用的控制服务调用方法
     *
     * @param requestData 请求数据
     * @param request HTTP请求
     * @param serviceMethod 服务方法
     * @param operationName 操作名称（用于日志）
     * @return 处理结果
     */
    private R<String> executeControlService(JSONObject requestData, HttpServletRequest request,
                                          java.util.function.Function<GateControlService, R<String>> serviceMethod,
                                          String operationName) {
        try {
            // 添加远程地址到请求数据中
            requestData.put("remoteAddr", request.getRemoteAddr());

            // 根据parkingId获取对应的服务实现
            GateControlService service = gateControlServiceFactory.getServiceByParkingId(
                    requestData.getString("parkingId"));

            return serviceMethod.apply(service);
        } catch (Exception e) {
            log.error("{}处理失败: {}", operationName, e.getMessage(), e);
            return R.fail(operationName + "处理失败: " + e.getMessage());
        }
    }

    /**
     * 停车订单支付通知道闸平台
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/payOrder")
    public R<String> payOrder(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.payOrder(requestData), "支付订单");
    }

    /**
     * 无牌车入场
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/noPlateIn")
    public R<String> noPlateIn(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.noPlateIn(requestData), "无牌车入场");
    }

    /**
     * 添加月租车
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/saveMonthCar")
    public R<String> saveMonthCar(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.saveMonthCar(requestData), "添加月租车");
    }

    /**
     * 删除月租车
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/delMonthCar")
    public R<String> delMonthCar(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.delMonthCar(requestData), "删除月租车");
    }

    /**
     * 添加免费车
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/saveFreeCar")
    public R<String> saveFreeCar(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.saveFreeCar(requestData), "添加免费车");
    }

    /**
     * 增加黑名单
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/saveBlackCar")
    public R<String> saveBlackCar(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.saveBlackCar(requestData), "添加黑名单车辆");
    }

    /**
     * 删除黑名单
     *
     * @param requestData 请求数据JSON对象
     * @param request HTTP请求
     * @return 处理结果
     */
    @InnerAuth
    @PostMapping("/delBlackCar")
    public R<String> delBlackCar(@RequestBody JSONObject requestData, HttpServletRequest request) {
        return executeControlService(requestData, request,
                service -> service.delBlackCar(requestData), "删除黑名单车辆");
    }
}
