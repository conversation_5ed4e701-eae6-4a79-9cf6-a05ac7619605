package com.lgjy.gate.service;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;


public interface GateControlService {
        /**
         * 停车订单支付通知道闸平台
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> payOrder(JSONObject requestData);

        /**
         * 无牌车入场
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> noPlateIn(JSONObject requestData);

        /**
         * 添加月租车
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> saveMonthCar(JSONObject requestData);

        /**
         * 删除月租车
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> delMonthCar(JSONObject requestData);

        /**
         * 添加免费车
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> saveFreeCar(JSONObject requestData);

        /**
         * 添加黑名单
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> saveBlackCar(JSONObject requestData);

        /**
         * 删除黑名单
         *
         * @param requestData 请求数据JSON对象
         * @return
         */
        R<String> delBlackCar(JSONObject requestData);
}
