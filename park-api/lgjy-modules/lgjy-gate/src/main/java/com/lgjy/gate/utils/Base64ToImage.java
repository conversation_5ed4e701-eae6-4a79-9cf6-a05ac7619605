package com.lgjy.gate.utils;

import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.uuid.UUID;
import com.lgjy.system.api.RemoteFileService;
import com.lgjy.system.api.domain.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

@Slf4j
@Component
public class Base64ToImage {

    @Resource
    private RemoteFileService remoteFileService;

    /**
     * 将Base64图片转换为MultipartFile
     * @param base64Image Base64编码的图片字符串
     * @return MultipartFile对象
     * @throws IOException 如果转换失败
     */
    public static MultipartFile convertBase64ToMultipartFile(String base64Image) throws IOException {
        if (StringUtils.isEmpty(base64Image)) {
            throw new IllegalArgumentException("Base64图片字符串不能为空");
        }

        // 解析Base64字符串（处理data URI scheme）
        String[] parts = base64Image.split(",");
        String base64Data;
        String mimeType = "image/jpeg"; // 默认MIME类型

        if (parts.length > 1 && parts[0].contains(";base64")) {
            base64Data = parts[1];
            // 从头部提取MIME类型
            String header = parts[0];
            mimeType = header.substring(header.indexOf(':') + 1, header.indexOf(';'));
        } else {
            base64Data = base64Image;
        }

        // 解码Base64数据
        byte[] imageBytes = Base64.getDecoder().decode(base64Data.trim());
        if (imageBytes.length == 0) {
            throw new IOException("Base64解码失败：数据为空");
        }

        // 生成文件名（日期+UUID）
        String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String extension = getFileExtensionFromMime(mimeType);
        String fileName = datePath + "/" + UUID.fastUUID() + "." + extension;

        // 创建MultipartFile实现
        String finalMimeType = mimeType;
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return finalMimeType;
            }

            @Override
            public boolean isEmpty() {
                return imageBytes.length == 0;
            }

            @Override
            public long getSize() {
                return imageBytes.length;
            }

            @Override
            public byte[] getBytes() {
                return imageBytes;
            }

            @Override
            public InputStream getInputStream() {
                return new ByteArrayInputStream(imageBytes);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                try (FileOutputStream fos = new FileOutputStream(dest)) {
                    fos.write(imageBytes);
                }
            }
        };
    }

    /**
     * 根据MIME类型获取文件扩展名
     * @param mimeType MIME类型字符串
     * @return 文件扩展名（不带点）
     */
    private static String getFileExtensionFromMime(String mimeType) {
        switch (mimeType.toLowerCase()) {
            case "image/jpeg": return "jpg";
            case "image/png": return "png";
            case "image/gif": return "gif";
            case "image/bmp": return "bmp";
            case "image/webp": return "webp";
            case "image/svg+xml": return "svg";
            default: return "bin"; // 未知类型默认二进制
        }
    }

    /**
     * 处理Base64图片并上传
     * @param base64Image Base64编码的图片字符串（需包含data:image/[格式];base64,前缀）
     * @return 文件访问URL（失败时返回错误信息）
     */
    public R<SysFile> uploadBase64Image(String base64Image,String path) {
        // 1. 参数校验
        if (StringUtils.isBlank(base64Image)) {
            return R.fail("Base64图片数据不能为空");
        }

        try {
            // 2. 转换文件
            MultipartFile multipartFile = convertBase64ToMultipartFile(base64Image);
            if (multipartFile == null || multipartFile.isEmpty()) {
                return R.fail("Base64图片转换失败");
            }

            // 3. 上传文件
            return remoteFileService.upload(multipartFile,"gate/" + path);
        } catch (IllegalArgumentException e) {
            log.error("Base64格式错误: {}", e.getMessage());
            return R.fail("图片格式不合法");
        } catch (IOException e) {
            log.error("图片上传失败: {}", e.getMessage(), e); // 打印完整堆栈
            return R.fail("图片上传服务异常");
        } catch (Exception e) {
            log.error("未知错误: {}", e.getMessage(), e);
            return R.fail("系统处理图片失败");
        }
    }
}