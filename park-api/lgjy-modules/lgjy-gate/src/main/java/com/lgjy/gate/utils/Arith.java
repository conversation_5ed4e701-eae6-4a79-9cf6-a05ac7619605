package com.lgjy.gate.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class Arith {
    public static double round(double v,int scale){
        if(scale<0){
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 将金额从分转换为元（字符串形式，保留2位小数）
     * @param fen 金额（分），BigDecimal 类型
     * @return 金额（元），String 类型（如 "12.34"）
     */
    public static String fenToYuanString(BigDecimal fen) {
        if (fen == null) {
            return "0.00"; // 或抛出异常
        }
        // 1. 分转元（除以100，保留2位小数）
        BigDecimal yuan = fen.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        // 2. 格式化为字符串（确保两位小数，无科学计数法）
        DecimalFormat df = new DecimalFormat("#0.00");
        return df.format(yuan);
    }

    /**
     * 将金额从分转换为元（BigDecimal类型，保留2位小数）
     * @param fen 金额（分），Integer类型
     * @return 金额（元），BigDecimal类型（如 12.34）
     */
    public static BigDecimal fenToYuan(Integer fen) {
        // 处理null值（根据业务需求选择返回null或默认值）
        if (fen == null) {
            return BigDecimal.ZERO.setScale(2);
        }

        // 分转元（除以100，保留2位小数，四舍五入）
        return new BigDecimal(fen)
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }
}
