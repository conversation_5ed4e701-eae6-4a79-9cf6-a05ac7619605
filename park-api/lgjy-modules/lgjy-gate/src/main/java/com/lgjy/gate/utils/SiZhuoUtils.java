package com.lgjy.gate.utils;

import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.gate.config.BrandConfigManager;
import com.lgjy.gate.config.SiZhuoParkConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 思卓工具类
 * 保持向后兼容，同时使用新的品牌配置管理器
 *
 * <AUTHOR>
 */
@Data
@Component
@RequiredArgsConstructor
@RefreshScope
public class SiZhuoUtils {

    private final BrandConfigManager brandConfigManager;

    /**
     * 逻辑ID → 物理ID
     */
    public String getPhysicalId(String logicalId) {
        return brandConfigManager.getPhysicalId(logicalId);
    }

    /**
     * 物理ID → 逻辑ID
     */
    public String getLogicalId(String physicalId) {
        return brandConfigManager.getLogicalId(physicalId);
    }

    /**
     * 获取签名数据
     * @param physicalId
     * @param signData
     * @return
     */
    public String getSignData(String physicalId, StringBuilder signData) {
        signData.append("&key=").append(physicalId);
        return MD5Utils.MD5Encode(signData.toString(), "UTF-8").toUpperCase();
    }

    /**
     * 是否是思卓场库
     * @param parkingId
     * @return
     */
    public Boolean isSiZhuoParking(String parkingId) {
        return brandConfigManager.isBrandParking(parkingId, getSiZhuoParkConfig().getBrandId());
    }

    /**
     * 获取思卓配置
     * @return
     */
    public SiZhuoParkConfig getSiZhuoParkConfig() {
        return brandConfigManager.getSiZhuoParkConfig();
    }

}
