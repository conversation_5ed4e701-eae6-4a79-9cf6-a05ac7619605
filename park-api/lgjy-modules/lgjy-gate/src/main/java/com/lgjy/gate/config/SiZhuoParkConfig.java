package com.lgjy.gate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 思卓品牌配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sizhuo")
@RefreshScope
public class SiZhuoParkConfig implements BrandConfig {
    // sizhuo
    private String brandId;
    // 思卓
    private String brandName;
    // 请求地址 http://qr.it-wy.cn:83
    private String requestUrl;
    private List<SiZhuoParkArea> areas = new ArrayList<>();

    @Override
    public List<SiZhuoParkArea> getAreas() {
        return areas;
    }

    @Data
    public static class SiZhuoParkArea implements BrandConfig.ParkArea {
        // 物理停车场ID
        private String physicalId;
        // 逻辑停车场ID
        private String logicalId;
        // 停车场名称
        private String name;
        // 通道规则配置
        private ChannelRules channelRules;
    }

    @Data
    public static class ChannelRules implements BrandConfig.ChannelRules {
        // 入场不收费通道列表
        private List<String> entryDisabledChannels = new ArrayList<>();
        // 出场不收费通道列表
        private List<String> exitDisabledChannels = new ArrayList<>();
    }
}
