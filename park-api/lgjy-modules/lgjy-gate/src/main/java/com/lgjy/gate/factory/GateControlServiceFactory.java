package com.lgjy.gate.factory;

import com.lgjy.gate.annotation.GateService;
import com.lgjy.gate.config.BrandConfigManager;
import com.lgjy.gate.service.GateControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 道闸控制服务工厂
 * 根据品牌代码获取对应的控制服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GateControlServiceFactory {

    private final ApplicationContext applicationContext;
    private final BrandConfigManager brandConfigManager;

    // 品牌代码 -> 服务实现 映射缓存
    private final Map<String, GateControlService> serviceCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void initServices() {
        log.info("开始初始化道闸控制服务工厂...");

        // 使用注解自动注册控制服务
        registerServicesByType(GateService.Type.CONTROL);

        log.info("道闸控制服务工厂初始化完成，共注册 {} 个服务", serviceCache.size());
    }

    /**
     * 根据服务类型自动注册服务
     */
    private void registerServicesByType(GateService.Type targetType) {
        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(GateService.class);

        for (Map.Entry<String, Object> entry : beans.entrySet()) {
            String beanName = entry.getKey();
            Object bean = entry.getValue();

            GateService annotation = bean.getClass().getAnnotation(GateService.class);

            // 只处理指定类型的服务
            if (annotation.serviceType() != targetType) {
                continue;
            }

            String brandCode = annotation.brandCode();
            if (StringUtils.hasText(brandCode)) {
                if (serviceCache.containsKey(brandCode)) {
                    log.warn("品牌代码重复: {}, 已存在的服务将被覆盖", brandCode);
                }

                serviceCache.put(brandCode, (GateControlService) bean);
                log.info("成功注册{}服务: {} -> {} ({})",
                        targetType.getDescription(), brandCode, beanName, annotation.description());
            } else {
                log.warn("服务 {} 的品牌代码为空，跳过注册", beanName);
            }
        }
    }

    /**
     * 根据品牌代码获取服务实现
     */
    public GateControlService getService(String brandCode) {
        GateControlService service = serviceCache.get(brandCode);
        if (service == null) {
            throw new IllegalArgumentException("未找到品牌对应的道闸控制服务: " + brandCode);
        }
        return service;
    }

    /**
     * 根据停车场ID获取服务实现
     */
    public GateControlService getServiceByParkingId(String parkingId) {
        String brandCode = brandConfigManager.getBrandCode(parkingId);
        return getService(brandCode);
    }

    /**
     * 检查是否支持指定品牌
     */
    public boolean isSupported(String brandCode) {
        return serviceCache.containsKey(brandCode);
    }

    /**
     * 获取所有支持的品牌代码
     */
    public java.util.Set<String> getSupportedBrands() {
        return serviceCache.keySet();
    }
}
