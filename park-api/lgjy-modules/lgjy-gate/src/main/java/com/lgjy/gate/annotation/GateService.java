package com.lgjy.gate.annotation;

import org.springframework.stereotype.Service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 道闸服务注解
 * 用于标记道闸控制和查询服务实现类
 * 支持自动注册到对应的服务工厂
 * 
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Service
public @interface GateService {
    
    /**
     * 品牌代码
     */
    String brandCode() default "";
    
    /**
     * 服务类型
     */
    Type serviceType();
    
    /**
     * 优先级，数字越小优先级越高
     */
    int priority() default 0;
    
    /**
     * 描述信息
     */
    String description() default "";
    
    /**
     * 服务类型枚举
     */
    enum Type {
        /**
         * 控制服务（开闸、关闸、支付通知等）
         */
        CONTROL("control", "控制服务"),
        
        /**
         * 查询服务（车辆查询、费用查询、状态查询等）
         */
        SEARCH("search", "查询服务");
        
        private final String code;
        private final String description;
        
        Type(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
