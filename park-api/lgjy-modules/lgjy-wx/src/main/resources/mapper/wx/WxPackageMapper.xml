<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxPackageMapper">
    <sql id="selectPackageVo">
        select id, warehouse_id, package_type, package_name, package_price,
               remark, delete_flag, create_by, create_time,
               update_by, update_time from mini_vip_package
    </sql>

    <sql id="selectUserPackageVo">
        select id, warehouse_id, user_id, phone_number, plate_no, begin_vip_time, end_vip_time,
               vip_type, parking_space_no, dly_system_id, remark, delete_flag, create_by, create_time, update_by,
               update_time from mini_vip_package_user_detail
    </sql>

    <sql id="selectUserPackageRecordVo">
        select id, warehouse_id, package_id, user_id, phone_number, plate_no, vip_type,
               operate_type, transact_time, begin_vip_time, expiration_time, payment_amount, discount_amount, actual_payment,
               trade_id, pay_status, invoice_id, parking_space_no, group_buy_record_id,
               choose_time, delete_flag, create_by, create_time, update_by, update_time from mini_vip_transact_record
    </sql>

    <select id="selectWxPackageList" resultType="com.lgjy.wx.domain.WxPackage">
        <include refid="selectPackageVo"/>
        where delete_flag = 0 and warehouse_id = #{warehouseId}
    </select>

    <select id="selectWxUserPackage" resultType="com.lgjy.wx.domain.WxUserPackage">
        <include refid="selectUserPackageVo"/>
        where delete_flag = 0 and warehouse_id = #{warehouseId} and plate_no = #{plateNo}
        and user_id = #{userId} and vip_type = #{vipType}
        and end_vip_time > NOW()
    </select>

    <select id="selectWxUserPackageRecordList" resultType="com.lgjy.wx.domain.WxUserPackageRecord">
        select vtr.id, vtr.warehouse_id, package_id, vtr.user_id, phone_number, plate_no, vtr.vip_type,
               operate_type, transact_time, begin_vip_time, expiration_time, vtr.payment_amount, discount_amount, actual_payment,
               trade_id, pay_status, invoice_id, parking_space_no, group_buy_record_id,
               choose_time, vtr.delete_flag, vtr.create_by, vtr.create_time, vtr.update_by, vtr.update_time,
               wh.warehouse_name, '' as package_name
        from mini_vip_transact_record vtr
                 left join mini_warehouse wh on vtr.warehouse_id = wh.id
        where vtr.delete_flag = 0 and vtr.user_id = #{userId}
            <if test="vipType != null">and vtr.vip_type = #{vipType}</if>
            <if test="payStatus != null">and vtr.payStatus = #{vipType}</if>
        order by vtr.create_time desc
    </select>

    <select id="selectWxUserPackageById" resultType="com.lgjy.wx.domain.WxUserPackage">
        <include refid="selectUserPackageVo"/>
        where delete_flag = 0 and id = #{packageId}
    </select>

    <select id="selectWxPackageById" resultType="com.lgjy.wx.domain.WxPackage">
        <include refid="selectPackageVo"/>
        where delete_flag = 0 and id = #{packageId}
    </select>

    <select id="selectWxUserPackageByWarehouseIdAndPlateNo" resultType="com.lgjy.wx.domain.WxUserPackage">
        <include refid="selectUserPackageVo"/>
        where delete_flag = 0 and warehouse_id = #{warehouseId}
        and plate_no = #{plateNo} and end_vip_time > NOW()
    </select>

    <select id="selectWxUserPackageRecordByTradeId" resultType="com.lgjy.wx.domain.WxUserPackageRecord">
        <include refid="selectUserPackageRecordVo"/>
        where delete_flag = 0 and trade_id = #{tradeId}
    </select>

    <insert id="insertWxUserPackageRecord">
        INSERT INTO mini_vip_transact_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="plateNo != null and plateNo != ''">plate_no,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="transactTime != null">transact_time,</if>
            <if test="beginVipTime != null">begin_vip_time,</if>
            <if test="expirationTime != null">expiration_time,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="actualPayment != null">actual_payment,</if>
            <if test="tradeId != null and tradeId != ''">trade_id,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="parkingSpaceNo != null and parkingSpaceNo != ''">parking_space_no,</if>
            <if test="groupBuyRecordId != null">group_buy_record_id,</if>
            <if test="chooseTime != null">choose_time,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="vipType != null">vip_type,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="plateNo != null and plateNo != ''">#{plateNo},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="transactTime != null">#{transactTime},</if>
            <if test="beginVipTime != null">#{beginVipTime},</if>
            <if test="expirationTime != null">#{expirationTime},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="discountAmount != null">#{discountAmount},</if>
            <if test="actualPayment != null">#{actualPayment},</if>
            <if test="tradeId != null and tradeId != ''">#{tradeId},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="parkingSpaceNo != null and parkingSpaceNo != ''">#{parkingSpaceNo},</if>
            <if test="groupBuyRecordId != null">#{groupBuyRecordId},</if>
            <if test="chooseTime != null">#{chooseTime},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="vipType != null">#{vipType},</if>
            NOW(),
            NOW()
        </trim>
    </insert>

    <update id="updateWxUserPackageRecord">
        UPDATE mini_vip_transact_record
        <set>
            <if test="warehouseId != null">
                warehouse_id = #{warehouseId},
            </if>
            <if test="packageId != null">
                package_id = #{packageId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                phone_number = #{phoneNumber},
            </if>
            <if test="plateNo != null and plateNo != ''">
                plate_no = #{plateNo},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType},
            </if>
            <if test="transactTime != null">
                transact_time = #{transactTime},
            </if>
            <if test="beginVipTime != null">
                begin_vip_time = #{beginVipTime},
            </if>
            <if test="expirationTime != null">
                expiration_time = #{expirationTime},
            </if>
            <if test="paymentAmount != null">
                payment_amount = #{paymentAmount},
            </if>
            <if test="discountAmount != null">
                discount_amount = #{discountAmount},
            </if>
            <if test="actualPayment != null">
                actual_payment = #{actualPayment},
            </if>
            <if test="tradeId != null and tradeId != ''">
                trade_id = #{tradeId},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus},
            </if>
            <if test="invoiceId != null">
                invoice_id = #{invoiceId},
            </if>
            <if test="parkingSpaceNo != null and parkingSpaceNo != ''">
                parking_space_no = #{parkingSpaceNo},
            </if>
            <if test="groupBuyRecordId != null">
                group_buy_record_id = #{groupBuyRecordId},
            </if>
            <if test="chooseTime != null">
                choose_time = #{chooseTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateWxUserPackageExceedTime">
        UPDATE mini_vip_transact_record
        SET pay_status = 2,
            update_time = NOW()
        WHERE pay_status = 1
          AND create_time <![CDATA[ < ]]> DATE_SUB(NOW(), INTERVAL 15 MINUTE)
          AND user_id = #{userId}
    </update>

    <select id="selectWxUserPackageOld" resultType="com.lgjy.wx.domain.WxUserPackage">
        <include refid="selectUserPackageVo"/>
        where warehouse_id = #{warehouseId}
          and plate_no = #{plateNo}
          and user_id = #{userId}
          and vip_type = #{vipType}
          and delete_flag = 0
    </select>

    <insert id="insertWxUserPackage">
        INSERT INTO mini_vip_package_user_detail (
            id,
            warehouse_id,
            user_id,
            phone_number,
            plate_no,
            begin_vip_time,
            end_vip_time,
            parking_space_no,
            dly_system_id,
            vip_type,
            remark,
            delete_flag,
            create_time,
            update_time
        ) VALUES (
                     #{id},
                     #{warehouseId},
                     #{userId},
                     #{phoneNumber},
                     #{plateNo},
                     #{beginVipTime},
                     #{endVipTime},
                     #{parkingSpaceNo},
                     #{dlySystemId},
                     #{vipType},
                     #{remark},
                     #{deleteFlag},
                     NOW(),
                     NOW()
                 );
    </insert>

    <update id="updateWxUserPackage">
        update mini_vip_package_user_detail set
            warehouse_id = #{warehouseId},
            user_id = #{userId},
            phone_number = #{phoneNumber},
            plate_no = #{plateNo},
            begin_vip_time = #{beginVipTime},
            end_vip_time = #{endVipTime},
            parking_space_no = #{parkingSpaceNo},
            dly_system_id = #{dlySystemId},
            vip_type = #{vipType},
            remark = #{remark},
            delete_flag = #{deleteFlag},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="selectWxUserPackageNew" resultType="com.lgjy.wx.domain.WxUserPackage">
        <include refid="selectUserPackageVo"/>
        where warehouse_id = #{warehouseId}
        and plate_no = #{plateNo}
        <if test="vipType != null">
            and vip_type = #{vipType}
        </if>
        and delete_flag = 0
    </select>

    <select id="selectWxUserPackageByPlateNo" resultType="com.lgjy.wx.domain.WxUserPackage">
        <include refid="selectUserPackageVo"/>
        where plate_no = #{plateNo}
        and user_id = #{userId}
        and delete_flag = 0
        and and end_vip_time &lt; NOW()
    </select>

    <select id="selectWxUserPackageRecordById" resultType="com.lgjy.wx.domain.WxUserPackageRecord">
        <include refid="selectUserPackageRecordVo"/>
        where id = #{id} and delete_flag = 0
    </select>

    <!-- 查询超时订单（备用处理任务使用） -->
    <select id="selectTimeoutOrders" resultType="com.lgjy.wx.domain.WxUserPackageRecord">
        <include refid="selectUserPackageRecordVo"/>
        where create_time <![CDATA[ <= ]]> #{timeoutTime}
          and pay_status = #{payStatus}
          and delete_flag = 0
        order by create_time asc
        limit 100
    </select>
</mapper>