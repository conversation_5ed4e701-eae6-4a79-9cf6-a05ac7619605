<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxParkingOrderMapper">
    <sql id="baseResult">
        SELECT
            id,
            warehouse_id,
            parking_manage_id,
            user_id,
            plate_no,
            begin_parking_time,
            end_parking_time,
            parking_duration,
            payment_amount,
            discount_amount,
            actual_payment,
            pay_type,
            parking_reservation_id,
            invoice_id,
            trade_id,
            pay_status,
            payment_time,
            open_id,
            car_type,
            delete_flag,
            create_by,
            create_time,
            update_by,
            update_time
        FROM
            mini_parking_order
    </sql>


    <select id="selectWxParkingOrderList" resultType="com.lgjy.wx.domain.WxParkingOrder">
        SELECT
        o.id,
        o.warehouse_id,
        o.parking_manage_id,
        o.user_id,
        o.plate_no,
        o.begin_parking_time,
        o.end_parking_time,
        o.parking_duration,
        o.payment_amount,
        o.discount_amount,
        o.actual_payment,
        o.pay_type,
        o.parking_reservation_id,
        o.invoice_id,
        o.trade_id,
        o.pay_status,
        o.payment_time,
        o.open_id,
        o.car_type,
        o.delete_flag,
        o.create_by,
        o.create_time,
        o.update_by,
        o.update_time,
        w.warehouse_name
        FROM
        mini_parking_order o
        LEFT JOIN
        mini_warehouse w ON o.warehouse_id = w.id
        WHERE
        o.delete_flag = 0
        <if test="plateNo != null and plateNo != ''">
            AND o.plate_no = #{plateNo}
        </if>
        <if test="warehouseId != null">
            AND o.warehouse_id = #{warehouseId}
        </if>
        <if test="payStatus != null">
            AND o.pay_status = #{payStatus}
        </if>
        <if test="openId != null and openId != ''">
            AND o.open_id = #{openId}
        </if>
        <if test="tradeId != null and tradeId != ''">
            AND o.trade_id = #{tradeId}
        </if>
        <if test="userId != null">
            AND o.user_id = #{userId}
        </if>
        order by create_time desc
    </select>

    <select id="selectWxParkingOrderById" resultType="com.lgjy.wx.domain.WxParkingOrder">
        <include refid="baseResult"/>
        where id = #{id} and delete_flag = 0
    </select>

    <update id="updateWxParkingOrder">
        update mini_parking_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="parkingManageId != null">parking_manage_id = #{parkingManageId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="plateNo != null and plateNo != ''">plate_no = #{plateNo},</if>
            <if test="beginParkingTime != null">begin_parking_time = #{beginParkingTime},</if>
            <if test="endParkingTime != null">end_parking_time = #{endParkingTime},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="parkingReservationId != null">parking_reservation_id = #{parkingReservationId},</if>
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
            <if test="parkingDuration != null">parking_duration = #{parkingDuration},</if>
            <if test="tradeId != null">trade_id = #{tradeId},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="actualPayment != null">actual_payment = #{actualPayment},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
        </trim>
        where id = #{id} and pay_status != 5
    </update>

    <insert id="insertWxParkingOrder">
        insert into mini_parking_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="parkingManageId != null">parking_manage_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="plateNo != null and plateNo != ''">plate_no,</if>
            <if test="beginParkingTime != null">begin_parking_time,</if>
            <if test="endParkingTime != null">end_parking_time,</if>
            <if test="parkingDuration != null">parking_duration,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="parkingReservationId != null">parking_reservation_id,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="tradeId != null">trade_id,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="openId != null">open_id,</if>
            <if test="carType != null">car_type,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="actualPayment != null">actual_payment,</if>
            <if test="paymentTime != null">payment_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="parkingManageId != null">#{parkingManageId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="plateNo != null and plateNo != ''">#{plateNo},</if>
            <if test="beginParkingTime != null">#{beginParkingTime},</if>
            <if test="endParkingTime != null">#{endParkingTime},</if>
            <if test="parkingDuration != null">#{parkingDuration},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="parkingReservationId != null">#{parkingReservationId},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="tradeId != null">#{tradeId},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="openId != null">#{openId},</if>
            <if test="carType != null">#{carType},</if>
            <if test="discountAmount != null">#{discountAmount},</if>
            <if test="actualPayment != null">#{actualPayment},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
        </trim>
    </insert>

    <select id="selectWxParkingOrderByTradeId" resultType="com.lgjy.wx.domain.WxParkingOrder">
        <include refid="baseResult"/>
        <where>
            delete_flag = false
            <if test="tradeId != null">and trade_id = #{tradeId}</if>
        </where>
    </select>

    <update id="deleteWxParkingOrderByIds">
        update mini_parking_order
        set delete_flag = true
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectWxParkingOrderListByPackage" resultType="com.lgjy.wx.domain.WxParkingOrder">
        <include refid="baseResult"/>
        where warehouse_id = #{warehouseId}
        and plate_no = #{plateNo}
        and pay_status = 5
        and delete_flag = 0
        order by end_parking_time desc
    </select>

    <update id="updateWxParkingOrderInvoiceId">
        update mini_parking_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
        </trim>
        where id = #{id} and pay_status = 5
    </update>
</mapper>