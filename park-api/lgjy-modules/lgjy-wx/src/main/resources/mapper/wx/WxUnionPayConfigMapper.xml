<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxUnionPayConfigMapper">
    <sql id="selectSicvUnionPayConfigVo">
        select id,
               warehouse_id,
               pay_type,
               mid,
               tid,
               delete_flag,
               create_time,
               update_time
        from mini_union_pay_config
    </sql>

    <select id="selectWxUnionPayConfigList" resultType="com.lgjy.wx.domain.WxUnionPayConfig">
        <include refid="selectSicvUnionPayConfigVo"/>
        <where>
            delete_flag = false
            <if test="warehouseId != null ">and warehouse_id = #{warehouseId}</if>
            <if test="payType != null ">and pay_type = #{payType}</if>
        </where>
    </select>
</mapper>