<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxSpecialUserMapper">
    <sql id="selectSpecialUserVo">
        select id, nick_name, phone_number, plate_no, user_type, delete_flag, create_by,
               create_time, update_by, update_time from mini_special_user
    </sql>

    <select id="selectSpecialUserDetail" resultType="com.lgjy.wx.domain.WxSpecialUser">
        select mini_special_user.id, mini_special_user.nick_name, mini_special_user.phone_number,
        plate_no, mini_special_user.user_type,mini_special_user.delete_flag
        from mini_special_user left join wx_user on mini_special_user.phone_number = wx_user.phone_number
        where mini_special_user.delete_flag = 0 and wx_user.delete_flag = 0
        and wx_user.id=#{userId}
    </select>

    <select id="selectSpecialUserByPhoneNumber" resultType="com.lgjy.wx.domain.WxSpecialUser">
        <include refid="selectSpecialUserVo"/>
        where phone_number = #{phoneNumber}
        and delete_flag = 0
    </select>
</mapper>
