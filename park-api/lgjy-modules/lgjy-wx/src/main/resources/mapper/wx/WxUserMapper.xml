<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxUserMapper">
    <sql  id="selectWxUserVo">
        select id,user_name,nick_name,phone_number,password,open_id,img,status,user_type,other_id,we_chat_balance,
        platform_balance,donate_balance,delete_flag,create_by,update_by,update_time from wx_user
    </sql>
    <select id="selectUserByPhoneNumber" resultType="com.lgjy.system.api.domain.WxUser">
        <include refid="selectWxUserVo"/>
        where phone_number = #{phoneNumber} and delete_flag = 0
    </select>

    <select id="selectUserByOpenId" resultType="com.lgjy.system.api.domain.WxUser">
        <include refid="selectWxUserVo"/>
        where open_id = #{openid}
    </select>

    <insert id="insertWxUser">
        insert into wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="img != null and img != ''">img,</if>
            <if test="status != null">status,</if>

            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="otherId != null and otherId != ''">other_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="img != null and img != ''">#{img},</if>
            <if test="status != null">#{status},</if>

            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="otherId != null and otherId != ''">#{otherId},</if>
        </trim>
    </insert>

    <update id="updateUserInfo">
        update wx_user
            <set>
            <if test="userName != null and userName != ''">user_name = #{phoneNumber},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="img != null and img != ''">img = #{img}</if>
            <if test="openId != null and openId != ''">open_id = #{openId}</if>
            </set>
        where id = #{id}
    </update>

    <select id="selectUserById" resultType="com.lgjy.system.api.domain.WxUser">
        <include refid="selectWxUserVo"/>
        where id = #{userId}
    </select>
</mapper>