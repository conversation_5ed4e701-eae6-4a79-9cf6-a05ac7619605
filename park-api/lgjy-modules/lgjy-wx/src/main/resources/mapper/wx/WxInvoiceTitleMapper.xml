<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxInvoiceTitleMapper">
    
    <sql id="selectWxInvoiceTitleVo">
        select id,
               user_id,
               invoice_type,
               title_type,
               invoice_title_content,
               unit_duty_paragraph,
               register_address,
               register_phone,
               deposit_bank,
               bank_account,
               audit_status,
               delete_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from mini_invoice_title
    </sql>

    <select id="selectWxInvoiceTitleList" resultType="com.lgjy.wx.domain.WxInvoiceTitle">
        <include refid="selectWxInvoiceTitleVo"/>
        where delete_flag = 0
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        order by create_time desc
    </select>

    <insert id="insertWxInvoiceTitle">
        insert into mini_invoice_title(
            id,
            user_id,
            invoice_type,
            title_type,
            invoice_title_content,
            unit_duty_paragraph,
            register_address,
            register_phone,
            deposit_bank,
            bank_account,
            audit_status,
            delete_flag,
            create_by,
            create_time,
            update_by,
            update_time
        )
        values(
            #{id},
            #{userId},
            #{invoiceType},
            #{titleType},
            #{invoiceTitleContent},
            #{unitDutyParagraph},
            #{registerAddress},
            #{registerPhone},
            #{depositBank},
            #{bankAccount},
            #{auditStatus},
            #{deleteFlag},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        )
    </insert>

    <update id="updateWxInvoiceTitle">
        update mini_invoice_title
        <set>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="titleType != null">title_type = #{titleType},</if>
            <if test="invoiceTitleContent != null">invoice_title_content = #{invoiceTitleContent},</if>
            <if test="unitDutyParagraph != null">unit_duty_paragraph = #{unitDutyParagraph},</if>
            <if test="registerAddress != null">register_address = #{registerAddress},</if>
            <if test="registerPhone != null">register_phone = #{registerPhone},</if>
            <if test="depositBank != null">deposit_bank = #{depositBank},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        where id = #{id}
    </update>

    <select id="selectWxInvoiceTitleById" resultType="com.lgjy.wx.domain.WxInvoiceTitle">
        <include refid="selectWxInvoiceTitleVo"/>
        where id = #{id} and delete_flag = 0
    </select>
    
</mapper>
