package com.lgjy.wx.rt;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.uuid.IdUtils;
import com.lgjy.wx.constants.InvoiceType;
import com.lgjy.wx.domain.WxInvoiceRecord;
import com.lgjy.wx.domain.WxInvoiceTitle;
import com.lgjy.wx.rt.config.AppletApiProperties;
import com.lgjy.wx.rt.config.UnionPayApiProperties;
import com.lgjy.wx.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

@Component
public class UnionPayApiRestTemplate {
    private static final Logger log = LoggerFactory.getLogger(UnionPayApiRestTemplate.class);
    private final RestTemplate restTemplate;

    private final UnionPayApiProperties unionPayApiProperties;

    private final AppletApiProperties appletApiProperties;

    private final ObjectMapper objectMapper;

    private final UnionPayUtil unionPayUtil;

    public UnionPayApiRestTemplate(@Qualifier("unionPayClientRestTemplate") RestTemplate restTemplate,
                                   UnionPayApiProperties unionPayApiProperties,
                                   AppletApiProperties appletApiProperties,
                                   ObjectMapper objectMapper,
                                   UnionPayUtil unionPayUtil) {
        this.restTemplate = restTemplate;
        this.unionPayApiProperties = unionPayApiProperties;
        this.appletApiProperties = appletApiProperties;
        this.objectMapper = objectMapper;
        this.unionPayUtil = unionPayUtil;
    }
    /**
     * 银联微信下单
     */
    public JSONObject jsapi(String tradeId, BigDecimal pay, Date date,
                            String notifyUrl, String openId, String srcReserve,
                            String mid, String tid) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("requestTimestamp", date);
        map.put("merOrderId", tradeId);
        map.put("mid", mid);
        map.put("tid", tid);
        map.put("expireTime", DateUtils.datePlusTime(date, unionPayApiProperties.getTimeout() * 60 * 1000));
        map.put("originalAmount", pay.multiply(BigDecimal.valueOf(100)).intValue());
        map.put("totalAmount", pay.multiply(BigDecimal.valueOf(100)).intValue());
        map.put("notifyUrl", notifyUrl);
        map.put("subAppId", appletApiProperties.getAppid());
        map.put("tradeType", "MINI");
        map.put("subOpenId", openId);
        if (StringUtils.isNotBlank(srcReserve)) map.put("srcReserve", srcReserve);
        return restTemplate
                .exchange(
                        unionPayApiProperties.getUrl() + "/v1/netpay/wx/unified-order",
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                    add("Authorization", unionPayUtil.getAuthorization(objectMapper.writeValueAsString(map), date));
                                }}
                        ),
                        JSONObject.class
                )
                .getBody();
    }
    /**
     * 银联微信退款
     */
    public JSONObject jsapiRefund(String tradeId, BigDecimal pay, Date date,String mid, String tid) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("requestTimestamp", date);
        map.put("merOrderId", tradeId);
        map.put("mid", mid);
        map.put("tid", tid);
        map.put("refundAmount", pay.multiply(BigDecimal.valueOf(100)).intValue());
        return restTemplate
                .exchange(
                        unionPayApiProperties.getUrl() + "/v1/netpay/refund",
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                    add("Authorization", unionPayUtil.getAuthorization(objectMapper.writeValueAsString(map), date));
                                }}
                        ),
                        JSONObject.class
                )
                .getBody();
    }
    /**
     * 商户订单号查询
     */
    public JSONObject queryOrderByMchNo(String tradeId, Date date, String instMid,
                                        String mid, String tid) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("requestTimestamp", date);
        map.put("merOrderId", tradeId);
        map.put("mid", mid);
        map.put("tid", tid);
        map.put("instMid", instMid);
        return restTemplate
                .exchange(
                        unionPayApiProperties.getUrl() + "/v1/netpay/query",
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                    add("Authorization", unionPayUtil.getAuthorization(objectMapper.writeValueAsString(map), date));
                                }}
                        ),
                        JSONObject.class
                )
                .getBody();
    }
    /**
     * 关闭支付宝订单
     */
    public JSONObject closeAlipayOrder(String tradeId, Date date, String instMid,
                                       String mid, String tid) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("requestTimestamp", date);
        map.put("merOrderId", tradeId);
        map.put("mid", mid);
        map.put("tid", tid);
        map.put("instMid", instMid);
        return restTemplate
                .exchange(
                        unionPayApiProperties.getUrl() + "/v1/netpay/close",
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                    add("Authorization", unionPayUtil.getAuthorization(objectMapper.writeValueAsString(map), date));
                                }}
                        ),
                        JSONObject.class
                )
                .getBody();
    }
    /**
     * 银联开具发票
     */
    public com.alibaba.fastjson.JSONObject issueInvoice(WxInvoiceRecord invoiceRecord, BigDecimal pay, Date date,
                                                        String notifyUrl, String phone, List<Object> goods,
                                                        LinkedHashMap<String, Object> map, String mid, String tid) throws Exception {
        map.put("amount", pay.multiply(BigDecimal.valueOf(100)).intValue());
        if (StringUtils.isNotBlank(invoiceRecord.getBankAccount()))
            map.put("buyerAccount", invoiceRecord.getBankAccount());
        if (StringUtils.isNotBlank(invoiceRecord.getRegisterAddress()))
            map.put("buyerAddress", invoiceRecord.getRegisterAddress());
        if (StringUtils.isNotBlank(invoiceRecord.getDepositBank()))
            map.put("buyerBank", invoiceRecord.getDepositBank());
        map.put("buyerName", invoiceRecord.getInvoiceTitleContent());
        if (StringUtils.isNotBlank(invoiceRecord.getUnitDutyParagraph()))
            map.put("buyerTaxCode", invoiceRecord.getUnitDutyParagraph());
        if (StringUtils.isNotBlank(invoiceRecord.getRegisterPhone()))
            map.put("buyerTelephone", invoiceRecord.getRegisterPhone());
        map.put("goodsDetail", objectMapper.writeValueAsString(goods));
        map.put("invoiceMaterial", "ELECTRONIC");
        map.put("invoiceType", InvoiceType.getName(invoiceRecord.getInvoiceType()));
        map.put("merchantId", mid);
        map.put("merOrderDate", DateUtils.format(date, DateUtils.DATE_UNION_FORMATTER));
        map.put("merOrderId", invoiceRecord.getTradeId());
        map.put("msgId", invoiceRecord.getTradeId());
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgType", "issue");
        map.put("notifyEMail", invoiceRecord.getNotifyEmail());
        map.put("notifyUrl", notifyUrl);
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("srcReserve", invoiceRecord.getTradeId());
        map.put("terminalId", tid);
        map.put("sign", unionPayUtil.sign(map));
        log.info("银联开具发票参数：{}", map);
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 银联开具发票状态查询
     */
    public com.alibaba.fastjson.JSONObject queryInvoice(String tradeId, Date date, String mid, String tid) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("merchantId", mid);
        map.put("merOrderDate", DateUtils.format(date, DateUtils.DATE_UNION_FORMATTER));
        map.put("merOrderId", tradeId);
        map.put("msgId", tradeId);
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgType", "query");
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("srcReserve", tradeId);
        map.put("terminalId", tid);
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 模糊查询发票抬头
     */
    public com.alibaba.fastjson.JSONObject queryInvoiceTitle(String name) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("msgType", "query.fuzzy.title");
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("name", name);
        map.put("msgId", IdUtils.fastSimpleUUID());
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 发送邮箱
     */
    public com.alibaba.fastjson.JSONObject invoiceSend(WxInvoiceRecord invoice, String email) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("msgType", "notify");
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgId", IdUtils.fastSimpleUUID());
        map.put("merchantId", invoice.getMid());
        map.put("terminalId", invoice.getTid());
        map.put("merOrderId", invoice.getTradeId());
        map.put("merOrderDate", DateUtils.format(invoice.getOrderDate(), DateUtils.DATE_UNION_FORMATTER));
        map.put("notifyEMail", email);
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 银联开具发票-重开传参
     */
    public com.alibaba.fastjson.JSONObject reopenIssueInvoice(WxInvoiceRecord newInvoice, WxInvoiceTitle invoiceTitle, String email,
                                                              String notifyUrl, List<Object> goods, LinkedHashMap<String, Object> map) throws Exception {
        map.put("amount", newInvoice.getTotalMoney().multiply(BigDecimal.valueOf(100)).intValue());
        if (StringUtils.isNotBlank(invoiceTitle.getBankAccount()))
            map.put("buyerAccount", invoiceTitle.getBankAccount());
        if (StringUtils.isNotBlank(invoiceTitle.getRegisterAddress()))
            map.put("buyerAddress", invoiceTitle.getRegisterAddress());
        if (StringUtils.isNotBlank(invoiceTitle.getDepositBank()))
            map.put("buyerBank", invoiceTitle.getDepositBank());
        map.put("buyerName", invoiceTitle.getInvoiceTitleContent());
        if (StringUtils.isNotBlank(invoiceTitle.getUnitDutyParagraph()))
            map.put("buyerTaxCode", invoiceTitle.getUnitDutyParagraph());
        if (StringUtils.isNotBlank(invoiceTitle.getRegisterPhone()))
            map.put("buyerTelephone", invoiceTitle.getRegisterPhone());
        map.put("goodsDetail", objectMapper.writeValueAsString(goods));
        map.put("invoiceMaterial", "ELECTRONIC");
        map.put("invoiceType", InvoiceType.getName(invoiceTitle.getInvoiceType()));
        map.put("merchantId", newInvoice.getMid());
        map.put("merOrderDate", DateUtils.format(newInvoice.getOrderDate(), DateUtils.DATE_UNION_FORMATTER));
        map.put("merOrderId", newInvoice.getTradeId());
        map.put("msgId", newInvoice.getTradeId());
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgType", "issue");
        map.put("notifyEMail", email);
        map.put("notifyUrl", notifyUrl);
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("srcReserve", newInvoice.getTradeId());
        map.put("terminalId", newInvoice.getTid());
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 全电红字确认表申请
     */
    public com.alibaba.fastjson.JSONObject confirmedInfoApply(String tradeId, Date date, String mid, String tid) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("msgType", "reverse.confirmedInfoApply");
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgId", IdUtils.fastSimpleUUID());
        map.put("merchantId", mid);
        map.put("terminalId", tid);
        map.put("merOrderId", tradeId);
        map.put("merOrderDate", DateUtils.format(date, DateUtils.DATE_UNION_FORMATTER));
        // 红冲原因暂仅支持小程序购方开票有误发起，录入方传销方
        map.put("applyReason", "01");
        map.put("entryIdentity", "01");
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 全电红字确认表信息查询
     */
    public com.alibaba.fastjson.JSONObject confirmedInfoQuery(String tradeId, Date date, String mid, String tid) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("msgType", "reverse.confirmedInfoQuery");
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgId", IdUtils.fastSimpleUUID());
        map.put("merchantId", mid);
        map.put("terminalId", tid);
        map.put("merOrderId", tradeId);
        map.put("merOrderDate", DateUtils.format(date, DateUtils.DATE_UNION_FORMATTER));
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
    /**
     * 发票红冲
     */
    public com.alibaba.fastjson.JSONObject reverseInvoice(String tradeId, Date date, String mid, String tid, String redNotificationNo) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("msgType", "reverse");
        map.put("requestTimestamp", DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        map.put("msgSrc", unionPayApiProperties.getInvoiceMsgSrc());
        map.put("msgId", IdUtils.fastSimpleUUID());
        map.put("merchantId", mid);
        map.put("terminalId", tid);
        map.put("merOrderId", tradeId);
        map.put("merOrderDate", DateUtils.format(date, DateUtils.DATE_UNION_FORMATTER));
        if (StringUtils.isNotBlank(redNotificationNo)) map.put("redNotificationNo", redNotificationNo);
        map.put("sign", unionPayUtil.sign(map));
        return restTemplate
                .exchange(
                        unionPayApiProperties.getInvoiceUrl(),
                        HttpMethod.POST,
                        new HttpEntity<>(
                                map,
                                new LinkedMultiValueMap<String, String>() {{
                                    add("Content-Type", "application/json;charset=utf-8");
                                }}
                        ),
                        com.alibaba.fastjson.JSONObject.class
                )
                .getBody();
    }
}
