package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.WxUserCar;

import java.util.List;

public interface WxUserCarMapper {
    List<WxUserCar> selectWxUserCarList(WxUserCar wxUserCar);

    int insertWxUserCar(WxUserCar wxUserCar);

    Integer updateWxUserCar(WxUserCar wxUserCar);

    Integer deleteWxUserCar(Long id);

    WxUserCar selectWxUserCarById(Long id);

    WxUserCar selectWxUserDefaultCar(Long id);

    WxUserCar selectWxUserCarByPlateNo(WxUserCar wxUserCar);
}
