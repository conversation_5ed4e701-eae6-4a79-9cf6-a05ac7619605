package com.lgjy.wx.listener;

import com.lgjy.wx.event.RedisKeyExpiredEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

/**
 * Redis键过期事件消息监听器
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
public class KeyExpirationEventMessageListener implements MessageListener {
    
    private final ApplicationEventPublisher publisher;
    
    public KeyExpirationEventMessageListener(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String expiredKey = new String(message.getBody());
            log.debug("Redis键过期事件: {}", expiredKey);
            
            if (publisher != null) {
                publisher.publishEvent(new RedisKeyExpiredEvent(this, expiredKey));
            }
        } catch (Exception e) {
            log.error("处理Redis键过期事件失败", e);
        }
    }
}
