package com.lgjy.wx.controller;

import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.service.WxSpecialUserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/specialUser")
public class WxSpecialUserController extends BaseController {

    @Resource
    private WxSpecialUserService wxSpecialUserService;

    /**
     * 查询当前用户是否为特殊用户
     */
    @GetMapping("/detail")
    public AjaxResult selectSpecialUserDetail()
    {
        return success(wxSpecialUserService.selectSpecialUserDetail());
    }
}
