package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ParkingInfoPojo {
    // 停车信息id
    private String id;
    // 场库id
    private String parkingId;
    // 车牌号
    private String plateNum;
    // 车辆类型
    private String carType;
    // 入库时间
    private long inTime;
    // 入库通道id
    private String inChannelId;
    // 入库通道名称
    private String inChannelName;
    // 入库图片
    private String inPic;
    // 出库时间
    private long outTime;
    // 出库通道id
    private String outChannelId;
    // 出库通道名称
    private String outChannelName;
    // 出库图片
    private String outPic;
    // 金额
    private String money;
    // 支付方式
    private String payType;
    // 状态
    private int status;
    // 最后更新时间
    private Date lastUpdate;
    
}
