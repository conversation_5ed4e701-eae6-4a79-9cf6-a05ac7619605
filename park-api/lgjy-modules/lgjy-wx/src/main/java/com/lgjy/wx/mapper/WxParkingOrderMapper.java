package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.WxParkingOrder;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface WxParkingOrderMapper {
    /**
     * 查询停车缴费订单列表（场库id，openid，道闸编号，订单状态）
     * @param wxParkingOrderSelected
     * @return
     */
    List<WxParkingOrder> selectWxParkingOrderList(WxParkingOrder wxParkingOrderSelected);

    /**
     * 根据id查询停车缴费订单
     * @param id
     */
    WxParkingOrder selectWxParkingOrderById(Long id);

    /**
     * 更新旧订单信息
     * @param oldOrder
     */
    int updateWxParkingOrder(WxParkingOrder oldOrder);

    /**
     * 插入新订单信息
     * @param oldOrder
     */
    int insertWxParkingOrder(WxParkingOrder oldOrder);

    /**
     * 根据订单号查询订单
     * @param tradeId
     * @return
     */
    WxParkingOrder selectWxParkingOrderByTradeId(String tradeId);

    /**
     * 无牌车入场时，删除旧订单（同一个微信号进入的未完成的）
     * @param array
     */
    void deleteWxParkingOrderByIds(Long[] array);

    /**
     * 在会员过期后，用户是否进行过临停缴费.临停缴费的结束停车时间，在会员结束时间与当前时间之间
     * @param warehouseId
     * @param plateNo
     * @return
     */
    List<WxParkingOrder> selectWxParkingOrderListByPackage(@Param("warehouseId") Long warehouseId,
                                                     @Param("plateNo") String plateNo);

    // 开发票后更新发票id
    void updateWxParkingOrderInvoiceId(WxParkingOrder wxParkingOrder);
}
