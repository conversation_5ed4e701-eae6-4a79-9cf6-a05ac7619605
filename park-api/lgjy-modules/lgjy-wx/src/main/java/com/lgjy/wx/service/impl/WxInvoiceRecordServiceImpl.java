package com.lgjy.wx.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wx.constants.Constants;
import com.lgjy.wx.constants.InvoiceType;
import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.*;
import com.lgjy.wx.mapper.*;
import com.lgjy.wx.rt.UnionPayApiRestTemplate;
import com.lgjy.wx.rt.UnionPayUtil;
import com.lgjy.wx.rt.config.UnionPayApiProperties;
import com.lgjy.wx.service.WxInvoiceRecordService;
import com.lgjy.wx.utils.DateUtils;
import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class WxInvoiceRecordServiceImpl implements WxInvoiceRecordService {

    @Resource
    private WxInvoiceRecordMapper wxInvoiceRecordMapper;

    @Resource
    private WxUserMapper wxUserMapper;

    @Resource
    private WxParkingOrderMapper wxParkingOrderMapper;

    @Resource
    private WxUnionPayConfigMapper wxUnionPayConfigMapper;

    @Resource
    private UnionPayApiRestTemplate unionPayApiRestTemplate;

    @Resource
    private UnionPayApiProperties unionPayApiProperties;

    @Resource
    private WxPackageMapper wxPackageMapper;

    @Resource
    private WxWareHouseMapper wxWareHouseMapper;

    @Resource
    private WxInvoiceTitleMapper wxInvoiceTitleMapper;

    @Resource
    private UnionPayUtil unionPayUtil;

    @Resource
    private WxInvoiceReverseRecordMapper wxInvoiceReverseRecordMapper;

    @Resource
    private WxInvoiceRedConfirmMapper wxInvoiceRedConfirmMapper;

    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 查询开票记录列表
     */
    @Override
    public List<WxInvoiceRecord> selectWxInvoiceRecordList(WxInvoiceRecord wxInvoiceRecord) {
        wxInvoiceRecord.setUserId(SecurityUtils.getUserId());
        return wxInvoiceRecordMapper.selectWxInvoiceRecordList(wxInvoiceRecord);
    }

    @Override
    public WxInvoiceRecord selectWxInvoiceRecordById(Long id) {
        return wxInvoiceRecordMapper.selectWxInvoiceRecordById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invoiceCallback(JSONObject json) {
        // 更新发票记录状态
        String tradeId = json.getString("merOrderId");
        WxInvoiceRecord record = wxInvoiceRecordMapper.queryInvoiceRecordByTradeId(tradeId);
        if (Objects.nonNull(record) && !Objects.equals(record.getStatus(), PayConstants.INVOICE_STATUS_ISSUING)) {
            return true;
        } else if (Objects.nonNull(record) && Objects.equals(record.getStatus(), PayConstants.INVOICE_STATUS_ISSUING)) {
            record.setInvoiceNo(Objects.nonNull(json.get("invoiceNo")) ? json.getString("invoiceNo") : null);
            record.setPdfUrl(Objects.nonNull(json.get("pdfUrl")) ? json.getString("pdfUrl") : null);
            record.setStatus(json.getString("status"));
            record.setIssueDate(Objects.nonNull(json.get("issueDate")) ? DateUtil.parse(json.getString("issueDate")).setTimeZone(TimeZone.getTimeZone("GMT+8:00")) : null);
            record.setInvoiceCode(Objects.nonNull(json.get("invoiceCode")) ? json.getString("invoiceCode") : null);
            record.setQrCodeId(json.getString("qrCodeId"));
            record.setPdfPreviewUrl(Objects.nonNull(json.get("pdfPreviewUrl")) ? json.getString("pdfPreviewUrl") : null);
            record.setTotalTax(json.getBigDecimal("totalTax"));
            wxInvoiceRecordMapper.updateWxInvoiceRecord(record);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public JSONObject invoiceStatus(WxInvoiceRecord wxInvoiceRecord) {
        WxInvoiceRecord invoice = wxInvoiceRecordMapper.selectSicvInvoiceRecordById(wxInvoiceRecord.getId());
        WxUnionPayConfig wxUnionPayConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(invoice.getWarehouseId(),
                PayConstants.INVOICE_FUNCTION_TYPE_PARK);
        return unionPayApiRestTemplate.queryInvoice(invoice.getTradeId(), invoice.getOrderDate(),
                wxUnionPayConfig.getMid(), wxUnionPayConfig.getTid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public JSONObject queryInvoiceTitle(WxInvoiceRecord wxInvoiceRecord) {
        cn.hutool.core.lang.Assert.isTrue(StringUtils.isNotBlank(wxInvoiceRecord.getInvoiceTitleContent()), "请输入公司名称");
        return unionPayApiRestTemplate.queryInvoiceTitle(wxInvoiceRecord.getInvoiceTitleContent());
    }

    /**
     * 发票重开--①购方发起红字确认表②销方确认红字确认表③查询生效的红字确认表信息④红字确认表传参红冲，生成红冲发票⑤重开蓝字发票
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult resumeInvoice(WxInvoiceRecord wxInvoiceRecord) {
        cn.hutool.core.lang.Assert.isTrue(Objects.nonNull(wxInvoiceRecord)
                && Objects.nonNull(wxInvoiceRecord.getId())
                && Objects.nonNull(wxInvoiceRecord.getTitleId())
                && Objects.nonNull(wxInvoiceRecord.getNotifyEmail()), "必要参数缺失！");
        WxInvoiceRecord oldInvoice = wxInvoiceRecordMapper.selectSicvInvoiceRecordById(wxInvoiceRecord.getId());
        cn.hutool.core.lang.Assert.isTrue(Objects.nonNull(oldInvoice) && Objects.nonNull(oldInvoice.getTradeId()), "未查询到已开蓝字发票");
        cn.hutool.core.lang.Assert.isTrue(!Objects.equals(oldInvoice.getStatus(), PayConstants.INVOICE_STATUS_CLOSED), "蓝字发票未开具成功，请重新发起发票开具");
        cn.hutool.core.lang.Assert.isTrue(Objects.equals(oldInvoice.getReopenSign(), false), "重开发票禁止再次重开");
        WxInvoiceTitle invoiceTitle = wxInvoiceTitleMapper.selectWxInvoiceTitleById(wxInvoiceRecord.getTitleId());
        cn.hutool.core.lang.Assert.isTrue(Objects.nonNull(invoiceTitle), "重开票抬头异常");
        // 查询原发票是否已有重开票
        WxInvoiceRecord reopen = wxInvoiceRecordMapper.queryInvoiceRecordByOldTradeId(oldInvoice.getTradeId());
        cn.hutool.core.lang.Assert.isTrue(Objects.isNull(reopen) || Objects.equals(reopen.getStatus(), PayConstants.INVOICE_STATUS_CLOSED), "已成功重开发票禁止再次重开");
        // 历史数据重新设值
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        LinkedHashMap<String, Object> goods = new LinkedHashMap<>();
        paramQuery(oldInvoice, map, goods);
        // 重开失败则直接重试重开
        boolean result = true;
        String msg = "操作成功，请您在发票页面查看开票结果。";
        if (Objects.nonNull(reopen) && Objects.nonNull(reopen.getId())) {
            boolean reopenSign = reopenInvoice(reopen, oldInvoice, invoiceTitle, wxInvoiceRecord.getNotifyEmail(), map, goods);
            if (!reopenSign) {
                result = false;
                msg = reopen.getRemark();
            }
        } else {
            reopen = new WxInvoiceRecord();
            WxInvoiceReverseRecord reverseRecord = wxInvoiceReverseRecordMapper.selectReverseRecordByInvoiceId(oldInvoice.getId());
            // 已红冲，直接重开
            if (Objects.nonNull(reverseRecord) && !Objects.equals(reverseRecord.getStatus(), PayConstants.INVOICE_STATUS_CLOSED)) {
                boolean reopenSign = reopenInvoice(reopen, oldInvoice, invoiceTitle, wxInvoiceRecord.getNotifyEmail(), map, goods);
                if (!reopenSign) {
                    result = false;
                    msg = reopen.getRemark();
                }
            } else {
                if (Objects.isNull(reverseRecord)) reverseRecord = new WxInvoiceReverseRecord();
                // 未红冲，普通发票直接发起红冲，专票先走红字确认表流程
                if (Objects.equals(oldInvoice.getInvoiceType(), InvoiceType.INVOICE_TYPE_VAT.getCode())) {
                    // 申请全电红字确认表，没申请先申请，已申请查看红字确认表状态是否能用，确认能用则发起红冲，正常红冲->再次发起开具发票
                    WxInvoiceRedConfirm redConfirm = wxInvoiceRedConfirmMapper.selectRedConfirmByInvoiceId(oldInvoice.getId());
                    if (Objects.isNull(redConfirm) || Objects.isNull(redConfirm.getId())) {
                        redConfirm = new WxInvoiceRedConfirm();
                        boolean apply = redConfirmApply(redConfirm, oldInvoice);
                        if (apply) {
                            boolean query = redConfirmQuery(redConfirm, oldInvoice);
                            if (query && Objects.nonNull(redConfirm.getRedConfirmedUuid())
                                    && (Objects.equals(redConfirm.getConfirmState(), PayConstants.INVOICE_RED_CONFIRM_STATUS_OK)
                                    || Objects.equals(redConfirm.getConfirmState(), PayConstants.INVOICE_RED_CONFIRM_STATUS_CONFIRMED))) {
                                boolean reverse = reverseInvoice(reverseRecord, redConfirm, oldInvoice);
                                if (reverse) {
                                    boolean reopenSign = reopenInvoice(reopen, oldInvoice, invoiceTitle, wxInvoiceRecord.getNotifyEmail(), map, goods);
                                    if (!reopenSign) {
                                        result = false;
                                        msg = reopen.getRemark();
                                    }
                                } else {
                                    result = false;
                                    msg = reverseRecord.getRemark();
                                }
                            } else {
                                result = false;
                                msg = redConfirm.getRemark();
                            }
                        } else {
                            result = false;
                            msg = redConfirm.getRemark();
                        }
                    } else {
                        boolean query = redConfirmQuery(redConfirm, oldInvoice);
                        if (query && Objects.nonNull(redConfirm.getRedConfirmedUuid())
                                && (Objects.equals(redConfirm.getConfirmState(), PayConstants.INVOICE_RED_CONFIRM_STATUS_OK)
                                || Objects.equals(redConfirm.getConfirmState(), PayConstants.INVOICE_RED_CONFIRM_STATUS_CONFIRMED))) {
                            boolean reverse = reverseInvoice(reverseRecord, redConfirm, oldInvoice);
                            if (reverse) {
                                reopenInvoice(reopen, oldInvoice, invoiceTitle, wxInvoiceRecord.getNotifyEmail(), map, goods);
                            }
                        } else {
                            boolean apply = redConfirmApply(redConfirm, oldInvoice);
                            if (apply) {
                                boolean queryTwice = redConfirmQuery(redConfirm, oldInvoice);
                                if (queryTwice && Objects.nonNull(redConfirm.getRedConfirmedUuid())
                                        && (Objects.equals(redConfirm.getConfirmState(), PayConstants.INVOICE_RED_CONFIRM_STATUS_OK)
                                        || Objects.equals(redConfirm.getConfirmState(), PayConstants.INVOICE_RED_CONFIRM_STATUS_CONFIRMED))) {
                                    boolean reverse = reverseInvoice(reverseRecord, redConfirm, oldInvoice);
                                    if (reverse) {
                                        boolean reopenSign = reopenInvoice(reopen, oldInvoice, invoiceTitle, wxInvoiceRecord.getNotifyEmail(), map, goods);
                                        if (!reopenSign) {
                                            result = false;
                                            msg = reopen.getRemark();
                                        }
                                    } else {
                                        result = false;
                                        msg = reverseRecord.getRemark();
                                    }
                                } else {
                                    result = false;
                                    msg = redConfirm.getRemark();
                                }
                            } else {
                                result = false;
                                msg = redConfirm.getRemark();
                            }
                        }
                    }
                } else {
                    boolean reverse = reverseInvoice(reverseRecord, null, oldInvoice);
                    if (reverse) {
                        boolean reopenSign = reopenInvoice(reopen, oldInvoice, invoiceTitle, wxInvoiceRecord.getNotifyEmail(), map, goods);
                        if (!reopenSign) {
                            result = false;
                            msg = reopen.getRemark();
                        }
                    } else {
                        result = false;
                        msg = reverseRecord.getRemark();
                    }
                }
            }
        }
        return result?AjaxResult.success():AjaxResult.error(msg);
    }

    @Override
    public void invoiceSend(WxInvoiceRecord wxInvoiceRecord) {
       Assert.isTrue(Objects.nonNull(wxInvoiceRecord)
                && Objects.nonNull(wxInvoiceRecord.getId())
                && Objects.nonNull(wxInvoiceRecord.getNotifyEmail()), "参数缺失");
        WxInvoiceRecord invoiceRecord = wxInvoiceRecordMapper.selectSicvInvoiceRecordById(wxInvoiceRecord.getId());
        Assert.isTrue(Objects.nonNull(invoiceRecord)
                && Objects.equals(invoiceRecord.getStatus(), PayConstants.INVOICE_STATUS_ISSUED), "已开具才能再次发送");
        Assert.isTrue(Objects.nonNull(invoiceRecord)
                && Objects.nonNull(invoiceRecord.getMid())
                && Objects.nonNull(invoiceRecord.getTid())
                && Objects.nonNull(invoiceRecord.getTradeId())
                && Objects.nonNull(invoiceRecord.getOrderDate()), "历史数据，参数缺失");
        JSONObject send = unionPayApiRestTemplate.invoiceSend(invoiceRecord, wxInvoiceRecord.getNotifyEmail());
        Assert.isTrue(Objects.nonNull(send) && Objects.equals(send.getString("resultCode"), PayConstants.WE_CHAT_PAY_SUCCESS),
                Objects.nonNull(send) && Objects.nonNull(send.getString("resultMsg")) ? send.getString("resultMsg") : "发送邮件异常！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issueInvoice(WxInvoiceRecord wxInvoiceRecord) {
        try {
            if (Objects.isNull(wxInvoiceRecord)
                    || Objects.isNull(wxInvoiceRecord.getFunctionId())
                    || Objects.isNull(wxInvoiceRecord.getInvoiceTitleContent())
                    || Objects.isNull(wxInvoiceRecord.getNotifyEmail())
                    || Objects.isNull(wxInvoiceRecord.getInvoiceType())) {
                throw new IllegalArgumentException("必要参数缺失！");
            }
            BigDecimal pay;
            Date orderDate;
            Long userId = SecurityUtils.getUserId();
            WxUser wxUser =wxUserMapper.selectUserById(userId);
            String phone =wxUser.getPhoneNumber();
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            LinkedHashMap<String, Object> goods = new LinkedHashMap<>();
            Long warehouseId;
            // 开票功能判断 取参.停车费用校验发票类型，只能选用专用发票
            switch (wxInvoiceRecord.getFunctionType()) {
                case PayConstants.INVOICE_FUNCTION_TYPE_PARK:
                    WxParkingOrder parkingOrder = wxParkingOrderMapper.selectWxParkingOrderById(wxInvoiceRecord.getFunctionId());
                    warehouseId = parkingOrder.getWarehouseId();
                    pay = parkingOrder.getActualPayment();
                    wxInvoiceRecord.setTradeId(parkingOrder.getTradeId());
                    orderDate = parkingOrder.getPaymentTime();
                    if(parkingOrder.getTradeId()== null){
                        throw new ServiceException("订单不在该软件产生，请使用付款软件开票！");
                    }
                    parkInvoiceDetail(pay, map, goods, parkingOrder);
                    break;
                case PayConstants.INVOICE_FUNCTION_TYPE_VIP:
                    WxUserPackageRecord transactRecord = wxPackageMapper.selectWxUserPackageRecordById(wxInvoiceRecord.getFunctionId());
                    warehouseId = transactRecord.getWarehouseId();
                    pay = transactRecord.getActualPayment();
                    wxInvoiceRecord.setTradeId(transactRecord.getTradeId());
                    orderDate = transactRecord.getTransactTime();
                    Assert.isTrue(Objects.nonNull(transactRecord.getTradeId()), "订单不在该软件产生，请使用付款软件开票！");
                    vipInvoiceDetail(pay, map, goods, transactRecord);
                    break;
                default:
                    throw new ServiceException("功能选择异常！function = {} " + wxInvoiceRecord.getFunctionType());
            }
            Assert.isTrue(pay.compareTo(BigDecimal.ZERO) > 0, "订单金额异常！");
            wxInvoiceRecord.setUserId(SecurityUtils.getUserId());
            wxInvoiceRecord.setWarehouseId(warehouseId);
            wxInvoiceRecord.setTotalMoney(pay);
            wxInvoiceRecord.setNotifyMobileNo(phone);
            wxInvoiceRecord.setOrderDate(orderDate);
            // 银联开票
            WxUnionPayConfig wxUnionPayConfig = wxUnionPayConfigMapper.
                    selectWxUnionPayConfigList(warehouseId,PayConstants.INVOICE_FUNCTION_TYPE_PARK);
            if(wxUnionPayConfig==null){
                throw new ServiceException("银联支付参数缺失");
            }
            wxInvoiceRecord.setMid(wxUnionPayConfig.getMid());
            wxInvoiceRecord.setTid(wxUnionPayConfig.getTid());
            JSONObject jsonObject = unionPayApiRestTemplate.issueInvoice(wxInvoiceRecord, pay, orderDate,
                    unionPayApiProperties.getNotifyUrlOrderInvoice(), phone, Collections.singletonList(goods), map,
                    wxUnionPayConfig.getMid(), wxUnionPayConfig.getTid());
            log.info("开票结果：{}", jsonObject);
            Assert.isTrue(Objects.nonNull(jsonObject) && Objects.equals(jsonObject.getString("resultCode"), PayConstants.WE_CHAT_PAY_SUCCESS),
                    Objects.nonNull(jsonObject) && Objects.nonNull(jsonObject.getString("resultMsg")) ? jsonObject.getString("resultMsg") : "开票异常！");
            wxInvoiceRecord.setInvoiceNo(Objects.nonNull(jsonObject.get("invoiceNo")) ? jsonObject.getString("invoiceNo") : null);
            wxInvoiceRecord.setPdfUrl(Objects.nonNull(jsonObject.get("pdfUrl")) ? jsonObject.getString("pdfUrl") : null);
            wxInvoiceRecord.setStatus(jsonObject.getString("status"));
            wxInvoiceRecord.setIssueDate(Objects.nonNull(jsonObject.get("issueDate")) ? DateUtil.parse(jsonObject.getString("issueDate")).setTimeZone(TimeZone.getTimeZone("GMT+8:00")) : null);
            wxInvoiceRecord.setInvoiceCode(Objects.nonNull(jsonObject.get("invoiceCode")) ? jsonObject.getString("invoiceCode") : null);
            wxInvoiceRecord.setQrCodeId(jsonObject.getString("qrCodeId"));
            wxInvoiceRecord.setPdfPreviewUrl(Objects.nonNull(jsonObject.get("pdfPreviewUrl")) ? jsonObject.getString("pdfPreviewUrl") : null);
            wxInvoiceRecord.setTotalTax(jsonObject.getBigDecimal("totalTax"));
            if (Objects.nonNull(wxInvoiceRecord.getId())) {
                wxInvoiceRecordMapper.updateWxInvoiceRecord(wxInvoiceRecord);
            } else {
                // 自定义id，避免重复开票(自己写的)
                wxInvoiceRecord.setId(snowflakeIdGenerator.nextId());
                wxInvoiceRecordMapper.insertWxInvoiceRecord(wxInvoiceRecord);
            }
            // 更新订单开票信息
            switch (wxInvoiceRecord.getFunctionType()) {
                case PayConstants.INVOICE_FUNCTION_TYPE_PARK:
                    wxParkingOrderMapper.updateWxParkingOrderInvoiceId(new WxParkingOrder() {{
                        setId(wxInvoiceRecord.getFunctionId());
                        setInvoiceId(wxInvoiceRecord.getId());
                    }});
                    break;
                case PayConstants.INVOICE_FUNCTION_TYPE_VIP:
                    wxPackageMapper.updateWxUserPackageRecord(new WxUserPackageRecord() {{
                        setId(wxInvoiceRecord.getFunctionId());
                        setInvoiceId(wxInvoiceRecord.getId());
                    }});
                    break;
            }
        } catch (Exception e) {
            log.error("开票异常！", e);
            // 保存错误开票信息，初始化开票参数
            saveErrorInvoice(wxInvoiceRecord, e);
        }

    }


    private void parkInvoiceDetail(BigDecimal pay, LinkedHashMap<String, Object> map, LinkedHashMap<String, Object> goods, WxParkingOrder parkingOrder) {
        JSONArray invoiceSpecialInfo = new JSONArray();
        JSONObject special = new JSONObject();

        // 根据数据库查询
        WxWareHouse warehouse = wxWareHouseMapper.selectMiniWarehouseById(parkingOrder.getWarehouseId());
        if (warehouse != null) {
            special.put("leasePropertyNo", warehouse.getLeasePropertyNo());
            special.put("leaseAddress", warehouse.getLeaseAddress());
            special.put("leaseDetailAddress", warehouse.getLeaseDetailAddress());
        } else {
            throw new RuntimeException("场库不存在，warehouseId: " + parkingOrder.getWarehouseId());
        }

        special.put("leaseCrossSign", "N");
        special.put("leaseAreaUnit", "㎡");
        special.put("leaseHoldDateStart", DateUtils.format(parkingOrder.getBeginParkingTime(), DateUtils.DATE_HOUR_MINUTE_FORMATTER));
        special.put("leaseHoldDateEnd", DateUtils.format(parkingOrder.getEndParkingTime(), DateUtils.DATE_HOUR_MINUTE_FORMATTER));
        special.put("carNumbers", parkingOrder.getPlateNo());
        invoiceSpecialInfo.add(special);
        map.put("specialInvoiceFlag", "06");
        map.put("invoiceSpecialInfo", JSON.toJSONString(invoiceSpecialInfo));
        goods.put("attribute", 0);
        goods.put("index", 1);
        goods.put("name", "*经营租赁*停车费");
        goods.put("priceIncludingTax", pay);
        goods.put("sn", "3040502020200000000");
        goods.put("taxRate", 9);
    }

    private void vipInvoiceDetail(BigDecimal pay, LinkedHashMap<String, Object> map, LinkedHashMap<String, Object> goods, WxUserPackageRecord transactRecord) {
        JSONArray invoiceSpecialInfo = new JSONArray();
        JSONObject special = new JSONObject();

        // 优化：一次查询获取场库租赁信息，避免重复调用枚举
        WxWareHouse warehouse = wxWareHouseMapper.selectMiniWarehouseById(transactRecord.getWarehouseId());
        if (warehouse != null) {
            special.put("leasePropertyNo", warehouse.getLeasePropertyNo());
            special.put("leaseAddress", warehouse.getLeaseAddress());
            special.put("leaseDetailAddress", warehouse.getLeaseDetailAddress());
        } else {
           throw new RuntimeException("场库不存在，warehouseId: " + transactRecord.getWarehouseId());
        }

        special.put("leaseCrossSign", "N");
        special.put("leaseAreaUnit", "㎡");
        special.put("leaseHoldDateStart", DateUtils.format(transactRecord.getChooseTime(), DateUtils.DATE_HOUR_MINUTE_FORMATTER));
        special.put("leaseHoldDateEnd", DateUtils.format(transactRecord.getExpirationTime(), DateUtils.DATE_HOUR_MINUTE_FORMATTER));
        if (Objects.nonNull(transactRecord.getPlateNo()))
            special.put("carNumbers", transactRecord.getPlateNo().split(",")[0]);
        invoiceSpecialInfo.add(special);
        map.put("specialInvoiceFlag", "06");
        map.put("invoiceSpecialInfo", JSON.toJSONString(invoiceSpecialInfo));
        goods.put("attribute", 0);
        goods.put("index", 1);
        goods.put("name", "*经营租赁*停车费");
        goods.put("priceIncludingTax", pay);
        goods.put("sn", "3040502020200000000");
        goods.put("taxRate", 9);
    }

    /**
     * 保存错误开票信息
     */
    private void saveErrorInvoice(WxInvoiceRecord sicvInvoiceRecord, Exception e) {
        sicvInvoiceRecord.setUserId(SecurityUtils.getUserId());
        if (Objects.isNull(sicvInvoiceRecord.getWarehouseId()))
            // 没有wareHouseId的时候，就是LIN舍id是2
            sicvInvoiceRecord.setWarehouseId(2L);
        if (Objects.isNull(sicvInvoiceRecord.getInvoiceType())){
            sicvInvoiceRecord.setInvoiceType(InvoiceType.INVOICE_TYPE_PLAIN.getCode());
        }
        sicvInvoiceRecord.setStatus(PayConstants.INVOICE_STATUS_CLOSED);
        sicvInvoiceRecord.setRemark(e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage());
        if (Objects.nonNull(sicvInvoiceRecord.getId())) {
            wxInvoiceRecordMapper.updateWxInvoiceRecord(sicvInvoiceRecord);
        } else {
            wxInvoiceRecordMapper.insertWxInvoiceRecord(sicvInvoiceRecord);
        }
        // 更新订单开票信息
        switch (sicvInvoiceRecord.getFunctionType()) {
            case PayConstants.INVOICE_FUNCTION_TYPE_PARK:
                wxParkingOrderMapper.updateWxParkingOrder(new WxParkingOrder() {{
                    setId(sicvInvoiceRecord.getFunctionId());
                    setInvoiceId(sicvInvoiceRecord.getId());
                }});
                break;
            case PayConstants.INVOICE_FUNCTION_TYPE_VIP:
                wxPackageMapper.updateWxUserPackageRecord(new WxUserPackageRecord() {{
                    setId(sicvInvoiceRecord.getFunctionId());
                    setInvoiceId(sicvInvoiceRecord.getId());
                }});
                break;
        }
    }
    // 冗余参数
    public void paramQuery(WxInvoiceRecord oldInvoice, LinkedHashMap<String, Object> map, LinkedHashMap<String, Object> goods) {
        Long warehouseId;
        Date orderDate;
        switch (oldInvoice.getFunctionType()) {
            case PayConstants.INVOICE_FUNCTION_TYPE_PARK:
                WxParkingOrder parkingOrder = wxParkingOrderMapper.selectWxParkingOrderByTradeId(oldInvoice.getTradeId());
                orderDate = parkingOrder.getPaymentTime();
                warehouseId = parkingOrder.getWarehouseId();
                parkInvoiceDetail(parkingOrder.getActualPayment(), map, goods, parkingOrder);
                break;
            case PayConstants.INVOICE_FUNCTION_TYPE_VIP:
                WxUserPackageRecord transactRecord = wxPackageMapper.selectWxUserPackageRecordByTradeId(oldInvoice.getTradeId());
                warehouseId = transactRecord.getWarehouseId();
                orderDate = transactRecord.getTransactTime();
                vipInvoiceDetail(transactRecord.getActualPayment(), map, goods, transactRecord);
                break;
            default:
                throw new ServiceException("功能选择异常！function = {} " + oldInvoice.getFunctionType());
        }
        if (Objects.isNull(oldInvoice.getWarehouseId())) oldInvoice.setWarehouseId(warehouseId);
        if (Objects.isNull(oldInvoice.getOrderDate())) oldInvoice.setOrderDate(orderDate);
        if (Objects.isNull(oldInvoice.getMid()) || Objects.isNull(oldInvoice.getTid())) {
            WxUnionPayConfig wxUnionPayConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(warehouseId,PayConstants.INVOICE_FUNCTION_TYPE_PARK);
            if(wxUnionPayConfig==null){
                throw new ServiceException("银联支付参数缺失");
            }
            oldInvoice.setMid(wxUnionPayConfig.getMid());
            oldInvoice.setTid(wxUnionPayConfig.getTid());
        }
    }
    /**
     * 重开发票
     */
    public boolean reopenInvoice(WxInvoiceRecord reopenInvoice, WxInvoiceRecord oldInvoice, WxInvoiceTitle invoiceTitle, String email,
                                 LinkedHashMap<String, Object> map, LinkedHashMap<String, Object> goods){
        JSONObject reopenJson;
        boolean result = true;
        String reopen = null;
        if (Objects.isNull(reopenInvoice.getId())) {
            reopenInvoice.setUserId(oldInvoice.getUserId());
            reopenInvoice.setWarehouseId(oldInvoice.getWarehouseId());
            reopenInvoice.setTotalMoney(oldInvoice.getTotalMoney());
            reopenInvoice.setMid(oldInvoice.getMid());
            reopenInvoice.setTid(oldInvoice.getTid());
            reopenInvoice.setOrderDate(oldInvoice.getOrderDate());
            reopenInvoice.setFunctionType(oldInvoice.getFunctionType());
            reopenInvoice.setTradeId(unionPayUtil.generateOrderNo());
            reopenInvoice.setInvoiceType(invoiceTitle.getInvoiceType());
            reopenInvoice.setInvoiceTitleContent(invoiceTitle.getInvoiceTitleContent());
            reopenInvoice.setUnitDutyParagraph(invoiceTitle.getUnitDutyParagraph());
            reopenInvoice.setRegisterAddress(invoiceTitle.getRegisterAddress());
            reopenInvoice.setRegisterPhone(invoiceTitle.getRegisterPhone());
            reopenInvoice.setDepositBank(invoiceTitle.getDepositBank());
            reopenInvoice.setBankAccount(invoiceTitle.getBankAccount());
            reopenInvoice.setNotifyEmail(email);
            reopenInvoice.setReopenSign(true);
            reopenInvoice.setOldTradeId(oldInvoice.getTradeId());
        }
        try {
            reopenJson = unionPayApiRestTemplate.reopenIssueInvoice(reopenInvoice, invoiceTitle, email,
                    unionPayApiProperties.getNotifyUrlOrderInvoice(), Collections.singletonList(goods), map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (Objects.isNull(reopenJson) || StringUtils.isBlank(reopenJson.getString("resultMsg"))) {
            reopen = "发票重开失败!";
            result = false;
        } else if (!Objects.equals(reopenJson.getString("resultCode"), PayConstants.WE_CHAT_PAY_SUCCESS)) {
            reopen = reopenJson.getString("resultMsg");
            result = false;
        }
        if (result) {
            reopenInvoice.setInvoiceNo(Objects.nonNull(reopenJson.get("invoiceNo")) ? reopenJson.getString("invoiceNo") : null);
            reopenInvoice.setPdfUrl(Objects.nonNull(reopenJson.get("pdfUrl")) ? reopenJson.getString("pdfUrl") : null);
            reopenInvoice.setStatus(reopenJson.getString("status"));
            reopenInvoice.setIssueDate(Objects.nonNull(reopenJson.get("issueDate")) ? DateUtil.parse(reopenJson.getString("issueDate")).setTimeZone(TimeZone.getTimeZone("GMT+8:00")) : null);
            reopenInvoice.setInvoiceCode(Objects.nonNull(reopenJson.get("invoiceCode")) ? reopenJson.getString("invoiceCode") : null);
            reopenInvoice.setQrCodeId(reopenJson.getString("qrCodeId"));
            reopenInvoice.setPdfPreviewUrl(Objects.nonNull(reopenJson.get("pdfPreviewUrl")) ? reopenJson.getString("pdfPreviewUrl") : null);
            reopenInvoice.setTotalTax(reopenJson.getBigDecimal("totalTax"));
        } else {
            reopenInvoice.setStatus(PayConstants.INVOICE_STATUS_CLOSED);
            reopenInvoice.setRemark(reopen);
        }
        if (Objects.isNull(reopenInvoice.getId())) {
            wxInvoiceRecordMapper.insertWxInvoiceRecord(reopenInvoice);
        } else {
            reopenInvoice.setId(reopenInvoice.getId());
            wxInvoiceRecordMapper.updateWxInvoiceRecord(reopenInvoice);
        }
        if (result) updateOrderInvoice(reopenInvoice, oldInvoice);
        return result;
    }
    /**
     * 更新订单开票信息
     */
    private void updateOrderInvoice(WxInvoiceRecord reopenInvoice, WxInvoiceRecord oldInvoice) {
        switch (reopenInvoice.getFunctionType()) {
            case PayConstants.INVOICE_FUNCTION_TYPE_PARK:
                wxParkingOrderMapper.updateWxParkingOrder(new WxParkingOrder() {{
                    setTradeId(oldInvoice.getTradeId());
                    setInvoiceId(reopenInvoice.getId());
                }});
                break;
            case PayConstants.INVOICE_FUNCTION_TYPE_VIP:
                wxPackageMapper.updateWxUserPackageRecord(new WxUserPackageRecord() {{
                    setTradeId(oldInvoice.getTradeId());
                    setInvoiceId(reopenInvoice.getId());
                }});
                break;
        }
    }
    /**
     * 购方发起红字确认表
     */
    public boolean redConfirmApply(WxInvoiceRedConfirm redConfirm, WxInvoiceRecord oldInvoice) {
        JSONObject confirmedInfoApply = unionPayApiRestTemplate.confirmedInfoApply(oldInvoice.getTradeId(), oldInvoice.getOrderDate(), oldInvoice.getMid(), oldInvoice.getTid());
        boolean result = true;
        String apply = null;
        if (Objects.isNull(confirmedInfoApply)) {
            apply = "红字确认表申请失败!";
            result = false;
        } else if (!Objects.equals(confirmedInfoApply.getString("resultCode"), PayConstants.WE_CHAT_PAY_SUCCESS)) {
            apply = confirmedInfoApply.getString("resultMsg");
            result = false;
        }
        redConfirm.setWarehouseId(oldInvoice.getWarehouseId());
        redConfirm.setUserId(SecurityUtils.getUserId());
        redConfirm.setInvoiceId(oldInvoice.getId());
        redConfirm.setInvoiceType(oldInvoice.getInvoiceType());
        redConfirm.setApplyReason("01");
        redConfirm.setReverseState("N");
        redConfirm.setInvoiceCode(oldInvoice.getInvoiceCode());
        redConfirm.setTotalPrice(oldInvoice.getTotalMoney());
        redConfirm.setIssueDate(oldInvoice.getIssueDate());
        redConfirm.setEntryIdentity("01");
        redConfirm.setEntryDate(new Date());
        redConfirm.setRemark(apply);
        if (Objects.isNull(redConfirm.getId())) {
            wxInvoiceRedConfirmMapper.insertSicvInvoiceRedConfirm(redConfirm);
        } else {
            wxInvoiceRedConfirmMapper.updateSicvInvoiceRedConfirm(redConfirm);
        }
        return result;
    }
    /**
     * 红字确认表查询
     */
    public boolean redConfirmQuery(WxInvoiceRedConfirm redConfirm, WxInvoiceRecord oldInvoice) {
        JSONObject confirmedInfoQuery = unionPayApiRestTemplate.confirmedInfoQuery(oldInvoice.getTradeId(), oldInvoice.getOrderDate(), oldInvoice.getMid(), oldInvoice.getTid());
        boolean result = true;
        String query = null;
        if (Objects.isNull(confirmedInfoQuery)) {
            query = "红字确认表查询失败!";
            result = false;
        } else if (!Objects.equals(confirmedInfoQuery.getString("resultCode"), PayConstants.WE_CHAT_PAY_SUCCESS)) {
            query = confirmedInfoQuery.getString("resultMsg");
            result = false;
        }
        if (result) {
            redConfirm.setRedConfirmedUuid(confirmedInfoQuery.getString("redConfirmUuid"));
            redConfirm.setBuyerName(confirmedInfoQuery.getString("buyerName"));
            redConfirm.setBuyerTaxCode(Objects.nonNull(confirmedInfoQuery.get("buyerTaxCode")) ? confirmedInfoQuery.getString("buyerTaxCode") : null);
            redConfirm.setRedConfirmNo(confirmedInfoQuery.getString("redConfirmNo"));
            redConfirm.setConfirmState(confirmedInfoQuery.getString("confirmState"));
            redConfirm.setInvoiceCode(confirmedInfoQuery.getString("invoiceCode"));
            redConfirm.setTotalPrice(confirmedInfoQuery.getBigDecimal("totalPrice"));
            redConfirm.setTotalTax(confirmedInfoQuery.getBigDecimal("totalTax"));
            redConfirm.setIssueDate(confirmedInfoQuery.getDate("issueDate"));
            redConfirm.setSellerName(confirmedInfoQuery.getString("sellerName"));
            redConfirm.setSellerTaxCode(confirmedInfoQuery.getString("sellerTaxCode"));
            redConfirm.setEntryIdentity(confirmedInfoQuery.getString("entryIdentity"));
            redConfirm.setEntryDate(confirmedInfoQuery.getDate("entryDate"));
            redConfirm.setConfirmDate(Objects.nonNull(confirmedInfoQuery.get("confirmDate")) ? confirmedInfoQuery.getDate("confirmDate") : null);
            redConfirm.setReverseInvoiceCode(Objects.nonNull(confirmedInfoQuery.get("reverseInvoiceCode")) ? confirmedInfoQuery.getString("reverseInvoiceCode") : null);
            redConfirm.setReverseDate(Objects.nonNull(confirmedInfoQuery.get("reverseDate")) ? confirmedInfoQuery.getDate("reverseDate") : null);
            redConfirm.setReverseTotalPrice(Objects.nonNull(confirmedInfoQuery.get("reverseTotalPrice")) ? confirmedInfoQuery.getBigDecimal("reverseTotalPrice") : null);
            redConfirm.setReverseTotalTax(Objects.nonNull(confirmedInfoQuery.get("reverseTotalTax")) ? confirmedInfoQuery.getBigDecimal("reverseTotalTax") : null);
        } else {
            redConfirm.setBuyerName(oldInvoice.getInvoiceTitleContent());
            redConfirm.setBuyerTaxCode(oldInvoice.getUnitDutyParagraph());
            redConfirm.setRemark(query);
        }
        wxInvoiceRedConfirmMapper.updateSicvInvoiceRedConfirm(redConfirm);
        return result;
    }
    /**
     * 发票红冲
     */
    public boolean reverseInvoice(WxInvoiceReverseRecord reverseRecord, WxInvoiceRedConfirm redConfirm, WxInvoiceRecord oldInvoice) {
        JSONObject reverseInvoice;
        if (Objects.nonNull(redConfirm) && Objects.nonNull(redConfirm.getRedConfirmedUuid())) {
            reverseRecord.setRedConfirmedUuid(redConfirm.getRedConfirmedUuid());
            reverseInvoice = unionPayApiRestTemplate.reverseInvoice(oldInvoice.getTradeId(), oldInvoice.getOrderDate(), oldInvoice.getMid(), oldInvoice.getTid(), redConfirm.getRedConfirmedUuid());
        } else {
            reverseInvoice = unionPayApiRestTemplate.reverseInvoice(oldInvoice.getTradeId(), oldInvoice.getOrderDate(), oldInvoice.getMid(), oldInvoice.getTid(), null);
        }
        boolean result = true;
        String reverse = null;
        if (Objects.isNull(reverseInvoice)) {
            reverse = "发票红冲失败!";
            result = false;
        } else if (!Objects.equals(reverseInvoice.getString("resultCode"), PayConstants.WE_CHAT_PAY_SUCCESS)) {
            reverse = reverseInvoice.getString("resultMsg");
            result = false;
        }
        reverseRecord.setWarehouseId(oldInvoice.getWarehouseId());
        reverseRecord.setUserId(SecurityUtils.getUserId());
        reverseRecord.setInvoiceId(oldInvoice.getId());
        reverseRecord.setInvoiceType(oldInvoice.getInvoiceType());
        if (result) {
            reverseRecord.setStatus(reverseInvoice.getString("status"));
            reverseRecord.setReverseInvoiceNo(Objects.nonNull(reverseInvoice.get("reverseInvoiceNo")) ? reverseInvoice.getString("reverseInvoiceNo") : null);
            reverseRecord.setReverseInvoiceCode(Objects.nonNull(reverseInvoice.get("reverseInvoiceCode")) ? reverseInvoice.getString("reverseInvoiceCode") : null);
            reverseRecord.setInvoiceNo(Objects.nonNull(reverseInvoice.get("invoiceNo")) ? reverseInvoice.getString("invoiceNo") : null);
            reverseRecord.setInvoiceCode(Objects.nonNull(reverseInvoice.get("invoiceCode")) ? reverseInvoice.getString("invoiceCode") : null);
            reverseRecord.setReverseDate(Objects.nonNull(reverseInvoice.get("issueDate")) ? reverseInvoice.getDate("issueDate") : new Date());
            reverseRecord.setQrCodeId(reverseInvoice.getString("qrCodeId"));
            reverseRecord.setMid(reverseInvoice.getString("merchantId"));
            reverseRecord.setTid(reverseInvoice.getString("terminalId"));
            reverseRecord.setTradeId(reverseInvoice.getString("merOrderId"));
            reverseRecord.setOrderDate(reverseInvoice.getDate("merOrderDate"));
            reverseRecord.setTaxMethod(reverseInvoice.getString("taxMethod"));
            reverseRecord.setDeductionAmount(reverseInvoice.getBigDecimal("deductionAmount"));
            reverseRecord.setTotalPriceIncludingTax(reverseInvoice.getBigDecimal("totalPriceIncludingTax"));
            reverseRecord.setTotalTax(reverseInvoice.getBigDecimal("totalTax"));
            reverseRecord.setTotalPrice(reverseInvoice.getBigDecimal("totalPrice"));
            reverseRecord.setNotifyMobileNo(reverseInvoice.getString("notifyMobileNo"));
            reverseRecord.setPdfUrl(reverseInvoice.getString("pdfUrl"));
            reverseRecord.setPdfPreviewUrl(reverseInvoice.getString("pdfPreviewUrl"));
        } else {
            reverseRecord.setStatus(PayConstants.INVOICE_STATUS_CLOSED);
            reverseRecord.setInvoiceNo(oldInvoice.getInvoiceNo());
            reverseRecord.setInvoiceCode(oldInvoice.getInvoiceCode());
            reverseRecord.setMid(oldInvoice.getMid());
            reverseRecord.setTid(oldInvoice.getTid());
            reverseRecord.setTradeId(oldInvoice.getTradeId());
            reverseRecord.setOrderDate(oldInvoice.getOrderDate());
            reverseRecord.setRemark(reverse);
        }
        if (Objects.isNull(reverseRecord.getId())) {
            wxInvoiceReverseRecordMapper.insertWxInvoiceReverseRecord(reverseRecord);
        } else {
            wxInvoiceReverseRecordMapper.updateWxInvoiceReverseRecord(reverseRecord);
        }
        oldInvoice.setStatus(PayConstants.INVOICE_STATUS_REVERSED);
        oldInvoice.setReverseDate(reverseRecord.getReverseDate());
        wxInvoiceRecordMapper.updateWxInvoiceRecord(oldInvoice);
        return result;
    }
}
