package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WxVipUserPackageList {
    
    // VIP用户信息
    private WxSpecialUser vipUser;
    
    // VIP用户的车辆套餐列表
    private List<VipCarPackage> carPackages;
    
    @Data
    public static class VipCarPackage {
        // 车牌号
        private String plateNo;
        
        // 场库ID
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long warehouseId;
        
        // 场库名称
        private String warehouseName;
        
        // 会员套餐信息
        private WxUserPackage userPackage;
        
        // 是否有有效会员
        private Boolean hasValidMembership;
        
        // 会员状态描述
        private String membershipStatus;
    }
}
