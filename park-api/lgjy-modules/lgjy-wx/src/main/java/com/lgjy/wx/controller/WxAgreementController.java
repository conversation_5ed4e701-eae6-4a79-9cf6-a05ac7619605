package com.lgjy.wx.controller;

import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.wx.domain.WxAgreement;
import com.lgjy.wx.service.WxAgreementService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 微信小程序协议Controller
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@RestController
@RequestMapping("/agreement")
public class WxAgreementController extends BaseController {

    @Resource
    private WxAgreementService wxAgreementService;

    /**
     * 根据协议类型获取协议内容
     * @param agreementType 协议类型 1-用户服务协议 2-隐私协议 3-发票抬头协议
     * @return 协议信息
     */
    @GetMapping("/getByType")
    public R<WxAgreement> getByType(@RequestParam("agreementType") Integer agreementType) {
        try {
            // 参数校验
            if (agreementType == null) {
                return R.fail("协议类型不能为空");
            }

            if (agreementType < 1 || agreementType > 3) {
                return R.fail("协议类型参数错误");
            }

            // 直接查询协议信息
            WxAgreement agreement = wxAgreementService.selectAgreementByType(agreementType);

            if (agreement == null) {
                return R.fail("协议信息不存在");
            }

            return R.ok(agreement);

        } catch (Exception e) {
            logger.error("获取协议信息异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }
}
