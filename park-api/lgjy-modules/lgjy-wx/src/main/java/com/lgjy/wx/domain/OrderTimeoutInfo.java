package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单超时信息
 * 用于查询和传输订单基本信息，不再用于Redis缓存
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
public class OrderTimeoutInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 交易ID */
    private String tradeId;
    
    /** 用户ID */
    private Long userId;
    
    /** 场库ID */
    private Long warehouseId;
    
    /** 车牌号 */
    private String plateNo;
    
    /** 套餐ID */
    private Long packageId;
    
    /** 套餐名称 */
    private String packageName;
    
    /** 实际支付金额 */
    private BigDecimal actualPayment;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 支付状态 */
    private Integer payStatus;
    
    /** 用户手机号 */
    private String userPhone;
    
    /** 套餐类型 */
    private Integer vipType;
    
    /** 是否续费 */
    private Boolean isRenewal;
    
    /** 微信openid */
    private String openid;
    
    public OrderTimeoutInfo() {
    }
    
    public OrderTimeoutInfo(String tradeId, Long userId, Long warehouseId, String plateNo) {
        this.tradeId = tradeId;
        this.userId = userId;
        this.warehouseId = warehouseId;
        this.plateNo = plateNo;
        this.createTime = new Date();
    }
}
