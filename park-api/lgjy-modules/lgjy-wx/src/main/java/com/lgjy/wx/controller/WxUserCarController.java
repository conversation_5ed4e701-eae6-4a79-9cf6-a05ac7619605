package com.lgjy.wx.controller;

import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxUserCar;
import com.lgjy.wx.service.WxUserCarService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/car")
public class WxUserCarController extends BaseController {

    @Resource
    private WxUserCarService wxUserCarService;

    /**
     * 获取当前用户车辆列表
     */
    @GetMapping("/list")
    public AjaxResult list() {
        return success(wxUserCarService.selectWxUserCarList());
    }

    @PostMapping("/insert")
    public AjaxResult insertWxUserCar(@RequestBody WxUserCar wxUserCar) {
        int insert = wxUserCarService.insertWxUserCar(wxUserCar);
        if (insert <= 0) {
            return error("添加失败");
        }
        return success();
    }
    @PutMapping("/update")
    public AjaxResult updateWxUserCar(@RequestBody WxUserCar wxUserCar) {
        Integer update = wxUserCarService.updateWxUserCar(wxUserCar);
        if(update <= 0){
            return error("修改失败");
        }
        return success();
    }
    @DeleteMapping("/delete")
    public AjaxResult deleteWxUserCar(@RequestBody WxUserCar wxUserCar) {
        return success(wxUserCarService.deleteWxUserCar(wxUserCar.getId()));
    }
    @GetMapping("/detail")
    public AjaxResult selectWxUserCarById(@RequestParam Long id) {
        return success(wxUserCarService.selectWxUserCarById(id));
    }
}
