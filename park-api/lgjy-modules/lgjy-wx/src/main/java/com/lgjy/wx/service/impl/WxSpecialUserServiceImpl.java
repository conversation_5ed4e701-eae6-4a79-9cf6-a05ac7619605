package com.lgjy.wx.service.impl;

import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.wx.domain.WxSpecialUser;
import com.lgjy.wx.mapper.WxSpecialUserMapper;
import com.lgjy.wx.service.WxSpecialUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WxSpecialUserServiceImpl implements WxSpecialUserService {

    @Resource
    private WxSpecialUserMapper wxSpecialUserMapper;

    /**
     * 查询当前用户是否为特殊用户
     */
    @Override
    public WxSpecialUser selectSpecialUserDetail() {
        Long userId = SecurityUtils.getUserId();
        if(userId == null){
            throw new ServiceException("用户不存在");
        }
        WxSpecialUser wxSpecialUser = wxSpecialUserMapper.selectSpecialUserDetail(userId);
        return wxSpecialUser;
    }
}
