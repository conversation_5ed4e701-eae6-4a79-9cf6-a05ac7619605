package com.lgjy.wx.rt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.uuid.IdUtils;
import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.rt.config.UnionPayApiProperties;
import com.lgjy.wx.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * 银联支付工具类
 */
@Slf4j
@Component

public class UnionPayUtil {
    private final UnionPayApiProperties unionPayApiProperties;

    public UnionPayUtil(UnionPayApiProperties unionPayApiProperties) {
        this.unionPayApiProperties = unionPayApiProperties;
    }
    /**
     * 获取签名头
     *
     * @param body 请求体
     * @return authorization 认证报文
     * @throws Exception
     */
    public String getAuthorization(String body, Date now) throws Exception {
        String appId = unionPayApiProperties.getAppId();
        String timestamp = DateUtils.format(now, DateUtils.DATE_HOUR_MINUTE_SECOND_UNION_FORMATTER);
        String nonce = IdUtils.fastSimpleUUID();
        return "OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" +
                nonce + "\"" + ", Signature=" + "\"" + getSignature(body, timestamp, nonce) + "\"";
    }
    /**
     * 生成签名
     */
    public String getSignature(String body, String timestamp, String nonce) throws Exception {
        String appId = unionPayApiProperties.getAppId();
        String appKey = unionPayApiProperties.getAppKey();
        byte[] data = body.getBytes(StandardCharsets.UTF_8);
        InputStream is = new ByteArrayInputStream(data);
        String content = DigestUtils.sha256Hex(is);
        String sign = appId + timestamp + nonce + content;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] localSignature = mac.doFinal(sign.getBytes(StandardCharsets.UTF_8));
        return org.apache.commons.codec.binary.Base64.encodeBase64String(localSignature);
    }
    /**
     * 银联支付-生成商户订单号，字母区分
     */
    public String generateOrderNo() {
        String typeSign = "P";
        String sign = unionPayApiProperties.getSign();
        String dateStamp = DateUtils.format(new Date(), DateUtils.DATE_HOUR_MINUTE_SECOND_MILLISECOND_FORMATTER);
        return sign + dateStamp + randomNumbers(6) + typeSign;
    }
    public static String randomNumbers(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10)); // 生成0-9的随机数
        }
        return sb.toString();
    }

    /**
     * 发票计算sign
     */
    public String sign(LinkedHashMap<String, Object> paramMap) {
        String param = formatUrlParam(paramMap);
        String sign = param + unionPayApiProperties.getInvoiceKey();
        return getSHA256(sign).toUpperCase();
    }
    /**
     * 发票拼接参数
     */
    public static String formatUrlParam(LinkedHashMap<String, Object> param) {
        String params;
        try {
            List<Map.Entry<String, Object>> itmes = new ArrayList<>(param.entrySet());
            // 对所有传入的参数按照字段名从小到大排序
            itmes.sort(Map.Entry.comparingByKey());
            // 构造URL 键值对的形式
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Object> item : itmes) {
                if (StringUtils.isNotBlank(item.getKey())) {
                    String key = item.getKey();
                    String val = item.getValue().toString();
                    sb.append(key).append("=").append(val);
                    sb.append("&");
                }
            }
            params = sb.toString();
            if (!params.isEmpty()) {
                params = params.substring(0, params.length() - 1);
            }
        } catch (Exception e) {
            return "";
        }
        return params;
    }
    /**
     * 利用java原生的类实现SHA256加密
     */
    public static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (Exception e) {
            log.error("SHA256加密异常！");
        }
        return encodestr;
    }
    /**
     * 将byte转为16进制
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    /**
     * H5支付宝链接
     */
    public String h5payAlipay(String tradeId, Date now, BigDecimal pay,
                              String srcReserve, String notifyUrl,
                              String mid, String tid) throws Exception {
        String url = unionPayApiProperties.getUrl() + "/v1/netpay/trade/h5-pay";
        String timestamp = DateUtils.format(now, DateUtils.DATE_HOUR_MINUTE_SECOND_UNION_FORMATTER);
        String nonce = IdUtils.fastSimpleUUID();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("authorization", "OPEN-FORM-PARAM");
        params.add("appId", unionPayApiProperties.getAppId());
        params.add("timestamp", timestamp);
        params.add("nonce", nonce);
        JSONObject content = new JSONObject();
        content.put("requestTimestamp", DateUtils.format(now, DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        content.put("merOrderId", tradeId);
        content.put("mid", mid);
        content.put("tid", tid);
        content.put("instMid", "H5DEFAULT");
        content.put("totalAmount", pay.multiply(BigDecimal.valueOf(100)).intValue());
        content.put("expireTime", DateUtils.format(DateUtils.datePlusTime(now, unionPayApiProperties.getTimeout() * 60 * 1000), DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        content.put("notifyUrl", notifyUrl);
        if (StringUtils.isNotBlank(srcReserve)) content.put("srcReserve", srcReserve);
        params.add("content", JSON.toJSONString(content));
        String signature = getSignature(JSON.toJSONString(content), timestamp, nonce);
        params.add("signature", signature);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        URI uri = builder.queryParams(params).build().encode().toUri();
        return uri.toString().replaceAll("\\+", "%2B");
    }
}
