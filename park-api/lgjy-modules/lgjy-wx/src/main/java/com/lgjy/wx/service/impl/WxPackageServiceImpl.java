package com.lgjy.wx.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.wx.mapper.*;
import com.lgjy.wx.domain.ParkingInfoPojo;
import com.lgjy.system.api.RemoteGateService;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.*;
import com.lgjy.wx.rt.UnionPayApiRestTemplate;
import com.lgjy.wx.rt.UnionPayUtil;
import com.lgjy.wx.rt.config.UnionPayApiProperties;
import com.lgjy.wx.service.OrderTimeoutService;
import com.lgjy.wx.service.WxPackageService;
import com.lgjy.wx.service.WxParkingOrderService;
import com.lgjy.common.enums.SpecialUserPackageEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class WxPackageServiceImpl implements WxPackageService {

    @Resource
    private WxPackageMapper wxPackageMapper;

    @Resource
    private WxUserMapper wxUserMapper;

    @Resource
    private UnionPayUtil unionPayUtil;

    @Resource
    private WxUnionPayConfigMapper wxUnionPayConfigMapper;

    @Resource
    private UnionPayApiRestTemplate unionPayApiRestTemplate;

    @Resource
    private UnionPayApiProperties unionPayApiProperties;

    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Resource
    private RemoteGateService remoteGateService;

    @Resource
    private WxParkingOrderMapper wxParkingOrderMapper;

    @Resource
    private ParkingInfoMapper parkingInfoMapper;

    @Resource
    private WxSpecialUserMapper wxSpecialUserMapper;

    @Resource
    private WxSpecialUserCarMapper wxSpecialUserCarMapper;

    @Resource
    private WxWareHouseMapper wxWareHouseMapper;

    @Resource
    private OrderTimeoutService orderTimeoutService;

    /**
     * 根据场库id和套餐类型查套餐列表(某场库普通套餐列表)
     */
    @Override
    public List<WxPackage> selectWxPackageList(WxPackage wxPackage) {
        if(wxPackage == null|| wxPackage.getWarehouseId()==null){
            throw new ServiceException("查询参数错误，请重试");
        }
        return wxPackageMapper.selectWxPackageList(wxPackage);
    }

    /**
     * 查询某用户在某场库的某车的未过期套餐（未过期的会员信息）
     */
    @Override
    public WxUserPackage selectWxUserPackage(WxUserPackage wxUserPackage) {
        if(wxUserPackage == null|| wxUserPackage.getWarehouseId()==null
                || wxUserPackage.getPlateNo()==null || wxUserPackage.getVipType()==null){
            throw new ServiceException("参数错误");
        }
        Long userId = SecurityUtils.getUserId();
        if(userId == null){
            throw new ServiceException("用户不存在");
        }
        wxUserPackage.setUserId(userId);
        return wxPackageMapper.selectWxUserPackage(wxUserPackage);
    }

    /**
     * 查询用户的套餐购买记录
     */
    @Override
    public List<WxUserPackageRecord> selectWxUserPackageRecordList(WxUserPackageRecord wxUserPackageRecord) {
        Long userId = SecurityUtils.getUserId();
        if(userId == null){
            throw new ServiceException("用户不存在");
        }
        wxUserPackageRecord.setUserId(userId);
        List<WxUserPackageRecord> records = wxPackageMapper.selectWxUserPackageRecordList(wxUserPackageRecord);

        for(WxUserPackageRecord record : records){
            if(record.getCreateTime().getTime() < System.currentTimeMillis() - 15 * 60 * 1000
                    && record.getPayStatus() == PayConstants.ORDER_PAY_STATUS_PROGRESS){
                record.setPayStatus(PayConstants.ORDER_PAY_STATUS_CANCEL);
                wxPackageMapper.updateWxUserPackageRecord(record);
            }
        }
        // 填充套餐名称
        for (WxUserPackageRecord record : records) {
            fillPackageNameByVipType(record);
        }

        return records;
    }

    /**
     * 根据会员类型填充套餐名称
     *
     * @param record 交易记录
     */
    private void fillPackageNameByVipType(WxUserPackageRecord record) {
        if (record == null || record.getPackageId() == null) {
            return;
        }

        String packageName;
        if (record.getVipType() != null && record.getVipType() == 0) {
            // 普通会员：从数据库套餐表查询
            WxPackage pkg = wxPackageMapper.selectWxPackageById(record.getPackageId());
            packageName = pkg != null ? pkg.getPackageName() : "普通会员套餐";
        } else {
            // 集团客户(1)和VIP客户(2)：从枚举类查询
            SpecialUserPackageEnum packageEnum = SpecialUserPackageEnum.getEnum(record.getPackageId());
            packageName = packageEnum != null ? packageEnum.getName() : "特殊会员套餐";
        }

        record.setPackageName(packageName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject createOrder(WxUserPackageRecord record) {
        // 1. 参数校验与数据准备
        WxUser wxUser = validateAndPrepareOrderData(record);

        // 2. 处理套餐信息与价格
        handlePackageInfo(record, wxUser);

        // 3. 保存订单记录
        saveOrderRecord(record);

        // 4. 设置订单超时Redis键（15分钟后过期）
        orderTimeoutService.setOrderTimeoutKey(record);

        // 5. 处理特殊支付场景（零元支付）
        if (isSpecialFreePayment(record)) {
            handleFreePayment(record);
            // 零元支付成功，清理超时键
            orderTimeoutService.clearOrderTimeoutKey(record.getTradeId());
            return buildFreePaymentResponse();
        }

        // 6. 正常支付流程
        return initiatePaymentProcess(record);
    }

    /**
     * 参数校验与数据准备
     * @return 当前登录用户对象
     */
    private WxUser validateAndPrepareOrderData(WxUserPackageRecord record) {
        // 用户验证
        Long userId = SecurityUtils.getUserId();
        WxUser wxUser = wxUserMapper.selectUserById(userId);
        if (wxUser == null) {
            throw new ServiceException("用户不存在");
        }
        record.setUserId(userId);

        // 基本参数校验
        if (record.getWarehouseId() == null || record.getPlateNo() == null || record.getVipType() == null
        || record.getPackageId() == null) {
            throw new ServiceException("参数错误");
        }

        // 查询某车在某场库是否已经购买套餐且未过期
        WxUserPackage existing = wxPackageMapper.selectWxUserPackageByWarehouseIdAndPlateNo(
                record.getWarehouseId(), record.getPlateNo());

        if (existing != null) {
            // 检查是否属于其他用户
            if (!Objects.equals(existing.getUserId(), userId)) {
                throw new ServiceException("该车套餐已被其他用户购买");
            }

            // 检查套餐类型一致性
            if (!Objects.equals(existing.getVipType(), record.getVipType())) {
                throw new ServiceException("套餐类型不一致");
            }
        }

        return wxUser; // 返回用户对象
    }

    /**
     * 处理套餐信息与价格
     */
    private void handlePackageInfo(WxUserPackageRecord record, WxUser wxUser) {
        // 普通套餐处理，使用查询的套餐价格
        if (record.getVipType() == PayConstants.VIP_PACKAGE_TYPE_COMMON) {
            WxPackage pkg = wxPackageMapper.selectWxPackageById(record.getPackageId());
            if (pkg == null) {
                throw new ServiceException("套餐不存在");
            }
            record.setPaymentAmount(pkg.getPackagePrice());
            record.setActualPayment(pkg.getPackagePrice());
            // 普通套餐使用数据库中的用户手机号
            record.setPhoneNumber(wxUser.getPhoneNumber());
        }
        // VIP/集团套餐处理，使用传来的应付金额
        else {
            record.setPaymentAmount(record.getPackagePrice());
            record.setActualPayment(record.getPaymentAmount());

            // VIP/集团套餐手机号特殊处理
            if (record.getIndex() != null) {
                String phone = record.getPhoneNumber();
                if (phone != null && phone.length() > 1) {
                    phone = record.getIndex() + phone.substring(1);
                    record.setPhoneNumber(phone);
                }
            }
        }
    }

    /**
     * 保存订单记录
     */
    private void saveOrderRecord(WxUserPackageRecord record) {
        // 续费时间处理
        if (Boolean.TRUE.equals(record.getIsRenewal()) && record.getNewExpirationTime() != null) {
            record.setExpirationTime(record.getNewExpirationTime());
        }

        // 设置订单基本信息
        record.setId(snowflakeIdGenerator.nextId());
        record.setTradeId(unionPayUtil.generateOrderNo());
        record.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
        record.setDiscountAmount(BigDecimal.ZERO);
        record.setCreateTime(new Date());

        // 默认新购 1
        if (record.getOperateType() == null) {
            record.setOperateType(1);
        }
        if (record.getTransactTime() == null) {
            record.setTransactTime(new Date());
        }
        // 保存到数据库
        if (wxPackageMapper.insertWxUserPackageRecord(record) <= 0) {
            throw new ServiceException("创建订单失败");
        }
    }

    /**
     * 判断是否为特殊免费支付场景
     */
    private boolean isSpecialFreePayment(WxUserPackageRecord record) {
        return record.getVipType() == PayConstants.VIP_PACKAGE_TYPE_BIG_CUSTOMER &&
                record.getActualPayment().compareTo(BigDecimal.ZERO) == 0 &&
                record.getIndex() != null;
    }

    /**
     * 处理免费支付
     */
    private void handleFreePayment(WxUserPackageRecord record) {
        try {
            JSONObject body = new JSONObject();
            body.put("merOrderId", record.getTradeId());
            payCallback(body);
        } catch (Exception e) {
            log.error("零元支付回调失败: {}", record.getTradeId(), e);
            throw new ServiceException("零元支付处理失败");
        }
    }

    /**
     * 构建免费支付响应
     */
    private JSONObject buildFreePaymentResponse() {
        JSONObject response = new JSONObject();
        response.put("needPay", false);
        response.put("message", "零元支付处理成功");
        return response;
    }

    /**
     * 发起支付流程
     */
    private JSONObject initiatePaymentProcess(WxUserPackageRecord record) {
        try {
            // 获取支付配置
            WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                    record.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
            if (payConfig == null) {
                throw new ServiceException("支付参数缺失");
            }

            // 发起支付请求
            JSONObject payResult = unionPayApiRestTemplate.jsapi(
                    record.getTradeId(),
                    record.getActualPayment(),
                    new Date(),
                    unionPayApiProperties.getNotifyUrlVip(),
                    record.getOpenid(),
                    null,
                    payConfig.getMid(),
                    payConfig.getTid()
            );

            // 检查支付结果
            if (!PayConstants.WE_CHAT_PAY_SUCCESS.equals(payResult.getString("errCode"))) {
                throw new ServiceException("支付失败: " + payResult.getString("errMsg"));
            }

            // 构建响应
            JSONObject response = JSON.parseObject(JSON.toJSONString(payResult.get("miniPayRequest")));
            response.put("needPay", true);
            response.put("tradeId", record.getTradeId());
            return response;
        } catch (ServiceException e) {
            throw e; // 业务异常直接抛出
        } catch (Exception e) {
            log.error("支付调用异常: {}", record.getTradeId(), e);
            throw new ServiceException("支付处理失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void payCallback(JSONObject body) {
        String tradeId = body.getString("merOrderId");
        WxUserPackageRecord wxUserPackageRecord = wxPackageMapper.selectWxUserPackageRecordByTradeId(tradeId);
        // 订单存在且不是已支付
        if(!ObjectUtils.isEmpty(wxUserPackageRecord)&&!Objects.equals(wxUserPackageRecord.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PAID)){
            wxUserPackageRecord.setPayStatus(PayConstants.ORDER_PAY_STATUS_PAID);
            int update = wxPackageMapper.updateWxUserPackageRecord(wxUserPackageRecord);
            if(update >0){
               // 处理VIP套餐逻辑
               handleVipPackage(wxUserPackageRecord);

               // 支付成功，清理Redis超时键
               orderTimeoutService.clearOrderTimeoutKey(tradeId);
               log.info("支付成功，已清理订单超时键，tradeId: {}", tradeId);
            }
        }
    }

    /**
     * 会员支付回调
     * @param wxUserPackageRecord
     */
    private void handleVipPackage(WxUserPackageRecord wxUserPackageRecord) {
        // 道闸请求参数
        String parkingId = wxUserPackageRecord.getWarehouseId().toString();
        String plateNum = wxUserPackageRecord.getPlateNo();
        Date beginVipTime = wxUserPackageRecord.getBeginVipTime();
        Date endVipTime = wxUserPackageRecord.getExpirationTime();
        String userId = wxUserPackageRecord.getUserId().toString();

        // 构建请求数据JSON对象
        JSONObject requestData = new JSONObject();
        requestData.put("parkingId", parkingId);
        requestData.put("plateNum", plateNum);
        requestData.put("userId", userId);
        requestData.put("beginDate", beginVipTime);
        requestData.put("endDate", endVipTime);

        // 查询是否有旧的会员记录
        WxUserPackage wxUserPackage = new WxUserPackage();
        WxUserPackage oldUserPackage = wxPackageMapper.selectWxUserPackageOld(wxUserPackageRecord);
        if(oldUserPackage != null){
            wxUserPackage=oldUserPackage;
        }else{
            wxUserPackage.setId(snowflakeIdGenerator.nextId());
        }
        wxUserPackage.setWarehouseId(wxUserPackageRecord.getWarehouseId());
        wxUserPackage.setUserId(wxUserPackageRecord.getUserId());
        wxUserPackage.setPhoneNumber(wxUserPackageRecord.getPhoneNumber());
        wxUserPackage.setPlateNo(wxUserPackageRecord.getPlateNo());
        wxUserPackage.setVipType(wxUserPackageRecord.getVipType());
        wxUserPackage.setBeginVipTime(wxUserPackageRecord.getBeginVipTime());
        wxUserPackage.setEndVipTime(wxUserPackageRecord.getExpirationTime());
        wxUserPackage.setDeleteFlag(0);
        int rows =0;
        if(oldUserPackage != null){
            rows=wxPackageMapper.updateWxUserPackage(wxUserPackage);
        }else{
            rows=wxPackageMapper.insertWxUserPackage(wxUserPackage);
        }
        if(rows<0){
            throw new ServiceException("保存用户套餐信息失败");
        }
        // 如果是vip客户或者大客户，插入特殊用户车辆
        if(wxUserPackage.getVipType()!= PayConstants.VIP_PACKAGE_TYPE_COMMON){
            WxSpecialUserCar wxSpecialUserCar = wxSpecialUserCarMapper.selectCar(wxUserPackage);
            if(wxSpecialUserCar==null){
                wxSpecialUserCar = new WxSpecialUserCar();
                wxSpecialUserCar.setId(snowflakeIdGenerator.nextId());
                wxSpecialUserCar.setSpecialUserId(wxUserPackage.getUserId());
                wxSpecialUserCar.setWarehouseId(wxUserPackage.getWarehouseId());
                wxSpecialUserCar.setPhoneNumber(wxUserPackage.getPhoneNumber());
                wxSpecialUserCar.setPlateNo(wxUserPackage.getPlateNo());
                wxSpecialUserCar.setDeleteFlag(0);
                int update=wxSpecialUserCarMapper.insertWxSpecialUserCar(wxSpecialUserCar);
                if(update<=0){
                    throw new ServiceException("保存特殊用户车辆信息失败");
                }
            }
        }
        // 最后调用道闸，异常回滚
        R<String> result =remoteGateService.saveMonthCar(requestData, SecurityConstants.INNER);
        if(R.isError(result)){
            throw new ServiceException(result.getMsg()==null?"保存月租车失败":result.getMsg());
        }
    }

    /**
     * 购买套餐前，车辆是否在场（在场是否交过费，是否会员过期）
     */
    @Override
    public WxPackageJudge packageJudge(WxUserPackage wxUserPackage) {
        // 参数校验
        Long warehouseId = wxUserPackage.getWarehouseId();
        String plateNo = wxUserPackage.getPlateNo();
        if (warehouseId == null || plateNo == null) {
            throw new ServiceException("参数错误：仓库ID和车牌号不能为空");
        }

        WxPackageJudge wxPackageJudge = new WxPackageJudge();

        // 查询车辆在场信息
        List<ParkingInfoPojo> parkingInfoList = parkingInfoMapper.findParkingInfo(
                String.valueOf(warehouseId), plateNo);

        // 车辆不在场
        if (CollectionUtils.isEmpty(parkingInfoList)) {
            wxPackageJudge.setIsCarInWarehouse(false);
            return wxPackageJudge;
        }

        // 车辆在场处理
        ParkingInfoPojo parkingInfo = parkingInfoList.get(0);
        Date inTime = new Date(parkingInfo.getInTime() * 1000L);
        wxPackageJudge.setIsCarInWarehouse(true);

        // 1. 检查过期会员套餐
        WxUserPackage expiredMembership = wxPackageMapper.selectWxUserPackageNew(warehouseId, plateNo,null);
        Date endVipTime = processExpiredMembership(wxPackageJudge, expiredMembership, inTime);

        // 2. 检查临停缴费记录
        List<WxParkingOrder> parkingOrders = wxParkingOrderMapper.selectWxParkingOrderListByPackage(
                warehouseId, plateNo);
        Date endParkingTime = processParkingOrders(wxPackageJudge, parkingOrders, inTime);

        // 3. 确定最终开始时间
        determineFinalBeginTime(wxPackageJudge, endVipTime, endParkingTime, inTime);

        return wxPackageJudge;
    }

    // 处理过期会员套餐
    private Date processExpiredMembership(WxPackageJudge judge, WxUserPackage membership, Date inTime) {
        if (membership == null) return null;

        Date endVipTime = membership.getEndVipTime();
        if (endVipTime != null && endVipTime.after(inTime)) {
            judge.setIsWxUserPackageCoverInTime(true);
            judge.setEndVipTime(endVipTime);
        }
        return endVipTime;
    }

    // 处理临停缴费记录
    private Date processParkingOrders(WxPackageJudge judge, List<WxParkingOrder> orders, Date inTime) {
        if (CollectionUtils.isEmpty(orders)) return null;

        // 获取最新的临停订单（假设按时间倒序排列）
        WxParkingOrder latestOrder = orders.get(0);
        Date endParkingTime = latestOrder.getEndParkingTime();

        if (endParkingTime != null && endParkingTime.after(inTime)) {
            judge.setIsCarInWarehouseAndInTimeHasPaid(true);
            judge.setEndParkingTime(endParkingTime);
        }
        return endParkingTime;
    }

    // 确定最终开始时间
    private void determineFinalBeginTime(WxPackageJudge judge, Date endVipTime, Date endParkingTime, Date inTime) {
        // 使用三元运算符简化条件判断
        Date finalBeginTime = null;

        if (endVipTime != null && endParkingTime != null) {
            finalBeginTime = endVipTime.after(endParkingTime) ? endVipTime : endParkingTime;
        } else if (endVipTime != null) {
            finalBeginTime = endVipTime.after(inTime)? endVipTime : inTime;
        } else if (endParkingTime != null) {
            finalBeginTime = endParkingTime.after(inTime)? endParkingTime : inTime;
        } else {
            finalBeginTime = inTime;
        }

        judge.setFinalBeginTime(finalBeginTime);
    }

    /**
     * VIP用户查询所有车辆套餐
     */
    @Override
    public List<WxVipCarInfo> getWxVipUserPackageList(Integer vipType) {
        if(vipType==null){
            throw new ServiceException("参数错误：会员类型不能为空");
        }
        // 1. 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户不存在");
        }

        // 2. 根据用户ID查询用户信息
        WxUser wxUser = wxUserMapper.selectUserById(userId);
        if (wxUser == null) {
            throw new ServiceException("用户信息不存在");
        }

        // 3. 根据手机号查询特殊用户表，判断是否是VIP会员
        WxSpecialUser specialUser = wxSpecialUserMapper.selectSpecialUserByPhoneNumber(wxUser.getPhoneNumber());
        if (specialUser == null) {
            throw new ServiceException("您不是特殊用户");
        }

        // 5. 构建返回结果
        List<WxVipCarInfo> result = new ArrayList<>();

        // 6. 查询特殊用户车辆表，获取所有车辆
        List<WxSpecialUserCar> specialUserCars = wxSpecialUserCarMapper.selectCarsByPhoneNumber(wxUser.getPhoneNumber());
        if (CollectionUtils.isEmpty(specialUserCars)) {
            return result;
        }

        for (WxSpecialUserCar car : specialUserCars) {
            // 查询该车辆在该场库的会员套餐
            WxUserPackage userPackage = wxPackageMapper.selectWxUserPackageNew(car.getWarehouseId(),
                    car.getPlateNo(),vipType);

            // 判断会员是否有效
            if (userPackage != null) {
                Date now = new Date();
                if (userPackage.getEndVipTime() != null && userPackage.getEndVipTime().after(now)) {
                    // 会员有效，返回完整信息
                    // 需要查询场库名称
                    String warehouseName = getWarehouseName(car.getWarehouseId());
                    result.add(new WxVipCarInfo(
                        car.getPlateNo(),
                        car.getWarehouseId(),
                        warehouseName,
                        userPackage.getBeginVipTime(),
                        userPackage.getEndVipTime()
                    ));
                } else {
                    // 会员过期，只返回车牌号
                    result.add(new WxVipCarInfo(car.getPlateNo()));
                }
            } else {
                // 无会员记录，只返回车牌号
                result.add(new WxVipCarInfo(car.getPlateNo()));
            }
        }

        return result;
    }

    @Override
    public int updateOrder(WxUserPackageRecord wxUserPackageRecord) {
        return wxPackageMapper.updateWxUserPackageRecord(wxUserPackageRecord);
    }

    /**
     * 获取场库名称
     */
    private String getWarehouseName(Long warehouseId) {
        try {
            WxWareHouse warehouse = wxWareHouseMapper.getWarehouseById(warehouseId);
            return warehouse != null ? warehouse.getWarehouseName() : "未知场库";
        } catch (Exception e) {
            return "未知场库";
        }
    }
}
