package com.lgjy.wx.service.impl;

import com.lgjy.wx.domain.WxAdvertConfig;
import com.lgjy.wx.mapper.WxAdvertConifgMapper;
import com.lgjy.wx.service.WxAdvertConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class WxAdverConfigServiceImpl implements WxAdvertConfigService {

    @Resource
    private WxAdvertConifgMapper wxAdvertConifgMapper;

    /**
     * 查询轮播图配置信息列表
     */
    @Override
    public List<WxAdvertConfig> selectWxAdvertConfigList() {
        return wxAdvertConifgMapper.selectWxAdvertConfigList();
    }
}
