package com.lgjy.wx.mapper;

import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wx.domain.WxUserPackage;

public interface WxUserMapper {
    /**
     * 根据手机号查询用户信息
     * @param phoneNumber
     * @return
     */
    WxUser selectUserByPhoneNumber(String phoneNumber);

    /**
     * 根据openid查询用户
     *
     * @param openid
     * @return 用户信息
     */
    WxUser selectUserByOpenId(String openid);

    /**
     * 用户首次手机号验证码登录注册
     * @param wxUser
     * @return
     */
    int insertWxUser(WxUser wxUser);


    /**
     * 更新用户个人信息
     * @param wxUser
     * @return
     */
    int updateUserInfo(WxUser wxUser);

    /**
     * 根据用户id查询用户
     * @param userId
     * @return
     */
    WxUser selectUserById(Long userId);

}
