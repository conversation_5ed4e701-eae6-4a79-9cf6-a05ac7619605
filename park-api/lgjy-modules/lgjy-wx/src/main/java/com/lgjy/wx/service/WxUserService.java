package com.lgjy.wx.service;

import com.lgjy.system.api.domain.WxUser;

/**
 * 小程序用户 业务层
 *
 * <AUTHOR>
 */
public interface WxUserService {
    /**
     * 根据手机号查询用户
     *
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    WxUser selectUserByPhoneNumber(String phoneNumber);

    /**
     * 根据openid查询用户
     *
     * @param openid 手机号
     * @return 用户信息
     */
    WxUser selectUserByOpenId(String openid);

    /**
     * 用户首次手机号验证码登录注册
     * @param wxUser
     * @return
     */
    Boolean insertWxUser(WxUser wxUser);

    /**
     * 更新用户个人信息
     * @param wxUser
     * @return
     */
    Boolean updateUserInfo(WxUser wxUser);
}
