package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.ParkingInfoPojo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ParkingInfoMapper {
    /**
     * 入场时查询停车信息时间逆序
     * @param parkingId
     * @param plateNum
     * @return
     */
    List<ParkingInfoPojo> findParkingInfoByPlateNum(@Param("parkingId") String parkingId, @Param("plateNum") String plateNum);

    /**
     * 入场成功插入停车信息
     * @param parkingInfoPojo
     */
    void ParkingInfoInsert(ParkingInfoPojo parkingInfoPojo);

    /**
     * 出场查询入场记录
     * @param parkingId
     * @param plateNum
     * @param inTime
     * @return
     */
    List<ParkingInfoPojo> findParkingInfoByPlateNumAndInTime(@Param("parkingId") String parkingId,@Param("plateNum") String plateNum, @Param("inTime") long inTime);

    /**
     * 出场查询停车费
     * @param parkingId
     * @param plateNum
     * @return
     */
    List<ParkingInfoPojo> findParkingInfo(@Param("parkingId") String parkingId, @Param("plateNum") String plateNum);

    /**
     * 更新出场数据
     * @param parkingInfoPojo
     */
    void updateParkingInfo(ParkingInfoPojo parkingInfoPojo);
}
