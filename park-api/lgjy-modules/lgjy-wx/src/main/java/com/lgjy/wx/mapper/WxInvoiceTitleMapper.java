package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.WxInvoiceTitle;

import java.util.List;

/**
 * 发票抬头Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface WxInvoiceTitleMapper {
    
    /**
     * 查询发票抬头列表
     * 
     * @param wxInvoiceTitle 发票抬头
     * @return 发票抬头集合
     */
    List<WxInvoiceTitle> selectWxInvoiceTitleList(WxInvoiceTitle wxInvoiceTitle);

    /**
     * 新增发票抬头
     * 
     * @param wxInvoiceTitle 发票抬头
     * @return 结果
     */
    int insertWxInvoiceTitle(WxInvoiceTitle wxInvoiceTitle);

    /**
     * 修改发票抬头
     * 
     * @param wxInvoiceTitle 发票抬头
     * @return 结果
     */
    Integer updateWxInvoiceTitle(WxInvoiceTitle wxInvoiceTitle);

    /**
     * 根据ID查询发票抬头
     * 
     * @param id 发票抬头主键
     * @return 发票抬头
     */
    WxInvoiceTitle selectWxInvoiceTitleById(Long id);
}
