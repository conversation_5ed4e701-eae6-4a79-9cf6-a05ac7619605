package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class WxSpecialUserCar {
    // 主键ID
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    // 特殊用户ID
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long specialUserId;

    // 场库ID
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;

    // 手机号
    private String phoneNumber;

    // 车牌号
    private String plateNo;

    // 删除标记
    private Integer deleteFlag;

    // 创建者
    private Long createBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 更新者
    private Long updateBy;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
