package com.lgjy.wx.service;

import com.alibaba.fastjson.JSONObject;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxInvoiceRecord;

import java.util.List;

public interface WxInvoiceRecordService {

    /**
     * 查询开票记录列表
     */
    List<WxInvoiceRecord> selectWxInvoiceRecordList(WxInvoiceRecord wxInvoiceRecord);

    /**
     * 新增开票记录
     */
    void issueInvoice(WxInvoiceRecord wxInvoiceRecord);

    /**
     * 获取开票记录详细信息
     */
    WxInvoiceRecord selectWxInvoiceRecordById(Long id);

    /**
     * 开票回调
     */
    boolean invoiceCallback(JSONObject jsonObject);

    /**
     * 查询银联开票状态
     */
    JSONObject invoiceStatus(WxInvoiceRecord wxInvoiceRecord);

    /**
     * 发票抬头查询
     */
    JSONObject queryInvoiceTitle(WxInvoiceRecord wxInvoiceRecord);

    /**
     * 发票重开
     */
    AjaxResult resumeInvoice(WxInvoiceRecord wxInvoiceRecord);


    /**
     * 发送邮箱
     */
    void invoiceSend(WxInvoiceRecord wxInvoiceRecord);
}
