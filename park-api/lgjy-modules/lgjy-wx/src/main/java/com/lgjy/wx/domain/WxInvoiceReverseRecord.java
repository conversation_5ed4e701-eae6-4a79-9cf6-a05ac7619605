package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 红冲发票对象 sicv_invoice_reverse_record
 */
@Data
public class WxInvoiceReverseRecord {
    private static final long serialVersionUID=1L;

    /**
     * id
     */
    private Long id;

    private Long warehouseId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 发票记录id
     */
    private Long invoiceId;

    /**
     * 发票类型0普票,1专票
     */
    private Integer invoiceType;

    /**
     * 红字确认信息uuid
     */
    private String redConfirmedUuid;

    /**
     * 状态ISSUED:已开具 REVERSING:红冲中 REVERSED:已红冲 SPLITED:已拆分
     */
    private String status;

    /**
     * 红票的发票号码
     */
    private String reverseInvoiceNo;

    /**
     * 红票的发票代码
     */
    private String reverseInvoiceCode;

    /**
     * 蓝字发票票号
     */
    private String invoiceNo;

    /**
     * 蓝字发票代码
     */
    private String invoiceCode;

    /**
     * 红票开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reverseDate;

    /**
     * 发票二维码id
     */
    private String qrCodeId;

    /**
     * 银商商户号
     */
    private String mid;

    /**
     * 银商终端号
     */
    private String tid;

    /**
     * 商户订单号
     */
    private String tradeId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date orderDate;

    /**
     * 征税方式NORMAL:普通征税 DEDUCTION:差额征税
     */
    private String taxMethod;

    /**
     * 扣除额
     */
    private BigDecimal deductionAmount;

    /**
     * 含税总金额
     */
    private BigDecimal totalPriceIncludingTax;

    /**
     * 税额
     */
    private BigDecimal totalTax;

    /**
     * 不含税总金额
     */
    private BigDecimal totalPrice;

    /**
     * 消费者手机号
     */
    private String notifyMobileNo;

    /**
     * PDF下载链接
     */
    private String pdfUrl;

    /**
     * PDF预览链接
     */
    private String pdfPreviewUrl;

    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
