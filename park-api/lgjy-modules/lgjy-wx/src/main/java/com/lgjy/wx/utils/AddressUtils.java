package com.lgjy.wx.utils;

public class AddressUtils {
    /**
     * 拼接省市区地址（对上海等直辖市特殊处理）
     * @param provinceName 省份名称
     * @param cityName     城市名称
     * @param areaName     区县名称
     * @param address 详细地址
     * @return 完整地址字符串
     */
    public static String buildFullAddress(String provinceName, String cityName, String areaName, String address) {
        // 处理直辖市（上海、北京、天津、重庆）
        if (isMunicipality(provinceName)) {
            return String.format("%s%s%s", provinceName, areaName, address);
        }
        // 普通省市：省 + 市 + 区 + 详细地址
        return String.format("%s%s%s%s", provinceName, cityName, areaName, address);
    }

    /**
     * 判断是否为直辖市
     */
    private static boolean isMunicipality(String provinceName) {
        return provinceName != null &&
                (provinceName.contains("上海") ||
                        provinceName.contains("北京") ||
                        provinceName.contains("天津") ||
                        provinceName.contains("重庆"));
    }
}
