package com.lgjy.wx.controller;

import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxInvoiceTitle;
import com.lgjy.wx.service.WxInvoiceTitleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发票抬头Controller
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/invoiceTitle")
public class WxInvoiceTitleController extends BaseController {

    @Resource
    private WxInvoiceTitleService wxInvoiceTitleService;

    /**
     * 获取当前用户发票抬头列表
     */
    @GetMapping("/list")
    public AjaxResult list() {
        List<WxInvoiceTitle> list = wxInvoiceTitleService.selectWxInvoiceTitleList();
        return success(list);
    }

    /**
     * 新增发票抬头
     */
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody WxInvoiceTitle wxInvoiceTitle) {
        int result = wxInvoiceTitleService.insertWxInvoiceTitle(wxInvoiceTitle);
        if (result <= 0) {
            return error("添加失败");
        }
        return success();
    }

    /**
     * 修改发票抬头
     */
    @PutMapping("/update")
    public AjaxResult update(@RequestBody WxInvoiceTitle wxInvoiceTitle) {
        Integer result = wxInvoiceTitleService.updateWxInvoiceTitle(wxInvoiceTitle);
        if (result <= 0) {
            return error("修改失败");
        }
        return success();
    }

    /**
     * 删除发票抬头
     */
    @DeleteMapping("/delete")
    public AjaxResult delete(@RequestBody WxInvoiceTitle wxInvoiceTitle) {
        Integer result = wxInvoiceTitleService.deleteWxInvoiceTitle(wxInvoiceTitle.getId());
        if (result <= 0) {
            return error("删除失败");
        }
        return success();
    }

    /**
     * 根据ID获取发票抬头详情
     */
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam Long id) {
        WxInvoiceTitle wxInvoiceTitle = wxInvoiceTitleService.selectWxInvoiceTitleById(id);
        return success(wxInvoiceTitle);
    }
}
