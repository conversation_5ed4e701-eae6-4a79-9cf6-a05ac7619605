package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 红字确认对象 sicv_invoice_red_confirm
 */
@Data
public class WxInvoiceRedConfirm {
    private static final long serialVersionUID=1L;

    /**
     * id
     */
    private Long id;

    private Long warehouseId;

    /**
     * 用户id
     */
    private Long userId;

    private Long invoiceId;

    /**
     * 发票类型0普票,1专票
     */
    private Integer invoiceType;

    /**
     * 红字确认信息uuid
     */
    private String redConfirmedUuid;

    /**
     * 冲红原因01：开票有误 02：销货退回 03：服务中止 04：销售折让
     */
    private String applyReason;

    /**
     * 红冲状态N：未红冲 Y：已红冲
     */
    private String reverseState;

    /**
     * 购买方名称
     */
    private String buyerName;

    /**
     * 购买方纳税人识别号
     */
    private String buyerTaxCode;

    /**
     * 红字发票信息确认单号
     */
    private String redConfirmNo;

    /**
     * 确认单状态代码
     * 01 无需确认
     * 02 销方录入待购方确认
     * 03 购方录入待销方确认
     * 04 购销双方已确认
     * 05 作废（销方录入购方否认）
     * 06 作废（购方录入销方否认）
     * 07 作废（超 72 小时未确认）
     * 08 作废（发起方已撤销）
     * 09 作废（确认后撤销）
     */
    private String confirmState;

    /**
     * 蓝字发票代码
     */
    private String invoiceCode;

    /**
     * 蓝票合计金额
     */
    private BigDecimal totalPrice;

    /**
     * 蓝票合计税额
     */
    private BigDecimal totalTax;

    /**
     * 蓝票开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date issueDate;

    /**
     * 销售方名称
     */
    private String sellerName;

    /**
     * 销售方纳税人识别号
     */
    private String sellerTaxCode;

    /**
     * 录入方身份
     */
    private String entryIdentity;

    /**
     * 录入日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 确认日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmDate;

    /**
     * 红票发票号码
     */
    private String reverseInvoiceCode;

    /**
     * 红票开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reverseDate;

    /**
     * 红票冲销金额
     */
    private BigDecimal reverseTotalPrice;

    /**
     * 红票冲销税额
     */
    private BigDecimal reverseTotalTax;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    private String remark;
}
