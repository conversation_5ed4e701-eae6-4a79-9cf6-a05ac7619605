package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class WxUserPackage extends BaseEntity {
    // 主键ID
    private Long id;
    // 停车场ID
    private Long warehouseId;
    // 关联用户ID
    private Long userId;
    // 手机号码
    private String phoneNumber;
    // 车牌号
    private String plateNo;
    // VIP类型（0普通会员 1集团客户 2VIP客户 3团购会员）
    private Integer vipType;
    // VIP开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginVipTime;
    // VIP结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endVipTime;
    // 车位编号
    private String parkingSpaceNo;
    // 代理系统ID
    private Long dlySystemId;
    // 备注信息
    private String remark;
    // 删除标记（0-正常，1-删除）
    private Integer deleteFlag;
}
