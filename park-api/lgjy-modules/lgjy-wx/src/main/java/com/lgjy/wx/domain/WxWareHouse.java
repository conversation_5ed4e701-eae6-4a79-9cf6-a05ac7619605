package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class WxWareHouse {
    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 场库名称 */
    @Excel(name = "场库名称")
    private String warehouseName;

    /** 所属运营商ID */
    @Excel(name = "所属运营商ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /** 所属运营商名称 */
    @Excel(name = "所属运营商")
    private String operatorName;

    /** 智能类型 */
    @Excel(name = "智能类型")
    private Integer smartsType;

    /** 总停车位数量 */
    @Excel(name = "总停车位数量")
    private Integer totalParking;

    /** 预约数量 */
    @Excel(name = "预约数量")
    private Integer reservationNum;

    /** 充电桩数量 */
    @Excel(name = "充电桩数量")
    private Integer chargingStationNum;

    /** 停车场数量 */
    @Excel(name = "停车场数量")
    private Integer parkingLotCount;

    /** 省份代码 */
    @Excel(name = "省份代码")
    private String provinceCode;

    /** 城市代码 */
    @Excel(name = "城市代码")
    private String cityCode;

    /** 区域代码 */
    @Excel(name = "区域代码")
    private String areaCode;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 通道数量 */
    @Excel(name = "通道数量")
    private Integer passageNum;

    /** 负责人 */
    @Excel(name = "负责人")
    private String responsiblePerson;

    /** 联系信息 */
    @Excel(name = "联系信息")
    private String contactInfo;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 删除标识 0-未删除 1-已删除 */
    private Integer deleteFlag;

    /** 轮播图片(JSON格式) */
    @Excel(name = "轮播图片")
    private String carouselImages;

    /** 省份代码 */
    @Excel(name = "省份代码")
    private String province;

    /** 城市代码 */
    @Excel(name = "城市代码")
    private String city;

    /** 区域代码 */
    @Excel(name = "区域代码")
    private String area;
    /** 省份代码 */

    @Excel(name = "省份名称")
    private String provinceName;

    /** 城市代码 */
    @Excel(name = "城市名称")
    private String cityName;

    /** 区域代码 */
    @Excel(name = "区域名称")
    private String areaName;

    /** 详细地址 */
    private String addressDetail;

    /** 负责人电话 */
    private String managerPhone;

    /** 租赁物业编号 */
    @Excel(name = "租赁物业编号")
    private String leasePropertyNo;

    /** 租赁地址 */
    @Excel(name = "租赁地址")
    private String leaseAddress;

    /** 租赁详细地址 */
    @Excel(name = "租赁详细地址")
    private String leaseDetailAddress;
}
