package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.WxPackage;
import com.lgjy.wx.domain.WxUserPackage;
import com.lgjy.wx.domain.WxUserPackageRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface WxPackageMapper {
    /**
     * 根据场库id和套餐类型查套餐列表(某场库普通套餐列表)
     */
    List<WxPackage> selectWxPackageList(WxPackage wxPackage);

    /**
     * 查询某用户在某场库的某车的未过期套餐（未过期的会员信息）
     */
    WxUserPackage selectWxUserPackage(WxUserPackage wxUserPackage);

    /**
     * 查询用户的套餐购买记录
     */
    List<WxUserPackageRecord> selectWxUserPackageRecordList(WxUserPackageRecord wxUserPackageRecord);


    WxUserPackage selectWxUserPackageById(Long packageId);

    /**
     * 根据套餐id查询套餐详情
     * @param packageId
     * @return
     */
    WxPackage selectWxPackageById(Long packageId);

    /**
     * 查询某车在某场库是否已经购买套餐且未过期
     * @param warehouseId
     * @param plateNo
     */
    WxUserPackage selectWxUserPackageByWarehouseIdAndPlateNo(@Param("warehouseId") Long warehouseId,
                                                   @Param ("plateNo") String plateNo);

    /**
     * 根据订单号查询订单
     * @param tradeId
     * @return
     */
    WxUserPackageRecord selectWxUserPackageRecordByTradeId(String tradeId);

    /**
     * 创建订单
     * @param wxUserPackageRecord
     */
    int insertWxUserPackageRecord(WxUserPackageRecord wxUserPackageRecord);

    /**
     * 更新套餐订单
     * @param wxUserPackageRecord
     * @return
     */
    int updateWxUserPackageRecord(WxUserPackageRecord wxUserPackageRecord);

    /**
     * 更新超过15分钟的未支付订单
     * @param userId
     */
    void updateWxUserPackageExceedTime(Long userId);

    /**
     * 续费会员，更新会员记录
     * @param wxUserPackageRecord
     * @return
     */
    WxUserPackage selectWxUserPackageOld(WxUserPackageRecord wxUserPackageRecord);

    /**
     * 新增会员信息
     * @param wxUserPackage
     * @return
     */
    int insertWxUserPackage(WxUserPackage wxUserPackage);

    /**
     * 更新会员信息
     * @param wxUserPackage
     * @return
     */
    int updateWxUserPackage(WxUserPackage wxUserPackage);

    /**
     * 查询会员记录
     * @return
     */
    WxUserPackage selectWxUserPackageNew(@Param("warehouseId") long warehouseId,
                                         @Param("plateNo") String plateNo,@Param("vipType") Integer vipType
    );

    WxUserPackage selectWxUserPackageByPlateNo(@Param("plateNo")String plateNo,
                                               @Param("userId")Long userId);

    WxUserPackageRecord selectWxUserPackageRecordById(Long functionId);

    /**
     * 查询超时订单（备用处理任务使用）
     * @param timeoutTime 超时时间点
     * @param payStatus 支付状态
     * @return 超时订单列表
     */
    List<WxUserPackageRecord> selectTimeoutOrders(@Param("timeoutTime") Date timeoutTime,
                                                   @Param("payStatus") Integer payStatus);
}
