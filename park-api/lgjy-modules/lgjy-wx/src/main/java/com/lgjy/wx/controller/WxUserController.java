package com.lgjy.wx.controller;

import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.security.annotation.InnerAuth;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wx.service.WxUserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 手机号验证码登录用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class WxUserController extends BaseController {

    @Resource
    private WxUserService wxUserService;

    /**
     * 获取当前小程序用户信息通过手机号
     * @param phoneNumber
     * @return
     */
    @InnerAuth
    @GetMapping("/info/{phoneNumber}")
    public R<WxUser> info(@PathVariable("phoneNumber") String phoneNumber)
    {
        WxUser wxUser = wxUserService.selectUserByPhoneNumber(phoneNumber);
        return R.ok(wxUser);
    }
    /**
     * 获取当前小程序用户信息通过openid
     * @param openid
     * @return
     */
    @InnerAuth
    @GetMapping("/info/openid/{openid}")
    public R<WxUser> infoByOpenId(@PathVariable("openid") String openid)
    {
        WxUser wxUser = wxUserService.selectUserByOpenId(openid);
        return R.ok(wxUser);
    }

    /**
     * 用户首次手机号验证码登录注册
     * @param wxUser
     * @return
     */
    @InnerAuth
    @PostMapping("/insert")
    public R<Boolean> insertWxUser(@RequestBody WxUser wxUser)
    {
        return R.ok(wxUserService.insertWxUser(wxUser));
    }

    /**
     * 更新用户个人信息
     * @param wxUser
     * @return
     */
    @InnerAuth
    @PostMapping("/update")
    public R<Boolean> updateUserInfo(@RequestBody WxUser wxUser)
    {
        return R.ok(wxUserService.updateUserInfo(wxUser));
    }
}
