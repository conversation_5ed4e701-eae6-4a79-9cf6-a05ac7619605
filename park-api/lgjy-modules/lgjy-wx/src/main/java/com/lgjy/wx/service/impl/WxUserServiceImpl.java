package com.lgjy.wx.service.impl;

import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wx.mapper.WxUserMapper;
import com.lgjy.wx.service.WxUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WxUserServiceImpl implements WxUserService {

    @Resource
    private WxUserMapper wxUserMapper;

    /**
     * 根据手机号查询用户信息
     * @param phoneNumber 手机号
     * @return
     */
    @Override
    public WxUser selectUserByPhoneNumber(String phoneNumber) {
        return wxUserMapper.selectUserByPhoneNumber(phoneNumber);
    }

    /**
     * 根据openid查询用户
     *
     * @param openid
     * @return 用户信息
     */
    @Override
    public WxUser selectUserByOpenId(String openid) {
        return wxUserMapper.selectUserByOpenId(openid);
    }

    /**
     * 用户首次手机号验证码登录注册
     * @param wxUser
     * @return
     */
    @Override
    public Boolean insertWxUser(WxUser wxUser) {
        return wxUserMapper.insertWxUser(wxUser) > 0;
    }

    /**
     * 更新用户个人信息
     * @param wxUser
     * @return
     */
    @Override
    public Boolean updateUserInfo(WxUser wxUser) {
        return wxUserMapper.updateUserInfo(wxUser) > 0;
    }
}
