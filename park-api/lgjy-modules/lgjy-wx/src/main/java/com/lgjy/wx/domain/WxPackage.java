package com.lgjy.wx.domain;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class WxPackage {
    // 主键ID
    private Long id;

    // 停车场ID
    private Long warehouseId;

    // 套餐类型（套餐天数）
    private Integer packageType;

    // 套餐名称
    private String packageName;

    // 套餐价格
    private BigDecimal packagePrice;

    // 备注信息
    private String remark;

    // 删除标记（0-正常，1-删除）
    private Integer deleteFlag;

}
