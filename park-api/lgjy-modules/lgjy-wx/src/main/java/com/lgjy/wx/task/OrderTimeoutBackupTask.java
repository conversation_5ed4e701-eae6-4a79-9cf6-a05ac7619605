package com.lgjy.wx.task;

import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.WxUserPackageRecord;
import com.lgjy.wx.mapper.WxPackageMapper;
import com.lgjy.wx.service.OrderTimeoutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 订单超时备用处理任务
 * 作为Redis事件监听的备用方案，防止Redis事件丢失导致订单无法重新支付
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Component
@Slf4j
public class OrderTimeoutBackupTask {

    public OrderTimeoutBackupTask() {
        log.info("订单超时备用处理任务已初始化");
    }

    @Autowired
    private WxPackageMapper wxPackageMapper;
    
    @Autowired
    private OrderTimeoutService orderTimeoutService;
    
    private static final String ORDER_TIMEOUT_KEY_PREFIX = "order:timeout:";
    
    /**
     * 每5分钟执行一次，处理可能遗漏的超时订单
     * 这是Redis事件监听的备用方案
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void processTimeoutOrdersBackup() {
        try {
            log.info("开始执行订单超时备用处理任务");

            // 查询15分钟前创建且状态为待支付的订单
            List<WxUserPackageRecord> timeoutOrders = selectTimeoutOrders();

            if (timeoutOrders.isEmpty()) {
                log.info("订单超时备用处理任务完成，没有发现超时订单");
                return;
            }

            log.info("发现{}个可能超时的订单，开始备用处理", timeoutOrders.size());
            
            int processedCount = 0;
            for (WxUserPackageRecord order : timeoutOrders) {
                try {
                    // 检查Redis键是否存在
                    String timeoutKey = ORDER_TIMEOUT_KEY_PREFIX + order.getTradeId();
                    boolean isTimeout = orderTimeoutService.isOrderTimeout(order.getTradeId());
                    
                    if (isTimeout) {
                        // Redis键已过期但订单状态未更新，执行备用处理
                        boolean success = orderTimeoutService.handleOrderTimeoutManually(order.getTradeId());
                        if (success) {
                            processedCount++;
                            log.info("备用处理订单超时成功，tradeId: {}", order.getTradeId());
                        }
                    }
                } catch (Exception e) {
                    log.error("备用处理单个订单超时失败，tradeId: {}", order.getTradeId(), e);
                }
            }
            
            if (processedCount > 0) {
                log.info("订单超时备用处理任务完成，共处理{}个订单", processedCount);
            }
            
        } catch (Exception e) {
            log.error("备用超时处理任务执行失败", e);
        }
    }
    
    /**
     * 查询可能超时的订单
     * 查询条件：创建时间在15分钟前，且状态为待支付
     */
    private List<WxUserPackageRecord> selectTimeoutOrders() {
        // 计算15分钟前的时间
        Date fifteenMinutesAgo = new Date(System.currentTimeMillis() - 15 * 60 * 1000);
        
        // 这里需要在Mapper中添加相应的查询方法
        // 暂时返回空列表，等Mapper方法实现后再调用
        return wxPackageMapper.selectTimeoutOrders(fifteenMinutesAgo, PayConstants.ORDER_PAY_STATUS_PROGRESS);
    }
    

}
