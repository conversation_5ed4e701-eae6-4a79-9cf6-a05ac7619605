package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 发票抬头信息对象 wx_invoice_title
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class WxInvoiceTitle {

    /** 发票抬头ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 用户ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 发票类型（1=专用发票，2=普通发票） */
    private Integer invoiceType;

    /** 抬头类型（1=公司，2=个人） */
    private Integer titleType;

    /** 发票抬头内容/名称 */
    private String invoiceTitleContent;

    /** 纳税人识别号 */
    private String unitDutyParagraph;

    /** 注册地址 */
    private String registerAddress;

    /** 注册电话 */
    private String registerPhone;

    /** 开户银行 */
    private String depositBank;

    /** 银行账号 */
    private String bankAccount;

    /** 审核状态 */
    private Integer auditStatus;

    /** 删除标志（0未删除 1已删除） */
    private Integer deleteFlag;

    /** 创建者 */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
