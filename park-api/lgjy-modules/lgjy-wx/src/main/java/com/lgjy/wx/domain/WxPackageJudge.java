package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class WxPackageJudge {

    // 车辆是否在场
    private Boolean isCarInWarehouse;

    // 车辆在场，且在场期间，会员是否过期
    private Boolean isWxUserPackageCoverInTime;

    // 车辆在场，且在场期间，会员过期时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endVipTime;

    // 车辆在场，且在场期间，是否进行过临停缴费
    private Boolean isCarInWarehouseAndInTimeHasPaid;

    // 车辆在场，且在场期间，进行过临停缴费的时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endParkingTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finalBeginTime;
}
