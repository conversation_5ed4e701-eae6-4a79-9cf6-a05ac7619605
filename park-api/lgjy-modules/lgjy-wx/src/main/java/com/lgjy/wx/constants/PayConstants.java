package com.lgjy.wx.constants;

public class PayConstants {
    // 订单-待支付
    public static final int ORDER_PAY_STATUS_PROGRESS = 1;

    // 订单-支付中
    public static final int ORDER_PAY_STATUS_PAYMENT = 2;

    // 订单-已取消
    public static final int ORDER_PAY_STATUS_CANCEL = 3;

    // 订单-已支付
    public static final int ORDER_PAY_STATUS_PAID = 5;

    // 订单-已退款
    public static final int ORDER_PAY_STATUS_REFUND = 4;

    // 功能常量：1停车
    public static final int INVOICE_FUNCTION_TYPE_PARK = 1;

    // 功能常量：2会员
    public static final int INVOICE_FUNCTION_TYPE_VIP = 2;

    // 支付方式-支付宝
    public static final int PAY_TYPE_ALIPAY = 1;

    // 支付方式-微信
    public static final int PAY_TYPE_WE_CHAT = 2;

    // 车辆类型-临时车
    public static final String CAR_TYPE_TEMPORARY_CAR = "临时车";

    // 微信支付-成功
    public static final String WE_CHAT_PAY_SUCCESS = "SUCCESS";

    // 订单业务类型-小程序
    public static final String ORDER_INST_MID_MINI = "MINIDEFAULT";

    // 订单业务类型-H5
    public static final String ORDER_INST_MID_H5 = "H5DEFAULT";

    // 套餐类型-0普通
    public static final int VIP_PACKAGE_TYPE_COMMON = 0;

    // 套餐类型-大客户
    public static final int VIP_PACKAGE_TYPE_BIG_CUSTOMER = 1;

    // 套餐类型-VIP客户
    public static final int VIP_PACKAGE_TYPE_VIP_CUSTOMER = 2;

    // 是否是无牌车
    public static final int IS_NO_PLATE_CAR = 1;

    // 是否是有牌车
    public static final int IS_PLATE_CAR = 0;

    /**
     * 发票状态常量：未开
     */
    public static final String INVOICE_STATUS_UNISSU = "UNISSU";
    /**
     * 发票状态常量：已开具
     */
    public static final String INVOICE_STATUS_ISSUED = "ISSUED";
    /**
     * 发票状态常量：开具中
     */
    public static final String INVOICE_STATUS_ISSUING = "ISSUING";
    /**
     * 发票状态常量：已关闭
     */
    public static final String INVOICE_STATUS_CLOSED = "CLOSED";
    /**
     * 发票状态常量：已红冲
     */
    public static final String INVOICE_STATUS_REVERSED = "REVERSED";

    /**
     * 发票-红字确认单确认状态：无需确认
     */
    public static final String INVOICE_RED_CONFIRM_STATUS_OK = "01";
    /**
     * 发票-红字确认单确认状态：已确认
     */
    public static final String INVOICE_RED_CONFIRM_STATUS_CONFIRMED = "04";

}
