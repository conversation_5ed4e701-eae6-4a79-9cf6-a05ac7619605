package com.lgjy.wx.controller;

import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxWareHouse;
import com.lgjy.wx.service.WxWareHouseService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/warehouse")
public class WxWarehouseController extends BaseController {

    @Resource
    private WxWareHouseService WxWarehouseService;

    /**
     * 查询停车场库信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(WxWareHouse wxWarehouse)
    {
        List<WxWareHouse> list = WxWarehouseService.selectWxWarehouseList(wxWarehouse);
        return success(list);
    }

    /**
     * 根据ID获取场库详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        WxWareHouse warehouse = WxWarehouseService.getWarehouseById(id);
        return success(warehouse);
    }
}
