package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WxUserPackageRecord extends BaseEntity {
    // 主键ID
    private Long id;

    // 停车场ID
    private Long warehouseId;

    // 套餐ID
    private Long packageId;

    // 用户ID
    private Long userId;

    // 手机号码（长度32）
    private String phoneNumber;

    // 车牌号（长度32）
    private String plateNo;

    // VIP类型
    private Integer vipType;

    // VIP开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginVipTime;

    // 操作类型（1-新购，2-赠送）
    private Integer operateType;

    // 交易时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transactTime;

    // VIP到期时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expirationTime;

    // 应付金额（11位整数，2位小数）
    private BigDecimal paymentAmount;

    // 套餐价格（11位整数，2位小数）- 保留用于兼容性
    private BigDecimal packagePrice;

    // 优惠金额（11位整数，2位小数）
    private BigDecimal discountAmount;

    // 实际支付金额（11位整数，2位小数）
    private BigDecimal actualPayment;

    // 交易ID（长度64）
    private String tradeId;

    // 支付状态：1-待支付,2-支付成功,3-支付失败
    private Integer payStatus;

    // 发票ID
    private Long invoiceId;

    // 停车位号（长度32）
    private String parkingSpaceNo;

    // 团购记录ID
    private Long groupBuyRecordId;

    // 选择时间
    private Date chooseTime;

    // 删除标记：0-正常,1-删除
    private Integer deleteFlag;

    // 场库名称
    private String warehouseName;

    // 套餐名称
    private String packageName;

    // openid
    private String openid;

    // 是否是续费套餐
    private Boolean isRenewal;

    // VIP结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newExpirationTime;

    // 用于vip或集团客户的套餐（标记是第几辆车）
    private Integer index;

}
