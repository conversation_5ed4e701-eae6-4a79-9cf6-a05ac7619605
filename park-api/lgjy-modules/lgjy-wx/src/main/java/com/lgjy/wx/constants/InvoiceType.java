package com.lgjy.wx.constants;

import java.util.Objects;

public enum InvoiceType {
    // 普通发票
    INVOICE_TYPE_PLAIN(0, "PLAIN"),

    // 专用发票
    INVOICE_TYPE_VAT(1, "VAT");

    private final Integer code;
    private final String name;

    InvoiceType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static Integer getCode(String name) {
        if (null == name) {
            return null;
        }
        for (InvoiceType item : InvoiceType.values()) {
            if (Objects.equals(item.getName(), name)) {
                return item.getCode();
            }
        }
        return null;
    }

    public static String getName(Integer code) {
        if (null == code) {
            return null;
        }
        for (InvoiceType item : InvoiceType.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item.getName();
            }
        }
        return null;
    }
}
