package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WxInvoiceRecord extends BaseEntity {
    /**
     * id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 场库id
     */
    private Long warehouseId;

    /**
     * 功能类型：1停车，2会员，3充电，4商户，5团购
     */
    private Integer functionType;

    /**
     * 发票类型 0普票,1专票
     */
    private Integer invoiceType;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date issueDate;

    /**
     * 开票金额
     */
    private BigDecimal totalMoney;

    /**
     * 主体名称
     */
    private String invoiceTitleContent;

    /**
     * 单位税号
     */
    private String unitDutyParagraph;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerPhone;

    /**
     * 开户银行
     */
    private String depositBank;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 推送短信手机号
     */
    private String notifyMobileNo;

    /**
     * 推送PDF文件的邮箱
     */
    private String notifyEmail;

    /**
     * PDF下载链接
     */
    private String pdfUrl;

    /**
     * 商户订单号
     */
    private String tradeId;

    /**
     * 开票状态 PENDING:待开具 ISSUING:开具中 ISSUED:已开具 REVERSING:红冲中 REVERSED：已红冲 CLOSED:已关闭
     */
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    /**
     * 功能id
     */
    private Long functionId;

    private String remark;

    private Long titleId;

    // 红冲日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date reverseDate;

    // 二维码id
    private String qrCodeId;

    // 发票代码
    private String invoiceCode;

    /**
     * mid
     */
    private String mid;

    /**
     * tid
     */
    private String tid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date orderDate;

    /**
     * pdfPreviewUrl
     */
    private String pdfPreviewUrl;

    private BigDecimal totalTax;

    private Boolean reopenSign;

    /**
     * 商户订单号
     */
    private String oldTradeId;

    private Boolean isResume;
}
