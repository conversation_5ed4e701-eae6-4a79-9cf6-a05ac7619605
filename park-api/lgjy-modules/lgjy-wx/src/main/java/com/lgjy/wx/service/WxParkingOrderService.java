package com.lgjy.wx.service;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxParkingOrder;

import java.math.BigDecimal;
import java.util.List;

public interface WxParkingOrderService {
    /**
     * 根据车牌号查询订单
     * @param wxParkingOrder
     * @return
     */
    WxParkingOrder getParkingOrderByPlateNo(WxParkingOrder wxParkingOrder);

    /**
     * 查询当前用户的临停订单列表
     */
    List<WxParkingOrder> getParkingOrderList(WxParkingOrder orderParams);

    /**
     * 停车缴费预下单(创建订单）
     * @param wxParkingOrder
     * @return
     */
    JSONObject createParkingOrder(WxParkingOrder wxParkingOrder);

    /**
     * 银联订单支付回调
     * @param body
     */
    void payCallback(JSONObject body);

    /**
     * 银联订单支付回调(支付宝)
     * @param body
     */
    void payCallbackAlipay(JSONObject body);

    /**
     * 前端支付成功-回调
     * @param tradeId
     * @return
     */
    void payFrontPayCallback(String tradeId);

    /**
     * 道闸口出口查询订单
     */
    WxParkingOrder channelPayQuery(WxParkingOrder wxParkingOrder);

    /**
     * 临停缴费-支付宝H5
     */
    String paymentTemporaryAlipay(WxParkingOrder wxParkingOrder);

    /**
     * 临停缴费-微信H5 预下单
     */
    JSONObject paymentTemporary(WxParkingOrder wxParkingOrder);

    /**
     * 无牌车 入场
     */
    void noPlateIn(WxParkingOrder sicvParkingOrder);

    /**
     * 无牌车 出场，预下单
     */
    AjaxResult noPlateOut(WxParkingOrder wxParkingOrder);

    /**
     * 停车订单退款
     * @param tradeId 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    JSONObject refundParkingOrder(String tradeId, BigDecimal refundAmount, String refundReason);

}
