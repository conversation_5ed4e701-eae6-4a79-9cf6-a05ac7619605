package com.lgjy.wx.event;

import org.springframework.context.ApplicationEvent;

/**
 * Redis键过期事件
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public class RedisKeyExpiredEvent extends ApplicationEvent {
    
    private static final long serialVersionUID = 1L;
    
    private final String expiredKey;
    
    public RedisKeyExpiredEvent(Object source, String expiredKey) {
        super(source);
        this.expiredKey = expiredKey;
    }
    
    public String getExpiredKey() {
        return expiredKey;
    }
    
    @Override
    public String toString() {
        return "RedisKeyExpiredEvent{" +
                "expiredKey='" + expiredKey + '\'' +
                ", source=" + getSource() +
                '}';
    }
}
