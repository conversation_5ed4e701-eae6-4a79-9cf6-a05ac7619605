package com.lgjy.wx.constants;


import java.util.Objects;

public enum ParkInfoEnums {
    PARK_INFO_HY(Constants.WAREHOUSE_ID_HY, "鸿音广场", "上海市&浦东新区", "南芦公路1755弄", "228274"),

    PARK_INFO_NC(Constants.WAREHOUSE_ID_NC, "LIN舍公寓", "上海市&浦东新区", "云端路833弄", "000661"),

    PARK_INFO_ZH(Constants.WAREHOUSE_ID_ZHY, "泽辉苑", "上海市&浦东新区", "泥城镇云端路1059弄", "000570"),

    PARK_INFO_ZYY(Constants.WAREHOUSE_ID_ZYY, "著雨苑", "上海市&浦东新区", "泥城镇群峰路275弄", "000565"),

    PARK_INFO_LCY(Constants.WAREHOUSE_ID_LCY, "林彩苑", "上海市&浦东新区", "泥城镇云端路1210弄", "000562"),

    PARK_INFO_FFY(Constants.WAREHOUSE_ID_FFY, "芳菲苑", "上海市&浦东新区", "泥城镇正茂路1397弄", "000559"),

    PARK_INFO_YLY(Constants.WAREHOUSE_ID_YLY, "云乐苑", "上海市&浦东新区", "海基一路288弄", "000477"),

    PARK_INFO_XLY(Constants.WAREHOUSE_ID_XLY, "熙岚苑", "上海市&浦东新区", "海基一路388弄", "000496"),

    PARK_INFO_XNY(Constants.WAREHOUSE_ID_XNY, "星凝苑", "上海市&浦东新区", "海基一路588弄", "000470"),

    PARK_INFO_YWY(Constants.WAREHOUSE_ID_YWY, "月微苑", "上海市&浦东新区", "木荷路515弄", "000475"),

    PARK_INFO_ZLY(Constants.WAREHOUSE_ID_ZLY, "朝露苑", "上海市&浦东新区", "海洋七路999弄", "000480"),

    PARK_INFO_XYCXY(Constants.WAREHOUSE_ID_XYCXY, "新元畅想苑", "上海市&闵行区", "建泽路260弄", "001966"),

    PARK_INFO_XYLXY(Constants.WAREHOUSE_ID_XYLXY, "新元理想苑", "上海市&闵行区", "建润路172弄", "032433"),

    PARK_INFO_GFMD(Constants.WAREHOUSE_ID_GFMD, "广丰名都", "上海市&奉贤区", "广丰路1288弄", "000661"),

    PARK_INFO_CXY(Constants.WAREHOUSE_ID_CXY, "春晓苑", "上海市&浦东新区", "泥城镇云端路1566弄", "033596"),

    PARK_INFO_YYY(Constants.WAREHOUSE_ID_YYY, "雨浥苑", "上海市&浦东新区", "泥城镇云端路1565弄", "036356"),

    PARK_INFO_LBY(Constants.WAREHOUSE_ID_LBY, "凌波苑", "上海市&浦东新区", "泥城镇云端路1388弄", "208791"),

    PARK_INFO_HYY(Constants.WAREHOUSE_ID_HYY, "海云苑", "上海市&浦东新区", "泥城镇云端路1389弄", "208789"),

    PARK_INFO_XYSJY(Constants.WAREHOUSE_ID_XYSJY, "新元盛璟苑", "上海市&浦东新区", "泥城镇群峰路128弄", "000357"),

    PARK_INFO_HTMY(Constants.WAREHOUSE_ID_HTMY, "华亭茗苑", "上海市&松江区", "九亭镇九亭中心路1450弄", "002196"),

    PARK_INFO_HFMD(Constants.WAREHOUSE_ID_HFMD, "汇丰名都", "上海市&奉贤区", "汇丰西路1399弄", "000661"),

    PARK_INFO_XJY(Constants.WAREHOUSE_ID_XJY, "熙景苑", "上海市&奉贤区", "汇丰西路1399弄", "000661"),

    PARK_INFO_XYY(Constants.WAREHOUSE_ID_XYY, "新雨苑", "上海市&奉贤区", "汇丰西路1399弄", "000661"),

    PARK_INFO_QTY(Constants.WAREHOUSE_ID_QTY, "清涛苑", "上海市&奉贤区", "汇丰西路1399弄", "000661"),

    PARK_INFO_LHY(Constants.WAREHOUSE_ID_LHY, "露华苑", "上海市&奉贤区", "汇丰西路1399弄", "000661");


    private final Long id;
    private final String name;
    private final String address; // 地址
    private final String detailAddress; // 后续详细地址
    private final String leasePropertyNo; // 不动产证号

    ParkInfoEnums(Long id, String name, String address, String detailAddress, String leasePropertyNo) {
        this.id = id;
        this.name = name;
        this.address = address;
        this.detailAddress = detailAddress;
        this.leasePropertyNo = leasePropertyNo;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getAddress() {
        return address;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public String getLeasePropertyNo() {
        return leasePropertyNo;
    }

    public static ParkInfoEnums getInfo(Long id) {
        if (null == id) {
            return null;
        }
        for (ParkInfoEnums item : ParkInfoEnums.values()) {
            if (Objects.equals(item.getId(), id)) {
                return item;
            }
        }
        return null;
    }
}
