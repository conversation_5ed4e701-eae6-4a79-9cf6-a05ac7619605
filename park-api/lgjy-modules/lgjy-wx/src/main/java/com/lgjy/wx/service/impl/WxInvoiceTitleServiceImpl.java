package com.lgjy.wx.service.impl;

import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.wx.domain.WxInvoiceTitle;
import com.lgjy.wx.mapper.WxInvoiceTitleMapper;
import com.lgjy.wx.service.WxInvoiceTitleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发票抬头Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class WxInvoiceTitleServiceImpl implements WxInvoiceTitleService {

    @Resource
    private WxInvoiceTitleMapper wxInvoiceTitleMapper;

    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public List<WxInvoiceTitle> selectWxInvoiceTitleList() {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户不存在");
        }
        WxInvoiceTitle wxInvoiceTitle = new WxInvoiceTitle();
        wxInvoiceTitle.setUserId(userId);
        wxInvoiceTitle.setDeleteFlag(0);
        return wxInvoiceTitleMapper.selectWxInvoiceTitleList(wxInvoiceTitle);
    }

    @Override
    public Integer insertWxInvoiceTitle(WxInvoiceTitle wxInvoiceTitle) {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户不存在");
        }
        wxInvoiceTitle.setId(snowflakeIdGenerator.nextId());
        wxInvoiceTitle.setUserId(userId);
        wxInvoiceTitle.setDeleteFlag(0);
        wxInvoiceTitle.setAuditStatus(0); // 默认待审核
        wxInvoiceTitle.setCreateBy(userId);
        wxInvoiceTitle.setCreateTime(new Date());
        wxInvoiceTitle.setUpdateBy(userId);
        wxInvoiceTitle.setUpdateTime(new Date());
        return wxInvoiceTitleMapper.insertWxInvoiceTitle(wxInvoiceTitle);
    }

    @Override
    public Integer updateWxInvoiceTitle(WxInvoiceTitle wxInvoiceTitle) {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户不存在");
        }
        wxInvoiceTitle.setUpdateBy(userId);
        wxInvoiceTitle.setUpdateTime(new Date());
        return wxInvoiceTitleMapper.updateWxInvoiceTitle(wxInvoiceTitle);
    }

    @Override
    public Integer deleteWxInvoiceTitle(Long id) {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户不存在");
        }
        WxInvoiceTitle wxInvoiceTitle = new WxInvoiceTitle();
        wxInvoiceTitle.setId(id);
        wxInvoiceTitle.setDeleteFlag(1);
        wxInvoiceTitle.setUpdateBy(userId);
        wxInvoiceTitle.setUpdateTime(new Date());
        return wxInvoiceTitleMapper.updateWxInvoiceTitle(wxInvoiceTitle);
    }

    @Override
    public WxInvoiceTitle selectWxInvoiceTitleById(Long id) {
        return wxInvoiceTitleMapper.selectWxInvoiceTitleById(id);
    }
}
