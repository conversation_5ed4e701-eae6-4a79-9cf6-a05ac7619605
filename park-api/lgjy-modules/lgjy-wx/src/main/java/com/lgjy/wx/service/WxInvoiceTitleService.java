package com.lgjy.wx.service;

import com.lgjy.wx.domain.WxInvoiceTitle;

import java.util.List;

/**
 * 发票抬头Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface WxInvoiceTitleService {
    
    /**
     * 查询当前用户发票抬头列表
     * 
     * @return 发票抬头集合
     */
    List<WxInvoiceTitle> selectWxInvoiceTitleList();

    /**
     * 新增发票抬头
     * 
     * @param wxInvoiceTitle 发票抬头
     * @return 结果
     */
    Integer insertWxInvoiceTitle(WxInvoiceTitle wxInvoiceTitle);

    /**
     * 修改发票抬头
     * 
     * @param wxInvoiceTitle 发票抬头
     * @return 结果
     */
    Integer updateWxInvoiceTitle(WxInvoiceTitle wxInvoiceTitle);

    /**
     * 删除发票抬头
     * 
     * @param id 发票抬头主键
     * @return 结果
     */
    Integer deleteWxInvoiceTitle(Long id);

    /**
     * 根据ID查询发票抬头
     * 
     * @param id 发票抬头主键
     * @return 发票抬头
     */
    WxInvoiceTitle selectWxInvoiceTitleById(Long id);
}
