package com.lgjy.wx.service;

import com.lgjy.wx.domain.OrderTimeoutInfo;
import com.lgjy.wx.domain.WxUserPackageRecord;

/**
 * 订单超时服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface OrderTimeoutService {
    
    /**
     * 设置订单超时Redis键
     * 
     * @param record 订单记录
     */
    void setOrderTimeoutKey(WxUserPackageRecord record);
    
    /**
     * 清理订单超时Redis键
     * 
     * @param tradeId 交易ID
     */
    void clearOrderTimeoutKey(String tradeId);
    
    /**
     * 检查订单是否超时
     * 
     * @param tradeId 交易ID
     * @return 是否超时
     */
    boolean isOrderTimeout(String tradeId);
    
    /**
     * 获取订单超时信息
     * 
     * @param tradeId 交易ID
     * @return 订单超时信息
     */
    OrderTimeoutInfo getOrderTimeoutInfo(String tradeId);
    
    /**
     * 手动处理订单超时（用于补偿机制）
     * 
     * @param tradeId 交易ID
     * @return 处理结果
     */
    boolean handleOrderTimeoutManually(String tradeId);
}
