package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WxParkingOrder extends BaseEntity {
    // 主键id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    // 停车场id
    private Long warehouseId;

    // 车位id（暂时没有）
    private Long parkingManageId;

    // 用户id
    private Long userId;

    // 车牌号
    private String plateNo;

    // 开始停车时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginParkingTime;

    // 结束停车时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endParkingTime;

    // 停车时长（单位：分钟）
    private Integer parkingDuration;

    // 缴费金额
    private BigDecimal paymentAmount;

    // 优惠金额
    private BigDecimal discountAmount;

    // 实付金额（缴费金额 - 优惠金额）
    private BigDecimal actualPayment;

    // 支付方式(0:现金;1:支付宝;2:微信;99:其他)
    private Integer payType;

    // 预约id(暂时没有）
    private Long parkingReservationId;

    // 发票id
    private Long invoiceId;

    // 商户订单号（支付系统唯一标识）
    private String tradeId;

    // 订单支付状态（1进行中 2支付中 5已支付）
    private Integer payStatus;

    // 支付完成时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paymentTime;

    // 无牌用户标识
    private String openId;

    // 车辆类型（如：小型车、大型车等）
    private String carType;

    // 删除标志（0：未删除，1：已删除）
    private Boolean deleteFlag;

    // 场库名称
    private String warehouseName;

    // 订单是否已完成（true：已完成，false：未完成）
    private Boolean isComplete;

    // 道闸编号(道闸出口，查询停车费用）
    private Integer gateNo;

    // 发票记录
    private WxInvoiceRecord miniInvoiceRecord;

}
