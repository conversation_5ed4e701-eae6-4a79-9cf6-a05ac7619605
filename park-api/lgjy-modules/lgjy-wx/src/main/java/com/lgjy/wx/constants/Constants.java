package com.lgjy.wx.constants;

public class Constants {
    /**
     * 成功标记
     */
    public static final Integer SUCCESS = 1;

    /**
     * 失败标记
     */
    public static final Integer FAIL = 0;

    /**
     * 车位类型-普通车位
     */
    public static final Integer COMMON_PARKING = 1;

    /**
     * 车位类型-可预约车位
     */
    public static final Integer BOOKABLE_PARKING = 2;

    /**
     * 状态-正常/开启
     */
    public static final int OPEN_STATUS = 1;

    /**
     * 状态-离线/关闭/未发生
     */
    public static final int CLOSE_STATUS = 0;

    /**
     * 车位状态-占用
     */
    public static final Integer PARKING_STATUS_OCCUPY = 1;

    /**
     * 车位状态-空闲
     */
    public static final Integer PARKING_STATUS_FREE = 0;

    /**
     * 车位状态-故障
     */
    public static final Integer PARKING_STATUS_FAULT = 3;

    /**
     * app标识
     */
    public static final String APP_SIGN = "app";

    /**
     * 小程序标识
     */
    public static final String APPLET_SIGN = "applet";

    /**
     * web标识
     */
    public static final String WEB_SIGN = "web";

    /**
     * 后端标识
     */
    public static final String WEB_SERVER_SIGN = "webserver";

    /*----------------停车场库ID信息---------------------*/
    /**
     * 场库id-鸿音广场
     */
    public static final long WAREHOUSE_ID_HY = 1;
    /**
     * 场库id-LIN舍公寓
     */
    public static final long WAREHOUSE_ID_NC = 2;
    /**
     * 场库id-泽辉苑
     */
    public static final long WAREHOUSE_ID_ZHY = 4;
    /**
     * 场库id-著雨苑
     */
    public static final long WAREHOUSE_ID_ZYY = 5;
    /**
     * 场库id-林彩苑
     */
    public static final long WAREHOUSE_ID_LCY = 6;
    /**
     * 场库id-芳菲苑
     */
    public static final long WAREHOUSE_ID_FFY = 7;
    /**
     * 场库id-云乐苑
     */
    public static final long WAREHOUSE_ID_YLY = 9;
    /**
     * 场库id-熙岚苑
     */
    public static final long WAREHOUSE_ID_XLY = 10;
    /**
     * 场库id-星凝苑
     */
    public static final long WAREHOUSE_ID_XNY = 11;
    /**
     * 场库id-月微苑
     */
    public static final long WAREHOUSE_ID_YWY = 12;
    /**
     * 场库id-朝露苑
     */
    public static final long WAREHOUSE_ID_ZLY = 13;
    /**
     * 场库id-新元畅想苑
     */
    public static final long WAREHOUSE_ID_XYCXY = 14;
    /**
     * 场库id-新元理想苑
     */
    public static final long WAREHOUSE_ID_XYLXY = 15;
    /**
     * 场库id-广丰名都
     */
    public static final long WAREHOUSE_ID_GFMD = 16;
    /**
     * 场库id-春晓苑
     */
    public static final long WAREHOUSE_ID_CXY = 17;
    /**
     * 场库id-雨浥苑
     */
    public static final long WAREHOUSE_ID_YYY = 18;
    /**
     * 场库id-凌波苑
     */
    public static final long WAREHOUSE_ID_LBY = 19;
    /**
     * 场库id-海云苑
     */
    public static final long WAREHOUSE_ID_HYY = 20;
    /**
     * 场库id-新元盛璟苑
     */
    public static final long WAREHOUSE_ID_XYSJY = 21;
    /**
     * 场库id-华亭茗苑
     */
    public static final long WAREHOUSE_ID_HTMY = 22;
    /**
     * 场库id-汇丰名都
     */
    public static final long WAREHOUSE_ID_HFMD = 23;
    /**
     * 场库id-熙景苑
     */
    public static final long WAREHOUSE_ID_XJY = 25;
    /**
     * 场库id-新雨苑
     */
    public static final long WAREHOUSE_ID_XYY = 26;
    /**
     * 场库id-清涛苑
     */
    public static final long WAREHOUSE_ID_QTY = 27;
    /**
     * 场库id-露华苑
     */
    public static final long WAREHOUSE_ID_LHY = 28;

    /**
     * 会员车位状态-占用
     */
    public static final String VIP_PARKING_STATUS_OCCUPY = "1";
    /**
     * 会员车位状态-空闲
     */
    public static final String VIP_PARKING_STATUS_FREE = "0";
}
