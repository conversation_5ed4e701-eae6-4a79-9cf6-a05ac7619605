package com.lgjy.wx.controller;

import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxAdvertConfig;
import com.lgjy.wx.service.WxAdvertConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/advertConfig")
public class WxAdvertConfigController extends BaseController {

    @Resource
    private WxAdvertConfigService wxAdvertConfigService;

    /**
     * 查询轮播图配置信息列表
     */
    @GetMapping("/list")
    public AjaxResult list()
    {
        List<WxAdvertConfig> list = wxAdvertConfigService.selectWxAdvertConfigList();
        return success(list);
    }
}
