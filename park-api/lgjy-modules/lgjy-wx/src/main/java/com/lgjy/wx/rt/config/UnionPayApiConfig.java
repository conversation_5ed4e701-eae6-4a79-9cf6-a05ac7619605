package com.lgjy.wx.rt.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lgjy.common.core.utils.ClientHttpResponseWrapper;
import com.lgjy.wx.rt.RestTemplateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;

@Configuration
public class UnionPayApiConfig {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Bean
    public RestTemplate unionPayClientRestTemplate(RestTemplateBuilder builder, ObjectMapper objectMapper) {
        return builder
                // 配置根路径
                //.rootUri()
                // 配置拦截器
                .additionalInterceptors(
                        (request, body, execution) -> {
                            // 设置为支持接收任何格式的响应
                            request.getHeaders().set("Accept", "*/*");
                            // 根据请求体字符集将请求体转换为字符串
                            String requestBody = new String(body, RestTemplateUtils.getRequestBodyCharset(request));
                            // 打印请求
                            RestTemplateUtils.printRequest(logger, objectMapper, request, requestBody);
                            // 执行请求，获取响应
                            try (ClientHttpResponse response = execution.execute(request, body)) {
                                // 获取响应体字符集
                                Charset responseBodyCharset = RestTemplateUtils.getResponseBodyCharset(response);
                                // 根据响应体字符集将响应体转换为字符串
                                String responseBody = new String(RestTemplateUtils.getResponseBody(response), responseBodyCharset);
                                // 打印响应
                                RestTemplateUtils.printResponse(logger, objectMapper, request, response, responseBody);
                                // 保存接口调用日志
                                // RestTemplateUtils.saveCallLog(objectMapper, SysHttpInterfaceProvider.OA, request, requestBody, response, responseBody);
                                // 设置响应内容类型为JSON，字符集不变
                                response.getHeaders().set("Content-Type", "application/json;charset=" + responseBodyCharset);
                                // 重新包装响应
                                return new ClientHttpResponseWrapper(response, responseBody.getBytes(responseBodyCharset));
                            }
                        }
                )
                .build();
    }
}
