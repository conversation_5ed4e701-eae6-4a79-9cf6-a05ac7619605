package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.WxSpecialUserCar;
import com.lgjy.wx.domain.WxUserPackage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WxSpecialUserCarMapper {
    
    /**
     * 根据特殊用户ID查询车辆列表
     * @param specialUserId 特殊用户ID
     * @return 车辆列表
     */
    List<WxSpecialUserCar> selectCarsBySpecialUserId(@Param("specialUserId") Long specialUserId);
    
    /**
     * 根据手机号查询车辆列表
     * @param phoneNumber 手机号
     * @return 车辆列表
     */
    List<WxSpecialUserCar> selectCarsByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    /**
     * 购买Vip或者大客户套餐后是否需要添加特殊用户车辆
     * @param wxUserPackage
     * @return
     */
    WxSpecialUserCar selectCar(WxUserPackage wxUserPackage);

    /**
     * 插入特殊会员
     * @param wxSpecialUserCar
     * @return
     */
    int insertWxSpecialUserCar(WxSpecialUserCar wxSpecialUserCar);
}
