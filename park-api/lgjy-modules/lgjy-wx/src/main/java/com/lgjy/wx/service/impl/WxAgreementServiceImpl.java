package com.lgjy.wx.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.wx.mapper.WxAgreementMapper;
import com.lgjy.wx.domain.WxAgreement;
import com.lgjy.wx.service.WxAgreementService;

/**
 * 系统协议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@Service
public class WxAgreementServiceImpl implements WxAgreementService 
{
    @Autowired
    private WxAgreementMapper wxAgreementMapper;

    /**
     * 根据协议类型查询系统协议
     * 
     * @param agreementType 协议类型
     * @return 系统协议
     */
    @Override
    public WxAgreement selectAgreementByType(Integer agreementType)
    {
        return wxAgreementMapper.selectAgreementByType(agreementType);
    }
}
