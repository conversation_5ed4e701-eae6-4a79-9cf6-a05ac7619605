package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class WxUserCar {
    // 车辆id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    // 车牌号
    private String plateNo;

    // 品牌
    private String carBrand;

    // 车型(微型，轿车，SUV，其他）
    private String carType;

    // 是否是默认车辆（0否 1是）
    private Integer isDefault;

    // 用户id
    private Long userId;

    // 能源类型(1燃油、2纯电、3混动)
    private Integer energyType;

    // 删除标志（0未删除 1已删除）
    private Integer deleteFlag;
}
