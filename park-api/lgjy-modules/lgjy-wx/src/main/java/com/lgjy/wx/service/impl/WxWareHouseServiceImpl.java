package com.lgjy.wx.service.impl;

import com.lgjy.wx.domain.WxWareHouse;
import com.lgjy.wx.mapper.WxWareHouseMapper;
import com.lgjy.wx.service.WxWareHouseService;
import com.lgjy.wx.utils.AddressUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WxWareHouseServiceImpl implements WxWareHouseService {

    @Resource
    private WxWareHouseMapper wxWareHouseMapper;
    /**
     * 查询停车场库信息列表
     */
    @Override
    public List<WxWareHouse> selectWxWarehouseList(WxWareHouse wxWarehouse) {
        List<WxWareHouse> list =wxWareHouseMapper.selectWxWarehouseList(wxWarehouse);
//        list.forEach(warehouse -> {
//            String fullAddress = AddressUtils.buildFullAddress(
//                    warehouse.getProvinceName(),
//                    warehouse.getCityName(),
//                    warehouse.getAreaName(),
//                    warehouse.getAddress()
//            );
//            warehouse.setAddressDetail(fullAddress);
//        });
        return list;
    }

    @Override
    public WxWareHouse getWarehouseById(Long id) {
        return wxWareHouseMapper.getWarehouseById(id);
    }
}
