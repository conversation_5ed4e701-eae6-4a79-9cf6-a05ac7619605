package com.lgjy.wx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class WxVipCarInfo {
    
    // 车牌号（必有）
    private String plateNo;
    
    // 场库ID（会员有效时才有）
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long warehouseId;
    
    // 场库名称（会员有效时才有）
    private String warehouseName;
    
    // 会员开始时间（会员有效时才有）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginVipTime;
    
    // 会员结束时间（会员有效时才有）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endVipTime;
    
    /**
     * 构造方法 - 会员过期或无会员记录时只返回车牌号
     */
    public WxVipCarInfo(String plateNo) {
        this.plateNo = plateNo;
    }
    
    /**
     * 构造方法 - 会员有效时返回完整信息
     */
    public WxVipCarInfo(String plateNo, Long warehouseId, String warehouseName, 
                        Date beginVipTime, Date endVipTime) {
        this.plateNo = plateNo;
        this.warehouseId = warehouseId;
        this.warehouseName = warehouseName;
        this.beginVipTime = beginVipTime;
        this.endVipTime = endVipTime;
    }
    
    /**
     * 无参构造方法（JSON序列化需要）
     */
    public WxVipCarInfo() {
    }
}
