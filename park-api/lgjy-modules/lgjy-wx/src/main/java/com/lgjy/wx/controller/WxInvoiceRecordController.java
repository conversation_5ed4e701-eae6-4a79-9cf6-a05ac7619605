package com.lgjy.wx.controller;

import com.alibaba.fastjson.JSONObject;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.wx.domain.WxInvoiceRecord;
import com.lgjy.wx.service.WxInvoiceRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/invoice/record")
public class WxInvoiceRecordController extends BaseController {

    @Resource
    private WxInvoiceRecordService wxInvoiceRecordService;

    /**
     * 查询开票记录列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody WxInvoiceRecord wxInvoiceRecord) {
        List<WxInvoiceRecord> list = wxInvoiceRecordService.selectWxInvoiceRecordList(wxInvoiceRecord);
        return success(list);
    }

    /**
     * 获取开票记录详细信息
     */
    @GetMapping( "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wxInvoiceRecordService.selectWxInvoiceRecordById(id));
    }

    /**
     * 新增开票记录
     */
    @PostMapping("/insert")
    public AjaxResult issueInvoice(@RequestBody WxInvoiceRecord wxInvoiceRecord) {
        wxInvoiceRecordService.issueInvoice(wxInvoiceRecord);
        return success("提交开票请求后，发票将会在1-3个工作日内发出，请您耐心等候");
    }

    /**
     * 开票回调
     */
    @PostMapping("/invoiceCallback")
    public Map<String, Object> invoiceCallback(HttpServletRequest request, HttpServletResponse response) {
        StringBuilder sb = new StringBuilder();
        try (ServletInputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        ) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            log.error("读取数据流异常", e);
        }
        log.info("开票回调start---------------body:{}", sb.toString());
        Map<String, Object> result = new HashMap<>();
        try {
            if (wxInvoiceRecordService.invoiceCallback(JSONObject.parseObject(sb.toString()))) {
                result.put("resultCode", "SUCCESS");
                result.put("resultMsg", "");
                log.info("开票回调成功");
                return result;
            } else {
                result.put("resultCode", "FAIL");
                result.put("resultMsg", "开票回调失败");
                response.setStatus(500);
                log.info("开票回调失败");
                return result;
            }
        } catch (Exception e) {
            log.error("支付回调失败", e);
            result.put("resultCode", "FAIL");
            result.put("resultMsg", "开票回调失败" + e.getMessage());
            response.setStatus(500);
            return result;
        }
    }

    /**
     * 查询银联开票状态
     */
    @GetMapping("/invoiceStatus")
    public AjaxResult invoiceStatus(WxInvoiceRecord wxInvoiceRecord) {
        return success(wxInvoiceRecordService.invoiceStatus(wxInvoiceRecord));
    }

    /**
     * 发票抬头查询
     */
    @GetMapping(value = "/queryInvoiceTitle")
    public AjaxResult queryInvoiceTitle(WxInvoiceRecord wxInvoiceRecord) {
        return success(wxInvoiceRecordService.queryInvoiceTitle(wxInvoiceRecord));
    }

    /**
     * 发票重开
     */
    @PostMapping(value = "/resumeInvoice")
    public AjaxResult resumeInvoice(@RequestBody WxInvoiceRecord wxInvoiceRecord){
        return wxInvoiceRecordService.resumeInvoice(wxInvoiceRecord);
    }

    /**
     * 发送邮箱
     */
    @PostMapping(value = "/send")
    public AjaxResult invoiceSend(@RequestBody WxInvoiceRecord wxInvoiceRecord) {
        wxInvoiceRecordService.invoiceSend(wxInvoiceRecord);
        return success();
    }

}
