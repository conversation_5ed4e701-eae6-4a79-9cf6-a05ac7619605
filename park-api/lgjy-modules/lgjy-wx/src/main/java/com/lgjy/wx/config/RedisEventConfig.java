package com.lgjy.wx.config;

import com.lgjy.wx.listener.KeyExpirationEventMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * Redis事件监听配置
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Configuration
@Slf4j
public class RedisEventConfig {
    
    /**
     * 配置Redis键过期事件监听
     */
    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            RedisConnectionFactory connectionFactory,
            ApplicationEventPublisher applicationEventPublisher) {
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // 创建键过期事件监听器
        KeyExpirationEventMessageListener listener = new KeyExpirationEventMessageListener(applicationEventPublisher);
        
        // 监听所有数据库的键过期事件
        // __keyevent@*__:expired 表示监听所有数据库的过期事件
        container.addMessageListener(listener, new PatternTopic("__keyevent@*__:expired"));
        
        log.info("Redis键过期事件监听器配置完成");
        
        return container;
    }
}
