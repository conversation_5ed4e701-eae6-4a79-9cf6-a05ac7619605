package com.lgjy.wx.service.impl;

import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.wx.domain.WxUserCar;
import com.lgjy.wx.domain.WxUserPackage;
import com.lgjy.wx.mapper.WxPackageMapper;
import com.lgjy.wx.mapper.WxUserCarMapper;
import com.lgjy.wx.service.WxUserCarService;
import io.jsonwebtoken.lang.Collections;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WxUserCarServiceImpl implements WxUserCarService {

    @Resource
    private WxUserCarMapper wxUserCarMapper;

    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Resource
    private WxPackageMapper wxPackageMapper;

    @Override
    public List<WxUserCar> selectWxUserCarList() {
        Long userId = SecurityUtils.getUserId();
        if(userId==null){
            throw new ServiceException("用户不存在");
        }
        WxUserCar wxUserCar = new WxUserCar();
        wxUserCar.setUserId(userId);
        return wxUserCarMapper.selectWxUserCarList(wxUserCar);
    }

    @Override
    public Integer insertWxUserCar(WxUserCar wxUserCar) {
        Long userId = SecurityUtils.getUserId();
        if(userId==null){
            throw new ServiceException("用户不存在");
        }
        WxUserCar wxUserCarOld=wxUserCarMapper.selectWxUserCarByPlateNo(wxUserCar);
        if(!ObjectUtils.isEmpty(wxUserCarOld) && wxUserCarOld.getUserId().equals(userId)){
            throw new ServiceException("您已添加过该车辆");
        }
        if(!ObjectUtils.isEmpty(wxUserCarOld) && !wxUserCarOld.getUserId().equals(userId)){
            throw new ServiceException("该车辆已被其他用户绑定");
        }
        wxUserCar.setId(snowflakeIdGenerator.nextId());
        wxUserCar.setUserId(SecurityUtils.getUserId());
        wxUserCar.setDeleteFlag(0);
        wxUserCar.setIsDefault(0);
        // 如果新增的是第一辆车，则设为默认车
        List<WxUserCar> wxUserCars = wxUserCarMapper.selectWxUserCarList(wxUserCar);
        if(Collections.isEmpty(wxUserCars)){
            wxUserCar.setIsDefault(1);
        }
        return wxUserCarMapper.insertWxUserCar(wxUserCar);
    }

    @Override
    public Integer updateWxUserCar(WxUserCar wxUserCar) {
        Long userId = SecurityUtils.getUserId();
        WxUserCar wxUserCarOld = wxUserCarMapper.selectWxUserCarById(wxUserCar.getId());
        // 如果修改了车牌号，则判断是否已存在
        if(!ObjectUtils.isEmpty(wxUserCarOld) && wxUserCar.getPlateNo()!=null &&
                !wxUserCar.getPlateNo().equals(wxUserCarOld.getPlateNo())){
            WxUserCar wxUserCarOldo=wxUserCarMapper.selectWxUserCarByPlateNo(wxUserCar);
            if(!ObjectUtils.isEmpty(wxUserCarOldo) && wxUserCarOldo.getUserId().equals(userId)){
                throw new ServiceException("您已添加过该车辆");
            }
            if(!ObjectUtils.isEmpty(wxUserCarOldo) && !wxUserCarOldo.getUserId().equals(userId)){
                throw new ServiceException("该车辆已被其他用户绑定");
            }
        }
        // 如果修改的是默认车，则将旧默认车设为非默认车
        if(wxUserCar.getIsDefault()!=null&&wxUserCar.getIsDefault()==1){
            WxUserCar old =wxUserCarMapper.selectWxUserDefaultCar(userId);
            old.setIsDefault(0);
            wxUserCarMapper.updateWxUserCar(old);
        }
        return wxUserCarMapper.updateWxUserCar(wxUserCar);
    }

    @Override
    public Integer deleteWxUserCar(Long id) {
        Long userId = SecurityUtils.getUserId();
        if(userId==null){
            throw new ServiceException("用户不存在");
        }
        WxUserCar wxUserCar = wxUserCarMapper.selectWxUserCarById(id);
        if(!ObjectUtils.isEmpty(wxUserCar)){
            // 删除前判断会员是否未过期
            WxUserPackage userPackage =wxPackageMapper.selectWxUserPackageByPlateNo(wxUserCar.getPlateNo(),userId);
            if(!ObjectUtils.isEmpty(userPackage)){
                throw new ServiceException("该车有会员未过期，不可删除");
            }
        }
        // 如果删除的是默认车，则将下一辆车设为默认车
        if(wxUserCar.getIsDefault()==1){
            // 先排序默认车，在按照创建时间排序，
            List<WxUserCar> wxUserCars = wxUserCarMapper.selectWxUserCarList(wxUserCar);
            if(!Collections.isEmpty(wxUserCars)&&wxUserCars.size()>1){
                WxUserCar wxUserCarDefault = wxUserCars.get(1);
                wxUserCarDefault.setIsDefault(1);
                wxUserCarMapper.updateWxUserCar(wxUserCarDefault);
            }
        }
        return wxUserCarMapper.deleteWxUserCar(id);
    }

    @Override
    public WxUserCar selectWxUserCarById(Long id) {
        return wxUserCarMapper.selectWxUserCarById(id);
    }
}
