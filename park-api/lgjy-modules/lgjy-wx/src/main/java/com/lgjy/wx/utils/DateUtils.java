package com.lgjy.wx.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

public class DateUtils extends org.apache.commons.lang.time.DateUtils{
    public static String DATE_FORMATTER = "yyyy-MM-dd";
    public final static String DATE_HOUR_FORMATTER = "yyyy-MM-dd HH";
    public final static String DATE_HOUR_MINUTE_FORMATTER = "yyyy-MM-dd HH:mm";
    public final static String DATE_HOUR_MINUTE_SECOND_FORMATTER = "yyyy-MM-dd HH:mm:ss";
    public final static String DATE_HOUR_MINUTE_SECOND_MILLISECOND_FORMATTER = "yyyyMMddHHmmssSSS";
    public final static String DATE_HOUR_MINUTE_SECOND_UNION_FORMATTER = "yyyyMMddHHmmss";
    public final static String DATE_UNION_FORMATTER = "yyyyMMdd";
    public final static String DATE_TIME_UNION_FORMATTER = "HHmmss";
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMATTER);
    private static final DateTimeFormatter DATE_TIME_HOUR_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_HOUR_MINUTE_SECOND_FORMATTER);
    public final static String CHINA_YEAR_MONTH_DAY = "yyyy年MM月dd日";
    public final static String SIMPLE_MONTH_DAY = "M月dd日";


    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 根据年份，天数获取日期
     **/
    public static String getLocalDate(int year, int dayOfYear) {
        return LocalDate.ofYearDay(year, dayOfYear).format(DATE_TIME_FORMATTER);
    }

    /**
     * 当前时间之前的时间与当前时间相差多少秒
     *
     * @param startDate 当前时间之前的时间
     */
    public static int intervalSecond(Date startDate) {
        long nowDate = System.currentTimeMillis();
        long startDateTime = startDate.getTime();
        return (int) ((nowDate - startDateTime) / 1000);
    }

    /**
     * 计算时间间隔，单位：毫秒
     **/
    public static long intervalSecond(Date startDate, Date endDate) {
        long startDateTime = startDate.getTime();
        long endDateTime = endDate.getTime();
        return endDateTime - startDateTime;
    }
    /**
     * 计算两个时间的分钟差（向上取整）
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 分钟差（正整数）
     * @throws IllegalArgumentException 如果时间无效
     */
    public static int intervalMinutes(Date startDate, Date endDate) {
        // 1. 校验参数
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("时间参数不能为空");
        }

        // 2. 计算毫秒差
        long diffMillis = endDate.getTime() - startDate.getTime();

        // 3. 处理时间倒流（结束时间早于开始时间）
        if (diffMillis < 0) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }

        // 4. 转换为分钟并向上取整
        return (int) Math.ceil((double) diffMillis / (60 * 1000));
    }

    /**
     * 日期格式转换
     */
    public static Date parse(String time, String pattern) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.parse(time.trim());
    }

    /**
     * 日期格式转换
     */
    public static String format(Date time, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(time);
    }

    /**
     * 比较当前时间是否介于两个日期
     *
     * @param startStr
     * @param endStr
     * @return
     */
    public static boolean isBetween(String startStr, String endStr) throws ParseException {
        Date now = new Date();
        Date start = parse(startStr, DATE_HOUR_MINUTE_SECOND_FORMATTER);
        Date end = parse(endStr, DATE_HOUR_MINUTE_SECOND_FORMATTER);
        if (now.getTime() > start.getTime() && now.getTime() < end.getTime()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * LocalDateTime转date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * date转LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 获取指定日期开始时间
     */
    public static Date minDate(Date date) {
        LocalDateTime startOfTheDay = LocalDateTime.of(dateToLocalDateTime(date).toLocalDate(), LocalTime.MIN);
        return localDateTimeToDate(startOfTheDay);
    }

    /**
     * 获取指定日期结束时间
     */
    public static Date maxDate(Date date) {
        LocalDateTime endOfTheDay = LocalDateTime.of(dateToLocalDateTime(date).toLocalDate(), LocalTime.MAX);
        return addMilliseconds(localDateTimeToDate(endOfTheDay), -999);
    }

    /**
     * 指定时间点加指定时间
     * time: ms
     */
    public static Date datePlusTime(Date date, Integer time) {
        long longDate = date.getTime();
        return new Date(longDate + time);
    }

    /**
     * 计算时间间隔，单位：天
     **/
    public static long betweenForDay(Date startDate, Date endDate) {
        LocalDate start = dateToLocalDateTime(startDate).toLocalDate();
        LocalDate end = dateToLocalDateTime(endDate).toLocalDate();
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 计算时间间隔，格式：xx小时xx分钟xx秒
     */
    public static String timeDifference(Date startDate, Date endDate) {
        LocalDateTime start = dateToLocalDateTime(startDate);
        LocalDateTime end = dateToLocalDateTime(endDate);
        Duration duration = Duration.between(start, end);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long second = duration.getSeconds() % 60;
        return (hours > 9 ? hours : "0" + hours) + "小时" + (minutes > 9 ? minutes : "0" + minutes) + "分钟" + (second > 9 ? second : "0" + second) + "秒";
    }
}
