package com.lgjy.wx.rt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "union-pay-api")
public class UnionPayApiProperties {
    /**
     * 平台标识
     */
    private String sign;

    /**
     * 服务器地址
     */
    private String url;

    /**
     * 银联商务appId
     */
    private String appId;

    /**
     * 银联商务appKey
     */
    private String appKey;

    /**
     * 支付超时时间
     */
    private Integer timeout;

    /**
     * 停车订单-异步微信订单通知地址，外网可访问的url，不能携带参数。公网域名必须为https
     */
    private String notifyUrlOrder;

    /**
     * 会员购买订单-异步微信订单通知地址，外网可访问的url，不能携带参数。公网域名必须为https
     */
    private String notifyUrlVip;

    /**
     * 临停车支付宝H5 回调
     */
    private String notifyUrlOrderAlipay;

    /**
     * 发票基础按钮
     */
    private String invoiceUrl;

    /**
     * 消息来源
     */
    private String invoiceMsgSrc;

    /**
     * 签名秘钥
     */
    private String invoiceKey;

    /**
     * 发票回调
     */
    private String notifyUrlOrderInvoice;



}
