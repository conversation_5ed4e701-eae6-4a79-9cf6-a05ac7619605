package com.lgjy.wx.listener;

import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.WxUserPackageRecord;
import com.lgjy.wx.event.RedisKeyExpiredEvent;
import com.lgjy.wx.mapper.WxPackageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 订单超时事件监听器
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Component
@Slf4j
public class OrderTimeoutEventListener {
    
    @Autowired
    private WxPackageMapper wxPackageMapper;
    
    private static final String ORDER_TIMEOUT_KEY_PREFIX = "order:timeout:";
    
    /**
     * 监听Redis键过期事件
     */
    @EventListener
    public void handleKeyExpiredEvent(RedisKeyExpiredEvent event) {
        String expiredKey = event.getExpiredKey();
        
        // 只处理订单超时键
        if (expiredKey != null && expiredKey.startsWith(ORDER_TIMEOUT_KEY_PREFIX)) {
            String tradeId = expiredKey.substring(ORDER_TIMEOUT_KEY_PREFIX.length());
            handleOrderTimeout(tradeId);
        }
    }
    
    /**
     * 处理订单超时逻辑
     */
    private void handleOrderTimeout(String tradeId) {
        try {
            log.info("开始处理订单超时，tradeId: {}", tradeId);
            
            // 1. 查询订单信息
            WxUserPackageRecord order = wxPackageMapper.selectWxUserPackageRecordByTradeId(tradeId);
            if (order == null) {
                log.warn("订单不存在，tradeId: {}", tradeId);
                return;
            }
            
            // 2. 检查订单状态
            if (!Objects.equals(order.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PROGRESS)) {
                log.info("订单状态已变更，无需处理超时，tradeId: {}, payStatus: {}", 
                        tradeId, order.getPayStatus());
                return;
            }
            
            // 3. 更新订单状态为已取消
            order.setPayStatus(PayConstants.ORDER_PAY_STATUS_CANCEL);
            order.setUpdateTime(new Date());
            int updateResult = wxPackageMapper.updateWxUserPackageRecord(order);
            
            if (updateResult > 0) {
                log.info("订单超时处理成功，tradeId: {}, 状态从待支付(1)更新为已取消，允许重新支付", tradeId);
                
                // 4. 记录超时事件
                recordTimeoutEvent(tradeId, order);
            } else {
                log.error("订单超时处理失败，数据库更新失败，tradeId: {}", tradeId);
            }
            
        } catch (Exception e) {
            log.error("处理订单超时异常，tradeId: {}", tradeId, e);
        }
    }
    
    /**
     * 记录超时事件
     */
    private void recordTimeoutEvent(String tradeId, WxUserPackageRecord order) {
        log.info("订单超时事件记录：tradeId={}, userId={}, plateNo={}, amount={}, warehouseId={}", 
                tradeId, order.getUserId(), order.getPlateNo(), order.getActualPayment(), order.getWarehouseId());
        
        // 这里可以扩展：
        // 1. 记录到专门的超时日志表
        // 2. 发送通知给运营人员
        // 3. 统计超时订单数据
    }
}
