package com.lgjy.wx.mapper;

import com.lgjy.wx.domain.WxInvoiceRecord;

import java.util.List;

public interface WxInvoiceRecordMapper {
    WxInvoiceRecord selectSicvInvoiceRecordById(Long invoiceId);

    void updateWxInvoiceRecord(WxInvoiceRecord wxInvoiceRecord);

    void insertWxInvoiceRecord(WxInvoiceRecord wxInvoiceRecord);

    List<WxInvoiceRecord> selectWxInvoiceRecordList(WxInvoiceRecord wxInvoiceRecord);

    WxInvoiceRecord selectWxInvoiceRecordById(Long id);

    WxInvoiceRecord queryInvoiceRecordByTradeId(String tradeId);

    WxInvoiceRecord queryInvoiceRecordByOldTradeId(String oldTradeId);
}
