package com.lgjy.wx.service.impl;

import com.lgjy.common.redis.service.RedisService;
import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.OrderTimeoutInfo;
import com.lgjy.wx.domain.WxUserPackageRecord;
import com.lgjy.wx.mapper.WxPackageMapper;
import com.lgjy.wx.service.OrderTimeoutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 订单超时服务实现类
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
@Slf4j
public class OrderTimeoutServiceImpl implements OrderTimeoutService {
    
    @Autowired
    private RedisService redisService;
    
    @Autowired
    private WxPackageMapper wxPackageMapper;
    
    private static final String ORDER_TIMEOUT_KEY_PREFIX = "order:timeout:";
    private static final int TIMEOUT_MINUTES = 15;
    
    @Override
    public void setOrderTimeoutKey(WxUserPackageRecord record) {
        try {
            String tradeId = record.getTradeId();

            // 设置超时控制键（15分钟后过期）
            String timeoutKey = ORDER_TIMEOUT_KEY_PREFIX + tradeId;
            redisService.setCacheObject(timeoutKey, tradeId, TIMEOUT_MINUTES, TimeUnit.MINUTES);

            log.info("设置订单超时键成功，tradeId: {}, 超时时间: {}分钟", tradeId, TIMEOUT_MINUTES);

        } catch (Exception e) {
            log.error("设置订单超时键失败，tradeId: {}", record.getTradeId(), e);
        }
    }
    
    @Override
    public void clearOrderTimeoutKey(String tradeId) {
        try {
            String timeoutKey = ORDER_TIMEOUT_KEY_PREFIX + tradeId;
            
            // 删除超时控制键
            redisService.deleteObject(timeoutKey);
            
            log.info("清理订单超时键成功，tradeId: {}", tradeId);
            
        } catch (Exception e) {
            log.error("清理订单超时键失败，tradeId: {}", tradeId, e);
        }
    }
    
    @Override
    public boolean isOrderTimeout(String tradeId) {
        try {
            String timeoutKey = ORDER_TIMEOUT_KEY_PREFIX + tradeId;
            return !redisService.hasKey(timeoutKey);
        } catch (Exception e) {
            log.error("检查订单超时状态失败，tradeId: {}", tradeId, e);
            return false;
        }
    }
    
    @Override
    public OrderTimeoutInfo getOrderTimeoutInfo(String tradeId) {
        // 简化实现：直接从数据库查询，避免数据不一致
        try {
            WxUserPackageRecord record = wxPackageMapper.selectWxUserPackageRecordByTradeId(tradeId);
            return record != null ? buildOrderTimeoutInfo(record) : null;
        } catch (Exception e) {
            log.error("获取订单超时信息失败，tradeId: {}", tradeId, e);
            return null;
        }
    }
    
    @Override
    public boolean handleOrderTimeoutManually(String tradeId) {
        try {
            log.info("手动处理订单超时，tradeId: {}", tradeId);
            
            // 1. 查询订单信息
            WxUserPackageRecord order = wxPackageMapper.selectWxUserPackageRecordByTradeId(tradeId);
            if (order == null) {
                log.warn("订单不存在，tradeId: {}", tradeId);
                return false;
            }
            
            // 2. 检查订单状态
            if (!Objects.equals(order.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PROGRESS)) {
                log.info("订单状态已变更，无需处理超时，tradeId: {}, payStatus: {}", 
                        tradeId, order.getPayStatus());
                return false;
            }
            
            // 3. 更新订单状态为支付中（允许重新支付）
            order.setPayStatus(PayConstants.ORDER_PAY_STATUS_CANCEL);
            order.setUpdateTime(new Date());
            int updateResult = wxPackageMapper.updateWxUserPackageRecord(order);
            
            if (updateResult > 0) {
                log.info("手动处理订单超时成功，tradeId: {}", tradeId);
                return true;
            } else {
                log.error("手动处理订单超时失败，数据库更新失败，tradeId: {}", tradeId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("手动处理订单超时异常，tradeId: {}", tradeId, e);
            return false;
        }
    }
    
    /**
     * 构建订单超时信息
     */
    private OrderTimeoutInfo buildOrderTimeoutInfo(WxUserPackageRecord record) {
        OrderTimeoutInfo info = new OrderTimeoutInfo();
        info.setTradeId(record.getTradeId());
        info.setUserId(record.getUserId());
        info.setWarehouseId(record.getWarehouseId());
        info.setPlateNo(record.getPlateNo());
        info.setPackageId(record.getPackageId());
        info.setPackageName(record.getPackageName());
        info.setActualPayment(record.getActualPayment());
        info.setCreateTime(new Date());
        info.setPayStatus(record.getPayStatus());
        info.setUserPhone(record.getPhoneNumber());
        info.setVipType(record.getVipType());
        info.setIsRenewal(record.getIsRenewal());
        info.setOpenid(record.getOpenid());
        return info;
    }
}
