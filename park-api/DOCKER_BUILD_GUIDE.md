# Docker构建指南

## 📋 项目配置概览

### 数据库配置
- **MySQL版本**: 8.0.35
- **MySQL驱动**: mysql-connector-j 8.0.35
- **连接池**: Druid 1.2.25
- **JDK版本**: 1.8
- **Spring Boot版本**: 2.7.18

### Docker配置
- **基础镜像**: eclipse-temurin:8-jdk
- **Docker插件版本**: 0.44.0
- **镜像前缀**: park

## 🚀 Maven打包和Docker构建流程

### 1. 开发环境构建

```bash
# 清理并编译项目
mvn clean compile

# 运行测试
mvn test

# 打包jar文件（跳过Docker构建）
mvn clean package
```

### 2. Docker镜像构建

#### 方式一：使用Docker profile
```bash
# 构建jar文件并创建Docker镜像
mvn clean package -Pdocker

# 或者分步执行
mvn clean package
mvn docker:build -Pdocker
```

#### 方式二：构建并推送到仓库
```bash
# 构建并推送Docker镜像
mvn clean package -Pprod
```

### 3. 单独构建特定微服务

```bash
# 构建系统服务
cd lgjy-modules/lgjy-system
mvn clean package docker:build -Pdocker

# 构建网关服务
cd lgjy-gateway
mvn clean package docker:build -Pdocker

# 构建文件服务
cd lgjy-modules/lgjy-file
mvn clean package docker:build -Pdocker

# 构建微信服务
cd lgjy-modules/lgjy-wx
mvn clean package docker:build -Pdocker

# 构建道闸服务
cd lgjy-modules/lgjy-gate
mvn clean package docker:build -Pdocker

# 构建认证服务
cd lgjy-auth
mvn clean package docker:build -Pdocker
```

## 🐳 Docker Compose部署

### 1. 启动所有服务
```bash
# 启动所有服务（后台运行）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service-name]
```

### 2. 重新构建并部署
```bash
# 重新构建镜像并启动
docker-compose up -d --build

# 停止所有服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

## 📦 生成的Docker镜像

构建完成后，将生成以下Docker镜像：

- `park/lgjy-auth:3.6.6` - 认证服务
- `park/lgjy-gateway:3.6.6` - 网关服务
- `park/lgjy-modules-system:3.6.6` - 系统管理服务
- `park/lgjy-modules-file:3.6.6` - 文件服务
- `park/lgjy-modules-wx:3.6.6` - 微信小程序服务
- `park/lgjy-modules-gate:3.6.6` - 道闸设备服务

## 🔧 Maven Profiles说明

### dev (默认)
- 跳过Docker构建
- 适用于本地开发

### docker
- 启用Docker构建
- 跳过镜像推送
- 适用于本地测试

### prod
- 启用Docker构建
- 启用镜像推送
- 适用于生产部署

## ⚠️ 注意事项

1. **数据库兼容性**: 确保MySQL 8.0.35与应用程序兼容
2. **内存配置**: 各服务默认JVM参数为 `-Xms256m -Xmx512m`
3. **端口配置**: 确保各服务端口不冲突
4. **网络配置**: 所有服务都在 `park-network` 网络中
5. **数据持久化**: MySQL和Redis数据通过Docker volumes持久化

## 🔍 故障排查

### 查看容器日志
```bash
# 查看特定服务日志
docker-compose logs -f mysql
docker-compose logs -f park-system
docker-compose logs -f park-gateway
```

### 检查容器状态
```bash
# 查看所有容器状态
docker ps -a

# 进入容器内部
docker exec -it park-mysql bash
docker exec -it park-system bash
```

### 重启服务
```bash
# 重启特定服务
docker-compose restart park-system
docker-compose restart mysql
```
