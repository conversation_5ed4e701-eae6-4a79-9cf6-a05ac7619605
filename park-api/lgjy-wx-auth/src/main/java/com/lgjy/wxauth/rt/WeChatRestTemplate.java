package com.lgjy.wxauth.rt;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;

@Component
public class WeChatRestTemplate {
    @Resource(name = "weChatClientRestTemplate")
    private final RestTemplate restTemplate;

    @Resource
    private final WxProperties wxProperties;

    public WeChatRestTemplate(@Qualifier("weChatClientRestTemplate") RestTemplate restTemplate,
                              WxProperties wxProperties) {
        this.restTemplate = restTemplate;
        this.wxProperties = wxProperties;
    }

    /**
     * 获取open_id
     */
    public JSONObject loginWx(String wxCode) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wxProperties.getUrl() + "/sns/jscode2session")
                .queryParam("appid", wxProperties.getAppid())
                .queryParam("secret", wxProperties.getSecret())
                .queryParam("js_code", wxCode)
                .queryParam("grant_type", wxProperties.getGrantType())
                .encode().build().toUri();
        return restTemplate.exchange(url, HttpMethod.GET, null, JSONObject.class).getBody();
    }

    public JSONObject getAccessKey() {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wxProperties.getAccessUrl())
                .queryParam("grant_type", "client_credential")
                .queryParam("appid", wxProperties.getAppid())
                .queryParam("secret", wxProperties.getSecret())
                .encode().build().toUri();
        return restTemplate.exchange(url, HttpMethod.GET, null, JSONObject.class).getBody();
    }

    /**
     * 获取open_id
     */
    public JSONObject getPhone(String accessToken, String phoneCode) {
        // 使用 UriComponentsBuilder 构建URL（更安全，自动处理编码）
        URI url = UriComponentsBuilder
                .fromHttpUrl(wxProperties.getPhoneUrl())
                .queryParam("access_token", accessToken)
                .encode() // 自动编码特殊字符
                .build()
                .toUri();

        // 构建请求体（JSON格式）
        JSONObject requestBody = new JSONObject();
        requestBody.put("code", phoneCode);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建 HttpEntity（包含请求头和请求体）
        HttpEntity<String> request = new HttpEntity<>(requestBody.toJSONString(), headers);

        // 发送请求并返回响应
        return restTemplate.exchange(url, HttpMethod.POST, request, JSONObject.class).getBody();
    }
}
