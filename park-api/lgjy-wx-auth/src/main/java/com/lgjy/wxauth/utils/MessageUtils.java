package com.lgjy.wxauth.utils;

import org.springframework.stereotype.Component;
import java.util.concurrent.ThreadLocalRandom;

@Component
public class MessageUtils {

    /**
     * 生成6位随机数字验证码
     */
    public String createCode() {
        StringBuilder code = new StringBuilder(6);
        for (int i = 0; i < 6; i++) {
            // 生成0-9的随机整数
            code.append(ThreadLocalRandom.current().nextInt(0, 10));
        }
        return code.toString();
    }
}
