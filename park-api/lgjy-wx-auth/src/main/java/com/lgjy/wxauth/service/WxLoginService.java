package com.lgjy.wxauth.service;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.constant.WxUserConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.api.RemoteWxUserService;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wxauth.form.LoginWxForm;
import com.lgjy.wxauth.rt.WeChatRestTemplate;
import com.lgjy.wxauth.utils.MessageUtils;
import com.lgjy.common.redis.service.RedisService;
import com.lgjy.system.api.model.LoginWxUser;
import com.lgjy.wxauth.form.LoginForm;
import com.lgjy.common.core.constant.CacheConstants;
import com.lgjy.wxauth.utils.MsgApiUtils;
import com.lgjy.wxauth.utils.MsgServerProperties;
import io.jsonwebtoken.lang.Assert;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class WxLoginService {

    @Resource
    private RedisService redisService;

    @Resource
    private MessageUtils messageUtils;

    @Resource
    private MsgApiUtils msgApiUtils;

    @Resource
    private MsgServerProperties msgServerProperties;

    @Resource
    private RemoteWxUserService remoteWxUserService;

    @Resource
    private WeChatRestTemplate weChatRestTemplate;

    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;


    /**
     * 发送验证码
     * @param phoneNumber
     * @return
     */
    public void sendCode(String phoneNumber) {
        String code = messageUtils.createCode();
        String verifyKey = CacheConstants.PHONE_CAPTCHA_CODE_KEY + phoneNumber;
        // 检查是否允许重新发送（防暴力请求）
        long remainingExpireSeconds = redisService.getExpire(verifyKey);
        // -2 表示键不存在，可以发送
        if (remainingExpireSeconds != -2) {
            if (remainingExpireSeconds > CacheConstants.PHONE_CAPTCHA_CODE_RESEND) {
                    throw new ServiceException("禁止频繁获取验证码");
                }
        }
        // 拼接短信模版
        String template = msgServerProperties.getTemplate();
        long expireMinutes = CacheConstants.PHONE_CAPTCHA_CODE_EXPIRATION;
        String content = MessageFormat.format(template, code, expireMinutes) + msgServerProperties.getSign();
        try{
            redisService.setCacheObject(verifyKey, code, CacheConstants.PHONE_CAPTCHA_CODE_EXPIRATION, TimeUnit.MINUTES);
            String result = msgApiUtils.msgSend(phoneNumber,content);
            Assert.isTrue(Objects.equals(result, "0"), "短信发送失败！" + result);
        }catch(Exception e) {
            redisService.deleteObject(verifyKey);
            throw new ServiceException("验证码发送异常！");
        }
    }

    /**
     * 手机号验证码登录
     * @param loginForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginWxUser login(LoginForm loginForm) {
        // 1. 参数校验
        String phoneNumber = loginForm.getPhoneNumber();
        String code = loginForm.getMsgCode();

        // 2. 验证码校验
        String cachedCode = redisService.getCacheObject(CacheConstants.PHONE_CAPTCHA_CODE_KEY + phoneNumber);
        if (cachedCode == null) {
            throw new ServiceException("验证码未发送或已过期");
        }
        if (!cachedCode.equals(code)) {
            throw new ServiceException("验证码错误");
        }
        redisService.deleteObject(CacheConstants.PHONE_CAPTCHA_CODE_KEY + phoneNumber);

        // 3. 获取微信openid
        JSONObject sessionInfo = weChatRestTemplate.loginWx(loginForm.getWxCode());
        String openid = sessionInfo.getString("openid");
        if (StringUtils.isBlank(openid)) {
            throw new ServiceException("微信授权失败");
        }

        // 4. 查询用户信息
        R<WxUser> userResult = remoteWxUserService.getUserInfo(phoneNumber, SecurityConstants.INNER);
        WxUser wxUser = userResult.getData();

        // 5. 处理用户注册/更新
        if (wxUser == null) {
            wxUser = new WxUser();
            wxUser.setId(snowflakeIdGenerator.nextId());
            wxUser.setUserName(phoneNumber);
            wxUser.setNickName(WxUserConstants.DEFAULT_NICK_NAME);
            wxUser.setPhoneNumber(phoneNumber);
            wxUser.setImg("");
            wxUser.setOpenId(openid);
            remoteWxUserService.insertUser(wxUser, SecurityConstants.INNER);
        } else if (!openid.equals(wxUser.getOpenId())) {
            wxUser.setOpenId(openid);
            remoteWxUserService.updateUserInfo(wxUser, SecurityConstants.INNER);
        }

        // 6. 返回登录结果
        LoginWxUser loginWxUser = new LoginWxUser();
        loginWxUser.setWxUser(wxUser);
        return loginWxUser;
    }

    /**
     * 微信一键登录
     * @param loginWxForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginWxUser loginWx(LoginWxForm loginWxForm) {
        // 1. 参数校验
        String wxCode = loginWxForm.getWxCode();
        String phoneCode = loginWxForm.getPhoneCode();
        if (StringUtils.isBlank(wxCode) || StringUtils.isBlank(phoneCode)) {
            throw new ServiceException("微信登录参数为空");
        }

        // 2. 获取微信openid和sessionKey
        JSONObject sessionInfo = weChatRestTemplate.loginWx(wxCode);
        String openid = sessionInfo.getString("openid");
        String sessionKey = sessionInfo.getString("session_key");
        if (StringUtils.isBlank(openid) || StringUtils.isBlank(sessionKey)) {
            throw new ServiceException("微信授权失败");
        }

        // 3. 获取用户手机号
        JSONObject accessTokenJson = weChatRestTemplate.getAccessKey();
        String accessToken = accessTokenJson.getString("access_token");
        JSONObject phoneInfo = weChatRestTemplate.getPhone(accessToken, phoneCode);
        String phoneNumber = phoneInfo.getJSONObject("phone_info").getString("phoneNumber");
        if (StringUtils.isBlank(phoneNumber)) {
            throw new ServiceException("获取手机号失败");
        }

        // 4. 查询用户信息
        R<WxUser> userResult = remoteWxUserService.getUserInfo(phoneNumber, SecurityConstants.INNER);
        WxUser wxUser = userResult.getData();

        // 5. 处理用户注册/更新
        if (wxUser == null) {
            wxUser = new WxUser();
            wxUser.setId(snowflakeIdGenerator.nextId());
            wxUser.setUserName(phoneNumber);
            wxUser.setNickName(WxUserConstants.DEFAULT_NICK_NAME);
            wxUser.setPhoneNumber(phoneNumber);
            wxUser.setImg("");
            wxUser.setOpenId(openid);
            remoteWxUserService.insertUser(wxUser, SecurityConstants.INNER);
        } else if (!openid.equals(wxUser.getOpenId())) {
            wxUser.setOpenId(openid);
            remoteWxUserService.updateUserInfo(wxUser, SecurityConstants.INNER);
        }

        // 6. 返回登录结果
        LoginWxUser loginWxUser = new LoginWxUser();
        loginWxUser.setWxUser(wxUser);
        return loginWxUser;
    }

    /**
     * 修改个人信息时候根据phoneCode获取手机号
     * @param phoneCode
     * @return
     */
    public String getPhoneNumberByCode(String phoneCode) {
        // 获取access_token
        JSONObject accessTokenJson = weChatRestTemplate.getAccessKey();
        String accessToken = accessTokenJson.getString("access_token");
        JSONObject phoneInfo = weChatRestTemplate.getPhone(accessToken, phoneCode);
        // 获取手机号
        String phoneNumber = phoneInfo.getJSONObject("phone_info").getString("phoneNumber");
        if(StringUtils.isBlank(phoneNumber)){
            throw new ServiceException("手机号获取失败！");
        }
        return phoneNumber;
    }

    /**
     * 更新用户信息
     * @param wxUser
     * @return
     */
    public LoginWxUser updateWxUser(WxUser wxUser) {
        Long userId = SecurityUtils.getUserId();
        // 用户修改后的手机号是否还是原来的手机号
        R<WxUser> newUser=remoteWxUserService.getUserInfo(wxUser.getPhoneNumber(), SecurityConstants.INNER);
        if(newUser.getData()!=null && !Objects.equals(newUser.getData().getId(), userId)){
            throw new ServiceException("您修改的手机号已经绑定其他用户");
        }
        wxUser.setId(userId);
        remoteWxUserService.updateUserInfo(wxUser, SecurityConstants.INNER);
        R<WxUser> userResult = remoteWxUserService.getUserInfo(wxUser.getPhoneNumber(), SecurityConstants.INNER);
        WxUser wxUserNew = userResult.getData();
        if(wxUserNew == null){
            throw new ServiceException("用户信息获取失败！");
        }
        LoginWxUser loginWxUser = new LoginWxUser();
        loginWxUser.setWxUser(wxUserNew);
        return loginWxUser;
    }
    /**
     * 根据code获取openid
     * @param loginWxForm
     * @return
     */
    public String getOpenidByCode(LoginWxForm loginWxForm) {
        if(loginWxForm.getWxCode()== null){
            throw new ServiceException("微信code不能为空");
        }
        // 微信登录，获取openid
        JSONObject SessionKeyOpenId = weChatRestTemplate.loginWx(loginWxForm.getWxCode());
        String openid = SessionKeyOpenId.getString("openid");
        String sessionKey = SessionKeyOpenId.getString("session_key");
        return openid;
    }
}
