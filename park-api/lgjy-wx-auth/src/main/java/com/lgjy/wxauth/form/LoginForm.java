package com.lgjy.wxauth.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class LoginForm {
    // 手机号
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String phoneNumber;

    // 验证码
    private String msgCode;

    // 微信code(获取openid)
    private String wxCode;

    // 旧手机号
    private String oldPhone;

}
