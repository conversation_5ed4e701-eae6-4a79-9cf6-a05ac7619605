package com.lgjy.wxauth.controller;

import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.security.service.WxTokenService;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.system.api.model.LoginWxUser;
import com.lgjy.wxauth.form.LoginForm;
import com.lgjy.wxauth.form.LoginWxForm;
import com.lgjy.wxauth.service.WxLoginService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
public class WxLoginController {

    @Resource
    private WxTokenService wxTokenService;

    @Resource
    private WxLoginService wxLoginService;

    /**
     * 发送验证码
     * @param loginForm
     * @return
     */
    @PostMapping("/code")
    public R<?> sendCode(@Valid @RequestBody LoginForm loginForm) {
        wxLoginService.sendCode(loginForm.getPhoneNumber());
        return R.ok();
    }

    /**
     * 手机号验证码登录
     * @param loginForm
     * @return
     */
    @PostMapping("/login/code")
    public R<?> login(@RequestBody LoginForm loginForm)
    {
        LoginWxUser loginWxUser = wxLoginService.login(loginForm);
        return R.ok(wxTokenService.createToken(loginWxUser));
    }

    /**
     * 微信一键登录
     * @param
     * @return
     */
    @PostMapping("/login/wx")
    public R<?> loginWx(@RequestBody LoginWxForm loginWxForm) {
        LoginWxUser loginWxUser = wxLoginService.loginWx(loginWxForm);
        // 生成令牌
        return R.ok(wxTokenService.createToken(loginWxUser));
    }

    /**
     * 退出登录
     * @param request
     * @return
     */
    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            wxTokenService.logout(token);
        }
        return R.ok();
    }

    /**
     * 修改个人信息时候根据phoneCode获取手机号
     * @param phoneCode
     * @return
     */
    @GetMapping("/phoneCode/{phoneCode}")
    public R<?> getPhoneNumberByCode(@PathVariable String phoneCode) {
        return R.ok(wxLoginService.getPhoneNumberByCode(phoneCode));
    }

    /**
     * 更新用户信息
     * @param wxUser
     * @return
     */
    @PostMapping("/wxUser/update")
    public R<?> updateWxUser(@RequestBody WxUser wxUser,HttpServletRequest request) {
        LoginWxUser loginWxUser=wxLoginService.updateWxUser(wxUser);
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            wxTokenService.logout(token);
        }
        return R.ok(wxTokenService.createToken(loginWxUser));
    }
    /**
     * 根据code获取openid
     * @param loginWxForm
     * @return
     */
    @PostMapping("/openid")
    public R<?> getOpenidByCode(@RequestBody LoginWxForm loginWxForm) {
        return R.ok(wxLoginService.getOpenidByCode(loginWxForm));
    }
}
