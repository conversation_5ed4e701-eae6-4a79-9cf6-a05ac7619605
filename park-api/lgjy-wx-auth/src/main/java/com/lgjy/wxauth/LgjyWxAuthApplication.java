package com.lgjy.wxauth;

import com.lgjy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 微信小程序认证授权中心
 */
@EnableDiscoveryClient
@EnableRyFeignClients
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class LgjyWxAuthApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjyWxAuthApplication.class, args);
        System.out.println("微信小程序认证授权中心启动成功");
    }
}