package com.lgjy.wxauth.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.Collections;

@Slf4j
@Component
public class MsgApiUtils {
    private final MsgServerProperties msgServerProperties;
    private final RestTemplate restTemplate;
    private HttpHeaders headers;

    public MsgApiUtils(MsgServerProperties msgServerProperties, @Qualifier("weChatClientRestTemplate") RestTemplate restTemplate) {
        this.msgServerProperties = msgServerProperties;
        this.restTemplate = restTemplate;
    }
    // bean初始化完成时执行
    @PostConstruct
    public void init() {
        this.headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setBasicAuth(msgServerProperties.getApi(), msgServerProperties.getApiKey());
    }
    // 发送单条短信
    public String msgSend(String phone, String content) {
        try {
            String httpResponse = send(phone, content);
            JSONObject jsonObj = JSON.parseObject(httpResponse);
            int error_code = jsonObj.getIntValue("error");
            return error_code == 0 ? "0" : jsonObj.getString("msg");
        } catch (Exception e) {
            log.error("短信发送异常", e);
            return "网络信息故障，请重试";
        }
    }

    private String send(String phone, String content) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("mobile", phone);
        formData.add("message", content);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(formData, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(
                msgServerProperties.getUrl() + "/v1/send.json",
                request,
                String.class
        );
        return response.getBody();
    }

    // 批量发送短信
    public String msgSendBatch(String mobileList, String content) {
        try {
            String httpResponse = sendBatch(mobileList, content);
            JSONObject jsonObj = JSON.parseObject(httpResponse);
            int error_code = jsonObj.getIntValue("error");
            return error_code == 0 ? "0" : jsonObj.getString("msg");
        } catch (Exception e) {
            log.error("短信发送异常", e);
            return "网络信息故障，请重试";
        }
    }

    private String sendBatch(String mobileList, String content) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("mobile_list", mobileList);
        formData.add("message", content);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(formData, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(
                msgServerProperties.getUrl() + "/v1/send_batch.json",
                request,
                String.class
        );
        // 获取响应体
        return response.getBody();
    }
}
