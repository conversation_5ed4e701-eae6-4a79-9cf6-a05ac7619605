package com.lgjy.wxauth.rt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "wx-api")
public class WxProperties {
    /** 服务器地址 */
    private String url;

    /** 手机号地址 */
    private String phoneUrl;

    /** 认证地址 */
    private String accessUrl;

    /** 用户信息地址 */
    private String userInfoUrl;

    /** 小程序唯一id */
    private String appid;

    /** 小程序秘钥 */
    private String secret;

    /** 验证标识 */
    private String grantType;
}
