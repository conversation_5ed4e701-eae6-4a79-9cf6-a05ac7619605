package com.lgjy.wxauth.utils;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "msg-server")
public class MsgServerProperties {
    // 服务器地址
    private String url;

    // 权限验证用户
    private String api;

    // 权限验证密码
    private String apiKey;

    // 签名
    private String sign;

    // 短信模版
    private String template;
}
