package com.lgjy.system.api;

import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.constant.ServiceNameConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.system.api.factory.RemoteWxUserFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 小程序用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteWxUserService", value = ServiceNameConstants.WX_SERVICE, fallbackFactory = RemoteWxUserFallbackFactory.class)
public interface RemoteWxUserService {
    /**
     * 通过用户手机号查询用户信息
     *
     * @param phoneNumber 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{phoneNumber}")
    public R<WxUser> getUserInfo(@PathVariable("phoneNumber") String phoneNumber, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增用户
     * @param wxUser
     * @param source
     * @return
     */
    @PostMapping("/user/insert")
    public R<?> insertUser(@RequestBody WxUser wxUser, @RequestHeader(SecurityConstants.FROM_SOURCE)String source);

    /**
     * 通过微信openid查询用户信息
     *
     * @param openid 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/openid/{openid}")
    R<WxUser> getUserInfoByOpenId(@PathVariable("openid") String openid, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 更新用户
     * @param wxUser
     * @param source
     * @return
     */
    @PostMapping("/user/update")
    public R<?> updateUserInfo(@RequestBody WxUser wxUser, @RequestHeader(SecurityConstants.FROM_SOURCE)String source);
}
