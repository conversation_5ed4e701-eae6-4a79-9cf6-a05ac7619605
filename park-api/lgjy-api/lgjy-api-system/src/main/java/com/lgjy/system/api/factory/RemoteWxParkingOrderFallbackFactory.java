package com.lgjy.system.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.RemoteWxParkingOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 小程序停车订单服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteWxParkingOrderFallbackFactory implements FallbackFactory<RemoteWxParkingOrderService> {
    
    private static final Logger log = LoggerFactory.getLogger(RemoteWxParkingOrderFallbackFactory.class);

    @Override
    public RemoteWxParkingOrderService create(Throwable throwable) {
        log.error("小程序停车订单服务调用失败:{}", throwable.getMessage());
        return new RemoteWxParkingOrderService() {
            @Override
            public R<JSONObject> refundParkingOrder(String tradeId, BigDecimal refundAmount, String refundReason, String source) {
                return R.fail("停车订单退款失败:" + throwable.getMessage());
            }
        };
    }
}
