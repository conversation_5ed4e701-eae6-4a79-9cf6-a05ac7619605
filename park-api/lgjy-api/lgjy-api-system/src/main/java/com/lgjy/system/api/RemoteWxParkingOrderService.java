package com.lgjy.system.api;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.constant.ServiceNameConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.factory.RemoteWxParkingOrderFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 小程序停车订单服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteWxParkingOrderService", value = ServiceNameConstants.WX_SERVICE, fallbackFactory = RemoteWxParkingOrderFallbackFactory.class)
public interface RemoteWxParkingOrderService {
    
    /**
     * 停车订单退款
     *
     * @param tradeId 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/parking/order/refund")
    R<JSONObject> refundParkingOrder(@RequestParam("tradeId") String tradeId,
                                     @RequestParam("refundAmount") BigDecimal refundAmount,
                                     @RequestParam("refundReason") String refundReason,
                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
