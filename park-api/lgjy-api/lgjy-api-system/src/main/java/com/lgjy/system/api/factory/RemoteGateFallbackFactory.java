package com.lgjy.system.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.RemoteGateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 道闸服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteGateFallbackFactory implements FallbackFactory<RemoteGateService> {
    @Override
    public RemoteGateService create(Throwable throwable) {
        log.error("道闸服务调用失败:{}", throwable.getMessage());
        return new RemoteGateService() {
            @Override
            public R<String> findParkingRate(JSONObject requestData, String source) {
                return R.fail("道闸查询失败" + throwable.getMessage());
            }

            @Override
            public R<String> payOrder(JSONObject requestData, String source) {
                return R.fail("道闸查询失败" + throwable.getMessage());
            }

            @Override
            public R<String> outPayQuery(JSONObject requestData, String source) {
                return R.fail("道闸查询失败" + throwable.getMessage());
            }

            @Override
            public R<String> noPlateIn(JSONObject requestData, String source) {
                return R.fail("道闸查询失败" + throwable.getMessage());
            }

            @Override
            public R<String> saveMonthCar(JSONObject requestData, String source) {
                return R.fail("月卡保存失败" + throwable.getMessage());
            }

            @Override
            public R<String> delMonthCar(JSONObject requestData, String source) {
                return R.fail("月卡删除失败" + throwable.getMessage());
            }

            @Override
            public R<String> saveFreeCar(JSONObject requestData, String source) {
                return R.fail("道闸服务调用失败" + throwable.getMessage());
            }

            @Override
            public R<String> saveBlackCar(JSONObject requestData, String source) {
                return R.fail("道闸服务调用失败" + throwable.getMessage());
            }

            @Override
            public R<String> delBlackCar(JSONObject requestData, String source) {
                return R.fail("道闸服务调用失败" + throwable.getMessage());
            }
        };
    }
}
