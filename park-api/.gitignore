######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml
### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
nbdist/
.nb-gradle/

######################################################################
# Others
*.log
*.log.*
*.xml.versionsBackup
*.swp

# 编辑器临时文件
*~
*.bak
*.tmp
*.orig

# 系统文件
Thumbs.db
.DS_Store

# 日志和缓存
logs/

# Maven相关
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

# 运行时文件
*.pid
*.seed
*.pid.lock

# 测试覆盖率
coverage/
*.coverage

# 本地环境配置
.env
.env.local
.env.*.local
application-local.yml
application-dev.yml
application-test.yml

!*/build/*.java
!*/build/*.html
!*/build/*.xml
sql/advert_config_fix_readme.md
sql/mini_advert_config.sql
sql/mini_advert_config.sql
sql/fix_advert_config_test.sql
