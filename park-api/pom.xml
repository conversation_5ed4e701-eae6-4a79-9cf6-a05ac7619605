<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lgjy</groupId>
    <artifactId>lgjy</artifactId>
    <version>3.6.6</version>

    <name>lgjy</name>
    <!--    <url>http://www.ruoyi.vip</url>-->
    <description>捷运停车管理系统</description>

    <properties>
        <lgjy.version>3.6.6</lgjy.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.9</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.6.1</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.16</spring-boot-admin.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.0.0</pagehelper.boot.version>
        <druid.version>1.2.25</druid.version>
        <commons.io.version>2.19.0</commons.io.version>

        <fastjson.version>2.0.57</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>

        <transmittable-thread-local.version>2.14.4</transmittable-thread-local.version>
        <!-- override dependency version -->
        <tomcat.version>9.0.105</tomcat.version>
        <logback.version>1.2.13</logback.version>
        <spring-framework.version>5.3.39</spring-framework.version>

        <!-- Docker相关配置 -->
        <docker.maven.plugin.version>0.44.0</docker.maven.plugin.version>
        <docker.image.prefix>park</docker.image.prefix>
        <!-- 使用JDK镜像以支持更好的调试和开发 -->
        <docker.base.image>eclipse-temurin:8-jdk</docker.base.image>
        <!-- 备用基础镜像选项 -->
        <!-- <docker.base.image>eclipse-temurin:8-jre-alpine</docker.base.image> -->
        <!-- <docker.base.image>openjdk:8-jre-alpine</docker.base.image> -->
        <docker.registry>docker.io</docker.registry>
        <docker.push.registry>docker.io</docker.push.registry>

        <!-- Docker构建控制属性（默认值，可被profile覆盖） -->
        <docker.skip>true</docker.skip>
        <docker.skipBuild>true</docker.skipBuild>
        <docker.skipPush>true</docker.skipPush>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- 覆盖SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 覆盖logback的依赖配置-->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- 覆盖tomcat的依赖配置-->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>



            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>



            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- FastJSON 旧版本 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>

            <!-- MySQL 驱动 - 使用稳定的Maven坐标 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.33</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- Druid 数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>tools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>jconsole</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Druid Spring Boot Starter -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>tools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>jconsole</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-core</artifactId>
                <version>${lgjy.version}</version>
            </dependency>



            <!-- 安全模块 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-security</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 数据脱敏 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-sensitive</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-datascope</artifactId>
                <version>${lgjy.version}</version>
            </dependency>



            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-seata</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-log</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-redis</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 数据源服务 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-common-datasource</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-api-system</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

            <!-- 道闸设备控制API接口 -->
            <dependency>
                <groupId>com.lgjy</groupId>
                <artifactId>lgjy-api-gate</artifactId>
                <version>${lgjy.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>lgjy-auth</module>
        <module>lgjy-gateway</module>
        <module>lgjy-modules</module>
        <module>lgjy-api</module>
        <module>lgjy-common</module>
        <module>lgjy-wx-auth</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Docker Maven Plugin -->
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.maven.plugin.version}</version>
                    <configuration>
                        <!-- 支持profile控制Docker构建 -->
                        <skip>${docker.skip}</skip>
                        <skipBuild>${docker.skipBuild}</skipBuild>
                        <skipPush>${docker.skipPush}</skipPush>
                        <images>
                            <image>
                                <name>${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                                <build>
                                    <from>${docker.base.image}</from>
                                    <assembly>
                                        <descriptorRef>artifact-with-dependencies</descriptorRef>
                                        <targetDir>/app</targetDir>
                                    </assembly>
                                    <cmd>java -jar /app/${project.artifactId}-${project.version}.jar</cmd>
                                    <ports>
                                        <port>8080</port>
                                    </ports>
                                </build>
                            </image>
                        </images>
                        <!-- 镜像拉取策略：如果本地存在则不拉取 -->
                        <imagePullPolicy>IfNotPresent</imagePullPolicy>
                        <buildArgs>
                            <JAVA_OPTS>-Xms256m -Xmx512m</JAVA_OPTS>
                        </buildArgs>
                    </configuration>
                    <executions>
                        <execution>
                            <id>docker-build</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- Maven Profiles 配置 -->
    <profiles>
        <!-- 开发环境：跳过Docker构建 -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <docker.skip>true</docker.skip>
                <docker.skipBuild>true</docker.skipBuild>
                <docker.skipPush>true</docker.skipPush>
            </properties>
        </profile>

        <!-- Docker构建环境：启用Docker构建 -->
        <profile>
            <id>docker</id>
            <properties>
                <docker.skip>false</docker.skip>
                <docker.skipBuild>false</docker.skipBuild>
                <docker.skipPush>true</docker.skipPush>
            </properties>
        </profile>

        <!-- 生产环境：启用Docker构建和推送 -->
        <profile>
            <id>prod</id>
            <properties>
                <docker.skip>false</docker.skip>
                <docker.skipBuild>false</docker.skipBuild>
                <docker.skipPush>false</docker.skipPush>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo1.maven.org/maven2/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo1.maven.org/maven2/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>