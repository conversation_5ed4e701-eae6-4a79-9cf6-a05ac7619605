-- MySQL dump 10.13  Distrib 8.0.43, for Linux (x86_64)
--
-- Host: localhost    Database: nacos
-- ------------------------------------------------------
-- Server version	8.0.43

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `config_info`
--

DROP TABLE IF EXISTS `config_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'group_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'configuration description',
  `c_use` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'configuration usage',
  `effect` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '配置生效的描述',
  `type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '配置的类型',
  `c_schema` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '配置的模式',
  `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_configinfo_datagrouptenant` (`data_id`,`group_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='config_info';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info`
--

LOCK TABLES `config_info` WRITE;
/*!40000 ALTER TABLE `config_info` DISABLE KEYS */;
INSERT INTO `config_info` VALUES (2,'park-gateway-dev.yml','DEFAULT_GROUP','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # è®¤è¯ä¸­å¿ƒ\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # éªŒè¯ç å¤„ç†\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # ç³»ç»Ÿæ¨¡å—\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # æ–‡ä»¶æœåŠ¡\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # å¾®ä¿¡å°ç¨‹åºè®¤è¯ä¸­å¿ƒ\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # å¾®ä¿¡å°ç¨‹åº\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # é“é—¸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# å®‰å…¨é…ç½®\nsecurity:\n  # éªŒè¯ç \n  captcha:\n    enabled: true\n    type: math\n  # é˜²æ­¢XSSæ”»å‡»\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut','e6f0cead56e94162e99231d0c3d18d79','2020-05-14 14:17:55','2025-07-13 10:32:12',NULL,'0:0:0:0:0:0:0:1',NULL,'','ç½‘å…³æ¨¡å—',NULL,NULL,'yaml',NULL,''),(3,'park-auth-dev.yml','DEFAULT_GROUP','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2','756a03974b815ff3febec9744c633cf4','2020-11-20 00:00:00','2025-07-13 13:38:03',NULL,'0:0:0:0:0:0:0:1',NULL,'','è®¤è¯ä¸­å¿ƒ',NULL,NULL,'yaml',NULL,''),(5,'park-system-dev.yml','DEFAULT_GROUP','# springé…ç½®\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatisé…ç½®\nmybatis:\n    # æœç´¢æŒ‡å®šåŒ…åˆ«å\n    typeAliasesPackage: com.lgjy.system\n    # é…ç½®mapperçš„æ‰«æï¼Œæ‰¾åˆ°æ‰€æœ‰çš„mapper.xmlæ˜ å°„æ–‡ä»¶\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2020-11-20 00:00:00','2025-07-13 14:13:30',NULL,'0:0:0:0:0:0:0:1',NULL,'','ç³»ç»Ÿæ¨¡å—',NULL,NULL,'yaml',NULL,''),(8,'park-file-dev.yml','DEFAULT_GROUP','# æœ¬åœ°æ–‡ä»¶ä¸Šä¼     \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFSé…ç½®\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minioé…ç½®\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # å•ä¸ªæ–‡ä»¶æœ€å¤§10MB\n      max-request-size: 20MB    # æ•´ä¸ªè¯·æ±‚æœ€å¤§20MB\n      enabled: true             # å¯ç”¨æ–‡ä»¶ä¸Šä¼ åŠŸèƒ½\n      resolve-lazily: false     # æ˜¯å¦å»¶è¿Ÿè§£æžï¼ˆå¤§æ–‡ä»¶å»ºè®®trueï¼‰','95bb71a3e1301faa5d04c1e6685936cb','2020-11-20 00:00:00','2025-07-13 10:32:35',NULL,'0:0:0:0:0:0:0:1',NULL,'',NULL,NULL,NULL,'yaml',NULL,''),(10,'park-wxauth-dev.yml','DEFAULT_GROUP','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # è¿žæŽ¥æ± æœ€å¤§è¿žæŽ¥æ•°\n        max-active: 200\n        # è¿žæŽ¥æ± æœ€å¤§é˜»å¡žç­‰å¾…æ—¶é—´ï¼ˆä½¿ç”¨è´Ÿå€¼è¡¨ç¤ºæ²¡æœ‰é™åˆ¶ï¼‰\n        max-wait: -1ms\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å¤§ç©ºé—²è¿žæŽ¥\n        max-idle: 10\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å°ç©ºé—²è¿žæŽ¥\n        min-idle: 0\n    \n# çŸ­ä¿¡éªŒè¯é…ç½®\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: ã€æ·è¿åœè½¦å……ç”µã€‘\n  template: \"æ‚¨æ­£åœ¨è¿›è¡ŒçŸ­ä¿¡éªŒè¯ï¼ŒéªŒè¯ç ä¸ºï¼š{0}ï¼Œæœ‰æ•ˆæœŸï¼š{1}åˆ†é’Ÿï¼Œè¯·å‹¿æ³„éœ²ç»™ä»–äººã€‚\"\n\n# å°ç¨‹åºå¾®ä¿¡APIé…ç½®\nwx-api:\n  # å°ç¨‹åºåŸºç¡€å‚æ•°\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# å¥åº·æ£€æŸ¥é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','a85b912b55c71764159f7f1487c59c5a','2025-06-07 11:34:11','2025-07-25 15:34:52',NULL,'0:0:0:0:0:0:0:1',NULL,'','å¾®ä¿¡å°ç¨‹åºè®¤è¯ä¸­å¿ƒ',NULL,NULL,'yaml',NULL,''),(11,'park-wx-dev.yml','DEFAULT_GROUP','# springé…ç½®\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatisé…ç½®\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# é“¶è”API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # å‘ç¥¨\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # å‘ç¥¨å›žè°ƒ\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','10d2a8b81d651cbab122c40b15714342','2025-06-07 18:11:24','2025-07-16 01:57:31',NULL,'0:0:0:0:0:0:0:1',NULL,'',NULL,NULL,NULL,'yaml',NULL,''),(21,'park-gate-dev.yml','DEFAULT_GROUP','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatisé…ç½®\nmybatis:\n    # æœç´¢æŒ‡å®šåŒ…åˆ«å\n    typeAliasesPackage: com.lgjy.gate\n    # é…ç½®mapperçš„æ‰«æï¼Œæ‰¾åˆ°æ‰€æœ‰çš„mapper.xmlæ˜ å°„æ–‡ä»¶\n    mapperLocations: classpath:mapper/**/*.xml\n    # å¼€å¯é©¼å³°å‘½åè‡ªåŠ¨æ˜ å°„\n    configuration:\n      mapUnderscoreToCamelCase: true','e5c4a07fc4ddfc06f70c489f3cda79bc','2025-06-10 21:01:06','2025-07-13 14:01:56',NULL,'0:0:0:0:0:0:0:1',NULL,'','lgjy-gateæ¨¡å—æ•°æ®æºé…ç½®',NULL,NULL,'yaml',NULL,''),(24,'sizhuo-config.yml','DEFAULT_GROUP','sizhuo:\n  brandId: sizhuo\n  brandName: æ€å“\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: æ±Ÿå±±è·¯\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]','c8e6072bf8e82c585544fb23dc829893','2025-07-11 10:32:41','2025-07-13 14:01:24',NULL,'0:0:0:0:0:0:0:1',NULL,'',NULL,NULL,NULL,'yaml',NULL,''),(26,'common-dev.yml','DEFAULT_GROUP','# ===========================================\n# é€šç”¨é…ç½®æ–‡ä»¶ - æ‰€æœ‰å¾®æœåŠ¡å…±äº«\n# ===========================================\n\n# æ—¥å¿—é…ç½®\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinelé…ç½®\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false','9b888a075373e2b0decd67ff85fa7ee7','2025-07-12 09:52:04','2025-07-13 14:00:47',NULL,'0:0:0:0:0:0:0:1',NULL,'','é€šç”¨é…ç½®æ–‡ä»¶-æ‰€æœ‰å¾®æœåŠ¡å…±äº«',NULL,NULL,'yaml',NULL,''),(55,'park-gateway-test.yml','DEFAULT_GROUP','spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n\n        # è®¤è¯ä¸­å¿ƒ\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # éªŒè¯ç å¤„ç†\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # ç³»ç»Ÿæ¨¡å—\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # æ–‡ä»¶æœåŠ¡\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # å¾®ä¿¡å°ç¨‹åºè®¤è¯ä¸­å¿ƒ\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # å¾®ä¿¡å°ç¨‹åº\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # é“é—¸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# å®‰å…¨é…ç½®\nsecurity:\n  # éªŒè¯ç \n  captcha:\n    enabled: true\n    type: math\n  # é˜²æ­¢XSSæ”»å‡»\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /code\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut','8b23ffc4dfa5742ef5574ac95451399c','2025-07-20 21:18:45','2025-07-25 13:49:07','nacos','*************',NULL,'a670e801-046b-4366-85ba-7643de52359f','è¿™æ˜¯æµ‹è¯•çŽ¯å¢ƒçš„ç½‘å…³é…ç½®æ–‡ä»¶',NULL,NULL,'yaml',NULL,''),(56,'park-auth-test.yml','DEFAULT_GROUP','# æ•°æ®æºé…ç½®\nspring:\n  datasource:\n    driverClassName: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # åˆå§‹è¿žæŽ¥æ•°\n      initialSize: 5\n      # æœ€å°è¿žæŽ¥æ± æ•°é‡\n      minIdle: 10\n      # æœ€å¤§è¿žæŽ¥æ± æ•°é‡\n      maxActive: 20\n      # é…ç½®èŽ·å–è¿žæŽ¥ç­‰å¾…è¶…æ—¶çš„æ—¶é—´\n      maxWait: 60000\n      # é…ç½®è¿žæŽ¥è¶…æ—¶æ—¶é—´\n      connectTimeout: 30000\n      # é…ç½®ç½‘ç»œè¶…æ—¶æ—¶é—´\n      socketTimeout: 60000\n      # é…ç½®é—´éš”å¤šä¹…æ‰è¿›è¡Œä¸€æ¬¡æ£€æµ‹ï¼Œæ£€æµ‹éœ€è¦å…³é—­çš„ç©ºé—²è¿žæŽ¥ï¼Œå•ä½æ˜¯æ¯«ç§’\n      timeBetweenEvictionRunsMillis: 60000\n      # é…ç½®ä¸€ä¸ªè¿žæŽ¥åœ¨æ± ä¸­æœ€å°ç”Ÿå­˜çš„æ—¶é—´ï¼Œå•ä½æ˜¯æ¯«ç§’\n      minEvictableIdleTimeMillis: 300000\n      # é…ç½®ä¸€ä¸ªè¿žæŽ¥åœ¨æ± ä¸­æœ€å¤§ç”Ÿå­˜çš„æ—¶é—´ï¼Œå•ä½æ˜¯æ¯«ç§’\n      maxEvictableIdleTimeMillis: 900000\n      # é…ç½®æ£€æµ‹è¿žæŽ¥æ˜¯å¦æœ‰æ•ˆ\n      validationQuery: SELECT 1 FROM DUAL\n      testWhileIdle: true\n      testOnBorrow: false\n      testOnReturn: false\n      webStatFilter:\n        enabled: true\n      statViewServlet:\n        enabled: true\n        # è®¾ç½®ç™½åå•ï¼Œä¸å¡«åˆ™å…è®¸æ‰€æœ‰è®¿é—®\n        allow:\n        url-pattern: /druid/*\n        # æŽ§åˆ¶å°ç®¡ç†ç”¨æˆ·åå’Œå¯†ç \n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # æ…¢SQLè®°å½•\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # è¿žæŽ¥æ± æœ€å¤§è¿žæŽ¥æ•°\n        max-active: 200\n        # è¿žæŽ¥æ± æœ€å¤§é˜»å¡žç­‰å¾…æ—¶é—´ï¼ˆä½¿ç”¨è´Ÿå€¼è¡¨ç¤ºæ²¡æœ‰é™åˆ¶ï¼‰\n        max-wait: -1ms\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å¤§ç©ºé—²è¿žæŽ¥\n        max-idle: 10\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å°ç©ºé—²è¿žæŽ¥\n        min-idle: 0\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','65cd5a683b96f5a56c1a645c97ef6424','2025-07-20 21:21:52','2025-07-25 13:41:47','nacos','***************',NULL,'a670e801-046b-4366-85ba-7643de52359f','è®¤è¯ä¸­å¿ƒæµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(57,'park-system-test.yml','DEFAULT_GROUP','# æ•°æ®æºé…ç½®\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # åˆå§‹è¿žæŽ¥æ•°\n      initial-size: 5\n      # æœ€å°è¿žæŽ¥æ± æ•°é‡\n      min-idle: 10\n      # æœ€å¤§è¿žæŽ¥æ± æ•°é‡\n      max-active: 20\n      # é…ç½®èŽ·å–è¿žæŽ¥ç­‰å¾…è¶…æ—¶çš„æ—¶é—´\n      max-wait: 60000\n      # é…ç½®è¿žæŽ¥è¶…æ—¶æ—¶é—´\n      connect-timeout: 30000\n      # é…ç½®ç½‘ç»œè¶…æ—¶æ—¶é—´\n      socket-timeout: 60000\n      # é…ç½®é—´éš”å¤šä¹…æ‰è¿›è¡Œä¸€æ¬¡æ£€æµ‹ï¼Œæ£€æµ‹éœ€è¦å…³é—­çš„ç©ºé—²è¿žæŽ¥ï¼Œå•ä½æ˜¯æ¯«ç§’\n      time-between-eviction-runs-millis: 60000\n      # é…ç½®ä¸€ä¸ªè¿žæŽ¥åœ¨æ± ä¸­æœ€å°ç”Ÿå­˜çš„æ—¶é—´ï¼Œå•ä½æ˜¯æ¯«ç§’\n      min-evictable-idle-time-millis: 300000\n      # é…ç½®ä¸€ä¸ªè¿žæŽ¥åœ¨æ± ä¸­æœ€å¤§ç”Ÿå­˜çš„æ—¶é—´ï¼Œå•ä½æ˜¯æ¯«ç§’\n      max-evictable-idle-time-millis: 900000\n      # é…ç½®æ£€æµ‹è¿žæŽ¥æ˜¯å¦æœ‰æ•ˆ\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        # è®¾ç½®ç™½åå•ï¼Œä¸å¡«åˆ™å…è®¸æ‰€æœ‰è®¿é—®\n        allow:\n        url-pattern: /druid/*\n        # æŽ§åˆ¶å°ç®¡ç†ç”¨æˆ·åå’Œå¯†ç \n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # æ…¢SQLè®°å½•\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # è¿žæŽ¥æ± æœ€å¤§è¿žæŽ¥æ•°\n        max-active: 200\n        # è¿žæŽ¥æ± æœ€å¤§é˜»å¡žç­‰å¾…æ—¶é—´ï¼ˆä½¿ç”¨è´Ÿå€¼è¡¨ç¤ºæ²¡æœ‰é™åˆ¶ï¼‰\n        max-wait: -1ms\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å¤§ç©ºé—²è¿žæŽ¥\n        max-idle: 10\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å°ç©ºé—²è¿žæŽ¥\n        min-idle: 0\n\n# MyBatisé…ç½®\nmybatis:\n  # æœç´¢æŒ‡å®šåŒ…åˆ«å\n  type-aliases-package: com.lgjy.system\n  # é…ç½®mapperçš„æ‰«æï¼Œæ‰¾åˆ°æ‰€æœ‰çš„mapper.xmlæ˜ å°„æ–‡ä»¶\n  mapper-locations: classpath:mapper/**/*.xml\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','26bb77068b62aeb9769ad43fa9a2d335','2025-07-20 21:22:45','2025-07-25 13:58:00','nacos','***************',NULL,'a670e801-046b-4366-85ba-7643de52359f','ç³»ç»Ÿæ¨¡å—æµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(58,'park-file-test.yml','DEFAULT_GROUP','# æœ¬åœ°æ–‡ä»¶ä¸Šä¼  - ä¿®æ­£ä¸ºDockerå®¹å™¨è·¯å¾„\nfile:\n    domain: http://127.0.0.1:9300\n    path: /app/upload\n    prefix: /statics\n\n# FastDFSé…ç½®\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minioé…ç½®\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # å•ä¸ªæ–‡ä»¶æœ€å¤§10MB\n      max-request-size: 20MB    # æ•´ä¸ªè¯·æ±‚æœ€å¤§20MB\n      enabled: true             # å¯ç”¨æ–‡ä»¶ä¸Šä¼ åŠŸèƒ½\n      resolve-lazily: false     # æ˜¯å¦å»¶è¿Ÿè§£æžï¼ˆå¤§æ–‡ä»¶å»ºè®®trueï¼‰\n\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','7defcbf1c60debbdb3f1d0f6e68a0918','2025-07-20 21:26:38','2025-07-20 21:36:42',NULL,'0:0:0:0:0:0:0:1',NULL,'a670e801-046b-4366-85ba-7643de52359f','æ–‡ä»¶æœåŠ¡æµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(62,'common-test.yml','DEFAULT_GROUP','# ===========================================\n# é€šç”¨é…ç½®æ–‡ä»¶ - æ‰€æœ‰å¾®æœåŠ¡å…±äº«\n# ===========================================\n\n# æ—¥å¿—é…ç½®\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinelé…ç½®\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','5ca228a0dff86cfd85fa97b51116a91b','2025-07-20 21:30:57','2025-07-20 21:31:15',NULL,'0:0:0:0:0:0:0:1',NULL,'a670e801-046b-4366-85ba-7643de52359f','é€šç”¨é…ç½®æ–‡ä»¶-æ‰€æœ‰å¾®æœåŠ¡å…±äº«æµ‹è¯•çŽ¯å¢ƒ',NULL,NULL,'yaml',NULL,''),(64,'sizhuo-config-test.yml','DEFAULT_GROUP','sizhuo:\n  brandId: sizhuo\n  brandName: æ€å“\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: æ±Ÿå±±è·¯æµ‹è¯•\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]','f0344d688316f6aacb0206b53bb7e799','2025-07-20 21:32:34','2025-07-20 21:32:34',NULL,'0:0:0:0:0:0:0:1',NULL,'a670e801-046b-4366-85ba-7643de52359f','æ€å“é“é—¸æµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(77,'park-wx-test.yml','DEFAULT_GROUP','# æ•°æ®æºé…ç½®\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # åˆå§‹è¿žæŽ¥æ•°\n      initial-size: 5\n      # æœ€å°è¿žæŽ¥æ± æ•°é‡\n      min-idle: 10\n      # æœ€å¤§è¿žæŽ¥æ± æ•°é‡\n      max-active: 20\n      # é…ç½®èŽ·å–è¿žæŽ¥ç­‰å¾…è¶…æ—¶çš„æ—¶é—´\n      max-wait: 60000\n      # é…ç½®è¿žæŽ¥è¶…æ—¶æ—¶é—´\n      connect-timeout: 30000\n      # é…ç½®ç½‘ç»œè¶…æ—¶æ—¶é—´\n      socket-timeout: 60000\n      # é…ç½®é—´éš”å¤šä¹…æ‰è¿›è¡Œä¸€æ¬¡æ£€æµ‹ï¼Œæ£€æµ‹éœ€è¦å…³é—­çš„ç©ºé—²è¿žæŽ¥ï¼Œå•ä½æ˜¯æ¯«ç§’\n      time-between-eviction-runs-millis: 60000\n      # é…ç½®ä¸€ä¸ªè¿žæŽ¥åœ¨æ± ä¸­æœ€å°ç”Ÿå­˜çš„æ—¶é—´ï¼Œå•ä½æ˜¯æ¯«ç§’\n      min-evictable-idle-time-millis: 300000\n      # é…ç½®ä¸€ä¸ªè¿žæŽ¥åœ¨æ± ä¸­æœ€å¤§ç”Ÿå­˜çš„æ—¶é—´ï¼Œå•ä½æ˜¯æ¯«ç§’\n      max-evictable-idle-time-millis: 900000\n      # é…ç½®æ£€æµ‹è¿žæŽ¥æ˜¯å¦æœ‰æ•ˆ\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        # è®¾ç½®ç™½åå•ï¼Œä¸å¡«åˆ™å…è®¸æ‰€æœ‰è®¿é—®\n        allow:\n        url-pattern: /druid/*\n        # æŽ§åˆ¶å°ç®¡ç†ç”¨æˆ·åå’Œå¯†ç \n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # æ…¢SQLè®°å½•\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # è¿žæŽ¥æ± æœ€å¤§è¿žæŽ¥æ•°\n        max-active: 200\n        # è¿žæŽ¥æ± æœ€å¤§é˜»å¡žç­‰å¾…æ—¶é—´ï¼ˆä½¿ç”¨è´Ÿå€¼è¡¨ç¤ºæ²¡æœ‰é™åˆ¶ï¼‰\n        max-wait: -1ms\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å¤§ç©ºé—²è¿žæŽ¥\n        max-idle: 10\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å°ç©ºé—²è¿žæŽ¥\n        min-idle: 0\n\n# MyBatisé…ç½®\nmybatis:\n  # æœç´¢æŒ‡å®šåŒ…åˆ«å\n  type-aliases-package: com.lgjy.wx\n  # é…ç½®mapperçš„æ‰«æï¼Œæ‰¾åˆ°æ‰€æœ‰çš„mapper.xmlæ˜ å°„æ–‡ä»¶\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    map-underscore-to-camel-case: true\n\n# å°ç¨‹åºAPIé…ç½®\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grant-type: authorization_code\n\n# é“¶è”æ”¯ä»˜APIé…ç½®\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  app-id: wxdcd31ee3e79190cc\n  app-key: 2e18a85cfa56e071fa00e96aaa9012cc\n  notify-url-order: https://test-parking.example.com/wx/parking/order/payCallback\n  notify-url-vip: https://test-parking.example.com/wx/package/payCallback\n  notify-url-order-alipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n  timeout: 5\n  # å‘ç¥¨é…ç½®\n  invoice-url: https://fapiao.chinaums.com/fapiao-api/\n  invoice-msg-src: LINGANG_JT\n  invoice-key: 12cd2d10f2204107bf87df57aa7a4c4e\n  # å‘ç¥¨å›žè°ƒåœ°å€\n  notify-url-order-invoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','617b95903a82bad4ae59e648068d3d4d','2025-07-25 13:18:27','2025-07-25 16:04:02','nacos','127.0.0.1',NULL,'a670e801-046b-4366-85ba-7643de52359f','å¾®ä¿¡å°ç¨‹åºæµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(78,'park-wxauth-test.yml','DEFAULT_GROUP','spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # è¿žæŽ¥æ± æœ€å¤§è¿žæŽ¥æ•°\n        max-active: 200\n        # è¿žæŽ¥æ± æœ€å¤§é˜»å¡žç­‰å¾…æ—¶é—´ï¼ˆä½¿ç”¨è´Ÿå€¼è¡¨ç¤ºæ²¡æœ‰é™åˆ¶ï¼‰\n        max-wait: -1ms\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å¤§ç©ºé—²è¿žæŽ¥\n        max-idle: 10\n        # è¿žæŽ¥æ± ä¸­çš„æœ€å°ç©ºé—²è¿žæŽ¥\n        min-idle: 0\n    \n# çŸ­ä¿¡æœåŠ¡é…ç½®\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: test-api-user\n  api-key: ************************************\n  sign: ã€æ·è¿åœè½¦å……ç”µã€‘\n  template: \"æ‚¨æ­£åœ¨è¿›è¡ŒçŸ­ä¿¡éªŒè¯ï¼ŒéªŒè¯ç ä¸ºï¼š{0}ï¼Œæœ‰æ•ˆæœŸï¼š{1}åˆ†é’Ÿï¼Œè¯·å‹¿æ³„éœ²ç»™ä»–äººã€‚\"\n\n# å¾®ä¿¡å°ç¨‹åºAPIé…ç½®\nwx-api:\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# å¥åº·æ£€æŸ¥é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','10004511d160ad7b9ad19e43d28a2d52','2025-07-25 13:18:44','2025-07-25 15:35:03','nacos','127.0.0.1',NULL,'a670e801-046b-4366-85ba-7643de52359f','å¾®ä¿¡è®¤è¯æµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(79,'park-gate-test.yml','DEFAULT_GROUP','# springé…ç½®\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true\n    username: ${DB_USERNAME:admin}\n    password: ${DB_PASSWORD:K7PmdL9Rf2Q}\n    druid:\n      initial-size: ${DB_INITIAL_SIZE:5}\n      min-idle: ${DB_MIN_IDLE:5}\n      max-active: ${DB_MAX_ACTIVE:20}\n      max-wait: ${DB_MAX_WAIT:60000}\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatisé…ç½®\nmybatis:\n  # æœç´¢æŒ‡å®šåŒ…åˆ«å\n  typeAliasesPackage: com.lgjy.gate\n  # é…ç½®mapperçš„æ‰«æï¼Œæ‰¾åˆ°æ‰€æœ‰çš„mapper.xmlæ˜ å°„æ–‡ä»¶\n  mapperLocations: classpath:mapper/**/*.xml\n  # å¼€å¯é©¼å³°å‘½åè‡ªåŠ¨æ˜ å°„\n  configuration:\n    mapUnderscoreToCamelCase: true\n\n# ç®¡ç†ç«¯ç‚¹é…ç½®\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','349c5ff4dc41c670b0d8f21a8d1a5025','2025-07-25 13:18:54','2025-07-25 13:18:54','nacos','127.0.0.1',NULL,'a670e801-046b-4366-85ba-7643de52359f','é“é—¸æœåŠ¡æµ‹è¯•çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,''),(80,'sizhuo-config-dev.yml','DEFAULT_GROUP','sizhuo:\n  brandId: sizhuo\n  brandName: æ€å“\n  requestUrl: http://dev-qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: æ±Ÿå±±è·¯å¼€å‘\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]','d4fe24705cbb6bd59caea6635493cefa','2025-07-25 13:19:28','2025-07-25 13:19:28','nacos','127.0.0.1',NULL,'','æ€å“è®¾å¤‡å¼€å‘çŽ¯å¢ƒé…ç½®',NULL,NULL,'yaml',NULL,'');
/*!40000 ALTER TABLE `config_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_aggr`
--

DROP TABLE IF EXISTS `config_info_aggr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_aggr` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_configinfoaggr_datagrouptenantdatum` (`data_id`,`group_id`,`tenant_id`,`datum_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='增加租户字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_aggr`
--

LOCK TABLES `config_info_aggr` WRITE;
/*!40000 ALTER TABLE `config_info_aggr` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_aggr` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_beta`
--

DROP TABLE IF EXISTS `config_info_beta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_beta` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_configinfobeta_datagrouptenant` (`data_id`,`group_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='config_info_beta';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_beta`
--

LOCK TABLES `config_info_beta` WRITE;
/*!40000 ALTER TABLE `config_info_beta` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_beta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_gray`
--

DROP TABLE IF EXISTS `config_info_gray`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_gray` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'group_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'md5',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT 'src_user',
  `src_ip` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'src_ip',
  `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'gmt_create',
  `gmt_modified` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'gmt_modified',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'app_name',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT 'tenant_id',
  `gray_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'gray_name',
  `gray_rule` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'gray_rule',
  `encrypted_data_key` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT 'encrypted_data_key',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_configinfogray_datagrouptenantgray` (`data_id`,`group_id`,`tenant_id`,`gray_name`) USING BTREE,
  KEY `idx_dataid_gmt_modified` (`data_id`,`gmt_modified`) USING BTREE,
  KEY `idx_gmt_modified` (`gmt_modified`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='config_info_gray';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_gray`
--

LOCK TABLES `config_info_gray` WRITE;
/*!40000 ALTER TABLE `config_info_gray` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_gray` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_tag`
--

DROP TABLE IF EXISTS `config_info_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_configinfotag_datagrouptenanttag` (`data_id`,`group_id`,`tenant_id`,`tag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='config_info_tag';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_tag`
--

LOCK TABLES `config_info_tag` WRITE;
/*!40000 ALTER TABLE `config_info_tag` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_tags_relation`
--

DROP TABLE IF EXISTS `config_tags_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_tags_relation` (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT COMMENT 'nid, 自增长标识',
  PRIMARY KEY (`nid`) USING BTREE,
  UNIQUE KEY `uk_configtagrelation_configidtag` (`id`,`tag_name`,`tag_type`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='config_tag_relation';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_tags_relation`
--

LOCK TABLES `config_tags_relation` WRITE;
/*!40000 ALTER TABLE `config_tags_relation` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_tags_relation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `group_capacity`
--

DROP TABLE IF EXISTS `group_capacity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_group_id` (`group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='集群、各Group容量信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `group_capacity`
--

LOCK TABLES `group_capacity` WRITE;
/*!40000 ALTER TABLE `group_capacity` DISABLE KEYS */;
/*!40000 ALTER TABLE `group_capacity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `his_config_info`
--

DROP TABLE IF EXISTS `his_config_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `his_config_info` (
  `id` bigint unsigned NOT NULL COMMENT 'id',
  `nid` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'nid, 自增标识',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `op_type` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'operation type',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
  `publish_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT 'formal' COMMENT 'publish type gray or formal',
  `gray_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'gray name',
  `ext_info` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'ext info',
  PRIMARY KEY (`nid`) USING BTREE,
  KEY `idx_gmt_create` (`gmt_create`) USING BTREE,
  KEY `idx_gmt_modified` (`gmt_modified`) USING BTREE,
  KEY `idx_did` (`data_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=228 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='多租户改造';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `his_config_info`
--

LOCK TABLES `his_config_info` WRITE;
/*!40000 ALTER TABLE `his_config_info` DISABLE KEYS */;
INSERT INTO `his_config_info` VALUES (2,87,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/paycallback\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n','d7c8435c84370c5ae96975d49b2b428a','2025-06-26 14:03:11','2025-06-26 06:03:11',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,88,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/payCallback\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','e7d72822e64d76bd2e3404b6cea1550e','2025-06-27 09:55:37','2025-06-27 01:55:38',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,89,'park-gate-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 723055983177371648\n      name: 1\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]        ','69540a6ade454204d50c069095411885','2025-07-02 12:40:24','2025-07-02 04:40:25',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,90,'park-gate-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 723055983177371648\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]        ','21c8ca827416f0cfb55c9a1134c1d650','2025-07-02 13:16:38','2025-07-02 05:16:38',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,91,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlOrderAlipay: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','5865584f6cb63780cfd5d28c525725fb','2025-07-04 10:01:40','2025-07-04 02:01:40',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,92,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/paycallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n','ec3ccec0315a54662121dd7c624fcb1a','2025-07-04 10:02:26','2025-07-04 02:02:27',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,93,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://6c39-116-226-72-227.ngrok-free.app/wx/package/order/payCallback\n    notifyUrlOrderAlipay: https://6c39-116-226-72-227.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','c17ece7b9740d45bab8db451a8bc5884','2025-07-05 15:27:15','2025-07-05 07:27:16',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,94,'park-gate-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 723055983177371648\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]        ','21c8ca827416f0cfb55c9a1134c1d650','2025-07-05 16:48:08','2025-07-05 08:48:09',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,95,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\nsnowflake:\n  worker-id: 2      # 取值范围：0~31\n  datacenter-id: 2   # 取值范围：0~31\n\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://6758-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://6758-39-173-159-200.ngrok-free.app/wx/package/order/payCallback\n    notifyUrlOrderAlipay: https://6758-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','5f17fb2ad5bac034fe722d10136ffc12','2025-07-06 09:30:42','2025-07-06 01:30:42',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,96,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n      - /wx/package/order/payCallback\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n','56d967e3fc47c901dbada7a8dbc1c619','2025-07-06 09:49:00','2025-07-06 01:49:00',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,97,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/order/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','56710eaf69b0e5d0efca30a72dadd263','2025-07-06 09:55:55','2025-07-06 01:55:56',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,98,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','76c9a6b73cc7cdfb77199d1238d7a266','2025-07-06 15:18:56','2025-07-06 07:18:57',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,99,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxf5aea578b4a18ffb\n  secret: e3a6258c6e4beed78226e603bcb9c2da\n  grantType: authorization_code\n','d61c247c1eda80edbb90b6ee563ca20d','2025-07-06 15:25:17','2025-07-06 07:25:17',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,100,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxf5a8941d0d5617c1\n  secret: 0ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n','17273a3ac50b8d741e105964b82fdd6a','2025-07-07 10:09:43','2025-07-07 02:09:44',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,101,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: xf5a8941d0d5617c1\n    secret: 0ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','8d78ca766247979acb7f22bf1eaa0a4c','2025-07-08 10:21:30','2025-07-08 02:21:30',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,102,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://875c-39-173-159-200.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','76c9a6b73cc7cdfb77199d1238d7a266','2025-07-08 10:57:30','2025-07-08 02:57:30',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,103,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    norifyUrlVip: https://01a2c6b0249d.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','c91d58185273519e0cfcf2dbd20f3f7d','2025-07-08 11:15:58','2025-07-08 03:15:58',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(6,104,'park-gen-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: *****************************************************************************************************************************************************    username: root\n    password: 1234\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'代码生成接口文档\'\n    # 描述\n    description: \'代码生成接口描述\'\n    # 作者信息\n    contact:\n      name: zzz\n      url: https://ruoyi.vip\n\n# 代码生成\ngen:\n  # 作者\n  author: zzz\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.lgjy.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n  # 是否允许生成文件覆盖到本地（自定义路径），默认不允许\n  allowOverwrite: false','185f676be26dd2ddcf190d4240b0b10e','2025-07-10 11:46:35','2025-07-10 03:46:36',NULL,'0:0:0:0:0:0:0:1','D','','','formal',NULL,NULL),(2,105,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: park-gen\n          uri: lb://park-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /*/v3/api-docs\n      - /csrf\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n      - /wx/package/payCallback\n\n# springdoc配置\nspringdoc:\n  webjars:\n    # 访问前缀\n    prefix:\n','098cff6611f8b436ac22e499b6710813','2025-07-10 11:56:53','2025-07-10 03:56:54',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(23,106,'brand-mapping.yml','DEFAULT_GROUP','','brand:\n  # 品牌映射配置：停车场ID -> 品牌编码\n  mapping:\n    # 思卓品牌停车场\n    \"728424271419936768\": \"sizhuo\"\n    # 未来可以添加其他品牌停车场\n    # \"另一个停车场ID\": \"jieshun\"\n    # \"第三个停车场ID\": \"brand3\"\n  \n  # 品牌服务配置：品牌编码 -> 服务地址\n  services:\n    sizhuo: \"http://localhost:9201\"\n    # 未来可以添加其他品牌服务\n    # jieshun: \"http://localhost:9202\"\n    # brand3: \"http://localhost:9203\"','deaead81051bd650bab1e78ea75c7eac','2025-07-10 17:47:54','2025-07-10 09:47:54',NULL,'0:0:0:0:0:0:0:1','D','','','formal',NULL,NULL),(22,107,'sizhuo-brand-config.yml','DEFAULT_GROUP','','sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 728424271419936768\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]\n    # 可以在这里添加更多思卓品牌的停车场\n    # - physicalId: 另一个物理ID\n    #   logicalId: 另一个逻辑ID\n    #   name: 另一个停车场名称\n    #   channelRules:\n    #     entryDisabledChannels: []\n    #     exitDisabledChannels: []','a8f327aac2995b64244e283787249719','2025-07-10 17:48:02','2025-07-10 09:48:03',NULL,'0:0:0:0:0:0:0:1','D','','','formal',NULL,NULL),(21,108,'park-gate-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug','dd58f58e5217964178b23a4c24531898','2025-07-10 17:48:13','2025-07-10 09:48:13',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(0,109,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\r\n  brandId: sizhuo\r\n  brandName: 思卓\r\n  requestUrl: http://qr.it-wy.cn:83\r\n  # 停车场区域配置\r\n  areas:\r\n    - physicalId: \"SZ001\"          \r\n      logicalId: \"1\"               \r\n      name: \"江山路\"\r\n      channelRules:\r\n        entryDisabledChannels: []  \r\n        exitDisabledChannels: []   \r\n','723dc2083b2a6d930e013809fd41e4b9','2025-07-11 10:32:40','2025-07-11 02:32:41',NULL,'0:0:0:0:0:0:0:1','I','','','formal',NULL,NULL),(24,110,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\r\n  brandId: sizhuo\r\n  brandName: 思卓\r\n  requestUrl: http://qr.it-wy.cn:83\r\n  # 停车场区域配置\r\n  areas:\r\n    - physicalId: \"SZ001\"          \r\n      logicalId: \"1\"               \r\n      name: \"江山路\"\r\n      channelRules:\r\n        entryDisabledChannels: []  \r\n        exitDisabledChannels: []   \r\n','723dc2083b2a6d930e013809fd41e4b9','2025-07-11 10:34:07','2025-07-11 02:34:08',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(0,111,'Igjy-payment-dev.yml','DEFAULT_GROUP','','# 支付服务配置文件 (开发环境)\r\n# 支付服务负责统一的支付、退款、查询等功能\r\n\r\nspring:\r\n  # 数据源配置\r\n  datasource:\r\n    # Druid数据库连接池配置\r\n    druid:\r\n      # Druid监控页面配置\r\n      stat-view-servlet:\r\n        # 启用Druid监控页面\r\n        # 可通过 http://ip:port/druid 访问监控界面\r\n        enabled: true\r\n        # 监控页面登录用户名\r\n        loginUsername: lgjy\r\n        # 监控页面登录密码\r\n        loginPassword: 123456\r\n    \r\n    # 动态数据源配置 - 支持主从读写分离\r\n    dynamic:\r\n      # Druid连接池全局配置\r\n      druid:\r\n        # 初始化连接数：启动时创建5个连接\r\n        initial-size: 5\r\n        # 最小空闲连接数：连接池中保持的最少连接数\r\n        min-idle: 5\r\n        # 最大活跃连接数：连接池最多同时使用20个连接\r\n        maxActive: 20\r\n        # 获取连接最大等待时间：60秒\r\n        maxWait: 60000\r\n        # 连接超时时间：30秒\r\n        connectTimeout: 30000\r\n        # Socket读取超时时间：60秒\r\n        socketTimeout: 60000\r\n        # 连接回收检测间隔：60秒检测一次空闲连接\r\n        timeBetweenEvictionRunsMillis: 60000\r\n        # 连接最小空闲时间：5分钟未使用的连接将被回收\r\n        minEvictableIdleTimeMillis: 300000\r\n        # 连接有效性检测SQL语句\r\n        validationQuery: SELECT 1 FROM DUAL\r\n        # 空闲时检测连接有效性\r\n        testWhileIdle: true\r\n        # 获取连接时不检测（提高性能）\r\n        testOnBorrow: false\r\n        # 归还连接时不检测（提高性能）\r\n        testOnReturn: false\r\n        # 启用预编译语句池\r\n        poolPreparedStatements: true\r\n        # 每个连接最多缓存20个预编译语句\r\n        maxPoolPreparedStatementPerConnectionSize: 20\r\n        # 启用统计和日志过滤器\r\n        filters: stat,slf4j\r\n        # 连接属性：启用SQL合并统计，慢SQL阈值5秒\r\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\r\n      \r\n      # 数据源配置\r\n      datasource:\r\n          # 主数据库配置 - 用于写操作和主要读操作\r\n          master:\r\n            # MySQL 8.0+ 驱动\r\n            driver-class-name: com.mysql.cj.jdbc.Driver\r\n            # 数据库连接URL\r\n            url: ************************************************************************************************************************************************\r\n            # 数据库用户名\r\n            username: root\r\n            # 数据库密码\r\n            password: 1234\r\n\r\n# MyBatis ORM框架配置\r\nmybatis:\r\n    # 实体类包扫描路径\r\n    typeAliasesPackage: com.lgjy.payment.domain\r\n    # Mapper XML文件扫描路径\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n\r\n# 银联支付配置\r\nunion-pay-api:\r\n  sign: 37Y1\r\n  url: https://api-mop.chinaums.com\r\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\r\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\r\n  timeout: 30\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\r\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\r\n  invoiceMsgSrc: lgjy_payment\r\n  invoiceKey: payment_invoice_key_2025\r\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\r\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n\r\n# 微信小程序配置\r\napplet-api:\r\n  url: https://api.weixin.qq.com\r\n  appid: wxf5a8941d0d5617c1\r\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\r\n  grantType: authorization_code\r\n  timeout: 30\r\n  payUrl: https://api.mch.weixin.qq.com\r\n  mchIdOrder: 1659104869\r\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  mchIdVip: 1659104869\r\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  supportFapiao: false\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund','f7ce0035e606045398e15e690bdb2d5f','2025-07-11 11:05:32','2025-07-11 03:05:32',NULL,'0:0:0:0:0:0:0:1','I','','','formal',NULL,NULL),(25,112,'Igjy-payment-dev.yml','DEFAULT_GROUP','','# 支付服务配置文件 (开发环境)\r\n# 支付服务负责统一的支付、退款、查询等功能\r\n\r\nspring:\r\n  # 数据源配置\r\n  datasource:\r\n    # Druid数据库连接池配置\r\n    druid:\r\n      # Druid监控页面配置\r\n      stat-view-servlet:\r\n        # 启用Druid监控页面\r\n        # 可通过 http://ip:port/druid 访问监控界面\r\n        enabled: true\r\n        # 监控页面登录用户名\r\n        loginUsername: lgjy\r\n        # 监控页面登录密码\r\n        loginPassword: 123456\r\n    \r\n    # 动态数据源配置 - 支持主从读写分离\r\n    dynamic:\r\n      # Druid连接池全局配置\r\n      druid:\r\n        # 初始化连接数：启动时创建5个连接\r\n        initial-size: 5\r\n        # 最小空闲连接数：连接池中保持的最少连接数\r\n        min-idle: 5\r\n        # 最大活跃连接数：连接池最多同时使用20个连接\r\n        maxActive: 20\r\n        # 获取连接最大等待时间：60秒\r\n        maxWait: 60000\r\n        # 连接超时时间：30秒\r\n        connectTimeout: 30000\r\n        # Socket读取超时时间：60秒\r\n        socketTimeout: 60000\r\n        # 连接回收检测间隔：60秒检测一次空闲连接\r\n        timeBetweenEvictionRunsMillis: 60000\r\n        # 连接最小空闲时间：5分钟未使用的连接将被回收\r\n        minEvictableIdleTimeMillis: 300000\r\n        # 连接有效性检测SQL语句\r\n        validationQuery: SELECT 1 FROM DUAL\r\n        # 空闲时检测连接有效性\r\n        testWhileIdle: true\r\n        # 获取连接时不检测（提高性能）\r\n        testOnBorrow: false\r\n        # 归还连接时不检测（提高性能）\r\n        testOnReturn: false\r\n        # 启用预编译语句池\r\n        poolPreparedStatements: true\r\n        # 每个连接最多缓存20个预编译语句\r\n        maxPoolPreparedStatementPerConnectionSize: 20\r\n        # 启用统计和日志过滤器\r\n        filters: stat,slf4j\r\n        # 连接属性：启用SQL合并统计，慢SQL阈值5秒\r\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\r\n      \r\n      # 数据源配置\r\n      datasource:\r\n          # 主数据库配置 - 用于写操作和主要读操作\r\n          master:\r\n            # MySQL 8.0+ 驱动\r\n            driver-class-name: com.mysql.cj.jdbc.Driver\r\n            # 数据库连接URL\r\n            url: ************************************************************************************************************************************************\r\n            # 数据库用户名\r\n            username: root\r\n            # 数据库密码\r\n            password: 1234\r\n\r\n# MyBatis ORM框架配置\r\nmybatis:\r\n    # 实体类包扫描路径\r\n    typeAliasesPackage: com.lgjy.payment.domain\r\n    # Mapper XML文件扫描路径\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n\r\n# 银联支付配置\r\nunion-pay-api:\r\n  sign: 37Y1\r\n  url: https://api-mop.chinaums.com\r\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\r\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\r\n  timeout: 30\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\r\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\r\n  invoiceMsgSrc: lgjy_payment\r\n  invoiceKey: payment_invoice_key_2025\r\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\r\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\r\n\r\n# 微信小程序配置\r\napplet-api:\r\n  url: https://api.weixin.qq.com\r\n  appid: wxf5a8941d0d5617c1\r\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\r\n  grantType: authorization_code\r\n  timeout: 30\r\n  payUrl: https://api.mch.weixin.qq.com\r\n  mchIdOrder: 1659104869\r\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  mchIdVip: 1659104869\r\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\r\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  supportFapiao: false\r\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\r\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund','f7ce0035e606045398e15e690bdb2d5f','2025-07-11 11:11:09','2025-07-11 03:11:09',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(25,113,'Igjy-payment-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund','2b5b492912ba21b11a6b657e7466d9c8','2025-07-11 11:12:24','2025-07-11 03:12:25',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(25,114,'Igjy-payment-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund','2b5b492912ba21b11a6b657e7466d9c8','2025-07-11 11:25:40','2025-07-11 03:25:40',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(25,115,'park-payment-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \"\"\n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: \"************************************************************************************************************************************************\"\n            username: root\n            password: \"1234\"\n          # 从库数据源\n#         slave:\n#           username: \"\"\n#           password: \"\"\n#           url: \"\"\n#           driver-class-name: \"\"\n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund','4ffedc0226aaac9f1ced4c1d7f29de32','2025-07-11 11:50:15','2025-07-11 03:50:15',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(25,116,'park-payment-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \"\"\n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: \"************************************************************************************************************************************************\"\n            username: root\n            password: \"1234\"\n          # 从库数据源\n#         slave:\n#           username: \"\"\n#           password: \"\"\n#           url: \"\"\n#           driver-class-name: \"\"\n\n# MyBatis ORM框架配置\nmybatis:\n    # 实体类包扫描路径\n    typeAliasesPackage: com.lgjy.payment.domain\n    # Mapper XML文件扫描路径\n    mapperLocations: classpath:mapper/**/*.xml\n\n# 银联支付配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  appId: 8a81c1bd89b6cadb018e08cf6b681acb\n  appKey: ba89d775d4c04a5090e10abd927d2dd0\n  timeout: 30\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlVip: https://88ed5dfb9b8c.ngrok-free.app/payment/callback/payment\n  notifyUrlVipGroup: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n  notifyUrlChargeOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  invoiceUrl: https://test-parking.lgfw24hours.com/invoice\n  invoiceMsgSrc: lgjy_payment\n  invoiceKey: payment_invoice_key_2025\n  notifyUrlOrderInvoice: https://test-parking.lgfw24hours.com/payment/callback/invoice\n  notifyUrlOrderAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlMerchantTicketAlipay: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlRefund: https://test-parking.lgfw24hours.com/payment/callback/refund\n\n# 微信小程序配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n  timeout: 30\n  payUrl: https://api.mch.weixin.qq.com\n  mchIdOrder: 1659104869\n  apiKeyOrder: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlOrder: https://test-parking.lgfw24hours.com/payment/callback/payment\n  mchIdVip: 1659104869\n  apiKeyVip: 7c507a8d82344e07bb6b2b565f927827\n  notifyUrlVip: https://test-parking.lgfw24hours.com/payment/callback/payment\n  supportFapiao: false\n  notifyUrlCharge: https://test-parking.lgfw24hours.com/payment/callback/payment\n  notifyUrlChargeRefund: https://test-parking.lgfw24hours.com/payment/callback/refund','4ffedc0226aaac9f1ced4c1d7f29de32','2025-07-11 12:34:22','2025-07-11 04:34:22',NULL,'0:0:0:0:0:0:0:1','D','','','formal',NULL,NULL),(11,117,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://01a2c6b0249d.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','1c59bc9fa36e4687a901f465819b010b','2025-07-11 12:45:31','2025-07-11 04:45:32',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,118,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://01a2c6b0249d.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','8d71caf49615cd340bafd4d8fa7056de','2025-07-11 12:48:00','2025-07-11 04:48:00',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,119,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','62ac86adf1d66c6e4e66f648a95c74f3','2025-07-11 12:55:00','2025-07-11 04:55:01',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,120,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','c9c71b2fde5689d30ec70ca4726abad6','2025-07-11 13:37:54','2025-07-11 05:37:55',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,121,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','c0ae425785887e3d55e6db0fe282722c','2025-07-11 13:38:23','2025-07-11 05:38:23',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,122,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: 8a81c1bd89b6cadb018e08cf6b681acb\n    secret: ba89d775d4c04a5090e10abd927d2dd0\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','913c7edc90b26f60700c7948c33c9182','2025-07-11 13:41:10','2025-07-11 05:41:10',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,123,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxf5a8941d0d5617c1\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','8f704b6a83cd4b039c72308ab915d32e','2025-07-11 13:44:17','2025-07-11 05:44:18',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,124,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','6d3d590f1486bc3bd1f9a3d1b59ba06b','2025-07-11 13:46:42','2025-07-11 05:46:42',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,125,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxf5a8941d0d5617c1\n  secret: 20ad32a6ab6bbdda27c1631676d9c534\n  grantType: authorization_code\n','36b71596458ad7be7a45c2c431420800','2025-07-11 13:48:36','2025-07-11 05:48:36',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(1,126,'application-dev.yml','DEFAULT_GROUP','','spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true','b20a6949af246cfacbc14191e0377ea0','2025-07-11 15:35:26','2025-07-11 07:35:27',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(24,127,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  # 停车场区域配置\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n','8ba1ca8e463547a0590dcc193fe27c91','2025-07-11 15:41:13','2025-07-11 07:41:14',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,128,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /gate/sizhuo/pull\n      - /wx/warehouse/list\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n      - /wx/package/payCallback','4e6f196da6900e54ae745e86ef2e05d9','2025-07-11 15:43:28','2025-07-11 07:43:28',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,129,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n','9d9ff4e99df68d08462a4d9596dc96ae','2025-07-11 15:44:35','2025-07-11 07:44:35',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,130,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip','3424f8998e6fde133f8be1d88867e706','2025-07-11 15:53:28','2025-07-11 07:53:29',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,131,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.wx\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','6d3d590f1486bc3bd1f9a3d1b59ba06b','2025-07-11 15:55:26','2025-07-11 07:55:27',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,132,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.system\n    mapperLocations: classpath:mapper/**/*.xml\n\n# springdoc配置\nspringdoc:\n  gatewayUrl: http://localhost:8080/${spring.application.name}\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  info:\n    # 标题\n    title: \'系统模块接口文档\'\n    # 描述\n    description: \'系统模块接口描述\'\n    # 作者信息\n    contact:\n      name: RuoYi\n      url: https://ruoyi.vip','1c9dd8e39372d9ee1273ee4df8e9b870','2025-07-11 15:56:13','2025-07-11 07:56:13',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,133,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','855d89040aff64b0db48d686416163d5','2025-07-11 15:56:41','2025-07-11 07:56:41',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,134,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.system\n    mapperLocations: classpath:mapper/**/*.xml','9ffa7e2cba10c9f2326301c773922ece','2025-07-11 15:57:52','2025-07-11 07:57:52',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,135,'park-gate-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: ruoyi\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        connectTimeout: 30000\n        socketTimeout: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: **************************************************************************************************************************************************            username: root\n            password: 1234\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 728424271419936768\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]','e21eef30bb36e97c596046a3e47c50f5','2025-07-11 16:02:32','2025-07-11 08:02:33',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,136,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234 \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 20ad32a6ab6bbdda27c1631676d9c534\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','776594d4818f598cf6922e5ca9283d78','2025-07-11 16:05:33','2025-07-11 08:05:34',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,137,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-11 16:08:17','2025-07-11 08:08:18',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,138,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234 \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n    timeout: 5\n    payUrl: https://api.mch.weixin.qq.com\n    mchId: 1659104869\n    supportFapiao: false\n    notifyUrlMerChant: https://test-parking.lgfw24hours.com/bigdata/merchant/ticket/payCallback\n    apiKey: 7c507a8d82344e07bb6b2b565f927827\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    mid: 89831014131000M\n    tid: 000M0001\n    subAppId: \n    subMerchantId: 631274231\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','c77037ccd806f29ef37d4dc3d72fe709','2025-07-11 16:11:38','2025-07-11 08:11:39',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,139,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234 \n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: 8a81c1bd89b6cadb018e08cf6b681acb\n    appKey: ba89d775d4c04a5090e10abd927d2dd0\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n','e66774871b3eb0fb1b50fdd023925d89','2025-07-11 16:11:53','2025-07-11 08:11:53',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,140,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n      datasource:\n        # 主库数据源\n        master:\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: **************************************************************************************************************************************************          username: root\n          password: 1234\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug\n\nsizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  \n  # 停车场列表\n  areas:\n    - physicalId: 20240926133058945\n      logicalId: 728424271419936768\n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]','02f7d3ba2420f7e017b3f73591ae0c3d','2025-07-11 16:13:19','2025-07-11 08:13:19',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(1,141,'application-dev.yml','DEFAULT_GROUP','','spring:\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true','b20a6949af246cfacbc14191e0377ea0','2025-07-11 16:22:29','2025-07-11 08:22:29',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,142,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n','e6f0cead56e94162e99231d0c3d18d79','2025-07-12 08:00:08','2025-07-12 00:00:09',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(3,143,'park-auth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n','756a03974b815ff3febec9744c633cf4','2025-07-12 08:00:14','2025-07-12 00:00:14',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,144,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','44d73acde87f75500940a819e0188906','2025-07-12 08:00:19','2025-07-12 00:00:19',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(8,145,'park-file-dev.yml','DEFAULT_GROUP','','# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）','95bb71a3e1301faa5d04c1e6685936cb','2025-07-12 08:00:23','2025-07-12 00:00:24',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,146,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-12 08:00:29','2025-07-12 00:00:29',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,147,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','3c5ed4b23aedeea41aec7d2162e34ffa','2025-07-12 08:00:33','2025-07-12 00:00:33',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,148,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','3c5ed4b23aedeea41aec7d2162e34ffa','2025-07-12 08:00:37','2025-07-12 00:00:37',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,149,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug','78c1484713099b5d9892bda7f1315e32','2025-07-12 08:00:41','2025-07-12 00:00:41',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(24,150,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n','84fa2dbf0322c86010606bdddf8580ac','2025-07-12 08:00:45','2025-07-12 00:00:45',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,151,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy.gate: debug','b8d5ad505f58a7be19f62311e1ef4573','2025-07-12 08:04:51','2025-07-12 00:04:51',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,152,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-12 08:08:55','2025-07-12 00:08:56',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,153,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-12 08:09:02','2025-07-12 00:09:03',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,154,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# 将来可能的通用配置预留位置','305a7a3474bb1c67df959796b351ceab','2025-07-12 10:29:10','2025-07-12 02:29:11',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,155,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n      transport:\n        port: 8719\n        dashboard: localhost:8080\n\n# 将来可能的通用配置预留位置','4671bb9540b7c151fc3b21042a7028c8','2025-07-12 10:38:39','2025-07-12 02:38:39',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,156,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n','e6f0cead56e94162e99231d0c3d18d79','2025-07-12 11:35:06','2025-07-12 03:35:06',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(3,157,'park-auth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n','756a03974b815ff3febec9744c633cf4','2025-07-12 11:35:10','2025-07-12 03:35:10',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,158,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-12 11:35:16','2025-07-12 03:35:16',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(8,159,'park-file-dev.yml','DEFAULT_GROUP','','# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）','95bb71a3e1301faa5d04c1e6685936cb','2025-07-12 11:35:19','2025-07-12 03:35:20',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,160,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-12 11:35:23','2025-07-12 03:35:24',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,161,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-12 11:35:29','2025-07-12 03:35:30',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,162,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 微信小程序API配置\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code','3c17f0ec88455cf95ceddbf2608eda2b','2025-07-12 11:35:33','2025-07-12 03:35:34',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,163,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true','e5c4a07fc4ddfc06f70c489f3cda79bc','2025-07-12 11:35:42','2025-07-12 03:35:42',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(24,164,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n','84fa2dbf0322c86010606bdddf8580ac','2025-07-12 11:35:45','2025-07-12 03:35:45',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,165,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n\n# 银联支付配置\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 将来可能的通用配置预留位置','99b7a30af39c701cfd4f48a9f891237e','2025-07-12 11:35:49','2025-07-12 03:35:50',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,166,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n\n# 银联支付配置\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 将来可能的通用配置预留位置','99b7a30af39c701cfd4f48a9f891237e','2025-07-12 11:35:59','2025-07-12 03:36:00',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,167,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\n# 微信小程序API配置\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code','3c17f0ec88455cf95ceddbf2608eda2b','2025-07-12 11:36:44','2025-07-12 03:36:45',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,168,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-12 11:37:14','2025-07-12 03:37:15',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(2,169,'park-gateway-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut\n','e6f0cead56e94162e99231d0c3d18d79','2025-07-13 10:32:11','2025-07-13 10:32:12',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(3,170,'park-auth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n','756a03974b815ff3febec9744c633cf4','2025-07-13 10:32:18','2025-07-13 10:32:19',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,171,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-13 10:32:26','2025-07-13 10:32:27',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(8,172,'park-file-dev.yml','DEFAULT_GROUP','','# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）','95bb71a3e1301faa5d04c1e6685936cb','2025-07-13 10:32:35','2025-07-13 10:32:35',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,173,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-13 10:32:42','2025-07-13 10:32:43',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,174,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-13 10:32:49','2025-07-13 10:32:50',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,175,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-13 10:32:56','2025-07-13 10:32:56',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,176,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true','e5c4a07fc4ddfc06f70c489f3cda79bc','2025-07-13 10:33:04','2025-07-13 10:33:04',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(24,177,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n','84fa2dbf0322c86010606bdddf8580ac','2025-07-13 10:33:14','2025-07-13 10:33:15',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,178,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false','9b888a075373e2b0decd67ff85fa7ee7','2025-07-13 10:33:21','2025-07-13 10:33:22',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,179,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-13 10:42:56','2025-07-13 10:42:57',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,180,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false','9b888a075373e2b0decd67ff85fa7ee7','2025-07-13 11:59:21','2025-07-13 11:59:22',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,181,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-13 11:59:47','2025-07-13 11:59:48',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,182,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false','9b888a075373e2b0decd67ff85fa7ee7','2025-07-13 13:37:14','2025-07-13 13:37:15',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,183,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-13 13:37:42','2025-07-13 13:37:43',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(3,184,'park-auth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2\n','756a03974b815ff3febec9744c633cf4','2025-07-13 13:38:03','2025-07-13 13:38:03',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,185,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true','e5c4a07fc4ddfc06f70c489f3cda79bc','2025-07-13 13:38:20','2025-07-13 13:38:20',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,186,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-13 13:51:11','2025-07-13 13:51:11',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(26,187,'common-dev.yml','DEFAULT_GROUP','','# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false','9b888a075373e2b0decd67ff85fa7ee7','2025-07-13 14:00:47','2025-07-13 14:00:47',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(24,188,'sizhuo-config.yml','DEFAULT_GROUP','','sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: [\"3\"]  \n        exitDisabledChannels: [\"4\"]   \n','84fa2dbf0322c86010606bdddf8580ac','2025-07-13 14:01:24','2025-07-13 14:01:24',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(21,189,'park-gate-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true','e5c4a07fc4ddfc06f70c489f3cda79bc','2025-07-13 14:01:56','2025-07-13 14:01:56',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,190,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-13 14:11:00','2025-07-13 14:11:01',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,191,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n','a85b912b55c71764159f7f1487c59c5a','2025-07-13 14:12:41','2025-07-13 14:12:41',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(5,192,'park-system-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml','1efaabbb1af2a55d8484c9232efec24a','2025-07-13 14:13:29','2025-07-13 14:13:30',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,193,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code','a7608ead261bb376b3f4ec38ec052b43','2025-07-13 16:04:35','2025-07-13 16:04:36',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,194,'park-wxauth-dev.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    \n# 短信验证\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【临港捷运】\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\n\n# 小程序微信API\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code','a7608ead261bb376b3f4ec38ec052b43','2025-07-13 16:08:04','2025-07-13 16:08:05',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(11,195,'park-wx-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-16 01:57:31','2025-07-16 01:57:31',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(10,196,'park-wxauth-dev.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback','ce47cb40aef5c3ae75f81706b75f06d2','2025-07-16 01:57:50','2025-07-16 01:57:51',NULL,'0:0:0:0:0:0:0:1','U','','','formal',NULL,NULL),(0,197,'park-gateway-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n    timeout: 10s\r\n    lettuce:\r\n      pool:\r\n        max-active: 200\r\n        max-wait: -1ms\r\n        max-idle: 10\r\n        min-idle: 0\r\n  cloud:\r\n    gateway:\r\n      discovery:\r\n        locator:\r\n          lowerCaseServiceId: true\r\n          enabled: true\r\n      routes:\r\n\r\n        # 认证中心\r\n        - id: park-auth\r\n          uri: lb://park-auth\r\n          predicates:\r\n            - Path=/auth/**\r\n          filters:\r\n            # 验证码处理\r\n            - CacheRequestBody\r\n            - ValidateCodeFilter\r\n            - StripPrefix=1\r\n        # 系统模块\r\n        - id: park-system\r\n          uri: lb://park-system\r\n          predicates:\r\n            - Path=/system/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 文件服务\r\n        - id: park-file\r\n          uri: lb://park-file\r\n          predicates:\r\n            - Path=/file/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 微信小程序认证中心\r\n        - id: park-wxauth\r\n          uri: lb://park-wxauth\r\n          predicates:\r\n            - Path=/wxauth/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 微信小程序\r\n        - id: park-wx\r\n          uri: lb://park-wx\r\n          predicates:\r\n            - Path=/wx/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 道闸\r\n        - id: park-gate\r\n          uri: lb://park-gate\r\n          predicates:\r\n            - Path=/gate/**\r\n          filters:\r\n            - StripPrefix=1\r\n\r\n# 管理端点配置\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,routes,gateway\r\n  endpoint:\r\n    health:\r\n      show-details: always\r\n\r\n# 安全配置\r\nsecurity:\r\n  # 验证码\r\n  captcha:\r\n    enabled: true\r\n    type: math\r\n  # 防止XSS攻击\r\n  xss:\r\n    enabled: true\r\n    excludeUrls:\r\n      - /system/notice\r\n\r\n  ignore:\r\n    whites:\r\n      - /auth/logout\r\n      - /auth/login\r\n      - /code\r\n      - /wxauth/code\r\n      - /wxauth/login/code\r\n      - /wxauth/login/wx\r\n      - /wx/advertConfig/list\r\n      - /wx/parking/order/payCallback\r\n      - /wx/parking/order/channelPayQuery\r\n      - /wx/warehouse/list\r\n      - /wx/package/payCallback\r\n      - /gate/sizhuo/pull\r\n      - /gate/sizhuo/push/CarIn\r\n      - /gate/sizhuo/push/CarOut\r\n','a82319e43bd98c8271aac455e6960604','2025-07-20 21:18:44','2025-07-20 21:18:45',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,198,'park-auth-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n','9d81c665a73ac2d3e55d3c916f5dd4f2','2025-07-20 21:21:52','2025-07-20 21:21:52',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,199,'park-system-test.yml','DEFAULT_GROUP','','# spring配置\r\nspring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n  datasource:\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: ************************************************************************************************************************************************    username: lgjy_admin\r\n    password: LgJy@Park#2024!\r\n    druid:\r\n      initial-size: 5\r\n      min-idle: 5\r\n      max-active: 20\r\n      max-wait: 60000\r\n      validation-query: SELECT 1 FROM DUAL\r\n      test-while-idle: true\r\n\r\n# mybatis配置\r\nmybatis:\r\n    # 搜索指定包别名\r\n    typeAliasesPackage: com.lgjy.system\r\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n','ad70ec175a130b0c2dea882be68f6c07','2025-07-20 21:22:44','2025-07-20 21:22:45',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,200,'park-file-test.yml','DEFAULT_GROUP','','# 本地文件上传    \r\nfile:\r\n    domain: http://127.0.0.1:9300\r\n    path: D:/lgjy/uploadPath\r\n    prefix: /statics\r\n\r\n# FastDFS配置\r\nfdfs:\r\n  domain: http://************\r\n  soTimeout: 3000\r\n  connectTimeout: 2000\r\n  trackerList: ************:22122\r\n\r\n# Minio配置\r\nminio:\r\n  url: http://************:9000\r\n  accessKey: minioadmin\r\n  secretKey: minioadmin\r\n  bucketName: test\r\n\r\nspring:\r\n  servlet:\r\n    multipart:\r\n      max-file-size: 10MB       # 单个文件最大10MB\r\n      max-request-size: 20MB    # 整个请求最大20MB\r\n      enabled: true             # 启用文件上传功能\r\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）\r\n','de782cb1c08d02bb8a7a50f10a9909a5','2025-07-20 21:26:38','2025-07-20 21:26:38',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,201,'park-wxauth-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n    \r\n# 短信验证\r\nmsg-server:\r\n  url: http://sms-api.luosimao.com\r\n  api: api\r\n  api-key: ************************************\r\n  sign: 【临港捷运】\r\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\r\n\r\n# 小程序微信API\r\nwx-api:\r\n  # 小程序基础参数\r\n  url: https://api.weixin.qq.com\r\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\r\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\r\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\r\n  appid: wxdcd31ee3e79190cc\r\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\r\n  grantType: authorization_code\r\n','40993d91a176521ae0e5d42e847c2586','2025-07-20 21:28:13','2025-07-20 21:28:13',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,202,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\r\nspring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n  datasource:\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: ************************************************************************************************************************************************    username: lgjy_admin\r\n    password: LgJy@Park#2024!\r\n    druid:\r\n      initial-size: 5\r\n      min-idle: 5\r\n      max-active: 20\r\n      max-wait: 60000\r\n      validation-query: SELECT 1 FROM DUAL\r\n      test-while-idle: true\r\n\r\n# mybatis配置\r\nmybatis:\r\n    typeAliasesPackage: com.lgjy.wx\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n    configuration:\r\n      mapUnderscoreToCamelCase: true\r\n\r\napplet-api:\r\n    url: https://api.weixin.qq.com\r\n    appid: wxdcd31ee3e79190cc\r\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\r\n    grantType: authorization_code\r\n\r\n# 银联API\r\nunion-pay-api:\r\n    sign: 37Y1\r\n    url: https://api-mop.chinaums.com\r\n    appId: wxdcd31ee3e79190cc\r\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\r\n    notifyUrlOrder: https://test-parking.example.com/wx/parking/order/payCallback\r\n    notifyUrlVip: https://test-parking.example.com/wx/package/payCallback\r\n    notifyUrlOrderAlipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\r\n    timeout: 5\r\n    # 发票\r\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\r\n    invoiceMsgSrc: LINGANG_JT\r\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\r\n    # 发票回调\r\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\r\n','dc474a80a4421374070568c14c3183bb','2025-07-20 21:29:14','2025-07-20 21:29:14',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,203,'park-gate-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n  datasource:\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: ************************************************************************************************************************************************    username: lgjy_admin\r\n    password: LgJy@Park#2024!\r\n    druid:\r\n      initial-size: 5\r\n      min-idle: 5\r\n      max-active: 20\r\n      max-wait: 60000\r\n      validation-query: SELECT 1 FROM DUAL\r\n      test-while-idle: true\r\n\r\n# mybatis配置\r\nmybatis:\r\n    # 搜索指定包别名\r\n    typeAliasesPackage: com.lgjy.gate\r\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n    # 开启驼峰命名自动映射\r\n    configuration:\r\n      mapUnderscoreToCamelCase: true\r\n','79e126290e2363739820bd786532564f','2025-07-20 21:30:01','2025-07-20 21:30:02',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,204,'common-test.yml','DEFAULT_GROUP','','\r\n# 日志配置\r\nlogging:\r\n  level:\r\n    com.lgjy: INFO\r\n  file:\r\n    name: logs/${spring.application.name}/app.log\r\n    max-size: 100MB\r\n    max-history: 30\r\n\r\n# Sentinel配置\r\nspring:\r\n  cloud:\r\n    sentinel:\r\n      log:\r\n        dir: logs/sentinel\r\n        switch-pid: false\r\n','58a2fe8edfaf409a24df6260b73b76d9','2025-07-20 21:30:56','2025-07-20 21:30:57',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(62,205,'common-test.yml','DEFAULT_GROUP','','\r\n# 日志配置\r\nlogging:\r\n  level:\r\n    com.lgjy: INFO\r\n  file:\r\n    name: logs/${spring.application.name}/app.log\r\n    max-size: 100MB\r\n    max-history: 30\r\n\r\n# Sentinel配置\r\nspring:\r\n  cloud:\r\n    sentinel:\r\n      log:\r\n        dir: logs/sentinel\r\n        switch-pid: false\r\n','58a2fe8edfaf409a24df6260b73b76d9','2025-07-20 21:31:15','2025-07-20 21:31:15',NULL,'0:0:0:0:0:0:0:1','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,206,'sizhuo-config-test.yml','DEFAULT_GROUP','','sizhuo:\r\n  brandId: sizhuo\r\n  brandName: 思卓\r\n  requestUrl: http://qr.it-wy.cn:83\r\n  areas:\r\n    - physicalId: 20240926133058945          \r\n      logicalId: 728424271419936768               \r\n      name: 江山路测试\r\n      channelRules:\r\n        entryDisabledChannels: [\"3\"]  \r\n        exitDisabledChannels: [\"4\"]\r\n','f0344d688316f6aacb0206b53bb7e799','2025-07-20 21:32:33','2025-07-20 21:32:34',NULL,'0:0:0:0:0:0:0:1','I','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(58,207,'park-file-test.yml','DEFAULT_GROUP','','# 本地文件上传    \r\nfile:\r\n    domain: http://127.0.0.1:9300\r\n    path: D:/lgjy/uploadPath\r\n    prefix: /statics\r\n\r\n# FastDFS配置\r\nfdfs:\r\n  domain: http://************\r\n  soTimeout: 3000\r\n  connectTimeout: 2000\r\n  trackerList: ************:22122\r\n\r\n# Minio配置\r\nminio:\r\n  url: http://************:9000\r\n  accessKey: minioadmin\r\n  secretKey: minioadmin\r\n  bucketName: test\r\n\r\nspring:\r\n  servlet:\r\n    multipart:\r\n      max-file-size: 10MB       # 单个文件最大10MB\r\n      max-request-size: 20MB    # 整个请求最大20MB\r\n      enabled: true             # 启用文件上传功能\r\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）\r\n','de782cb1c08d02bb8a7a50f10a9909a5','2025-07-20 21:36:42','2025-07-20 21:36:42',NULL,'0:0:0:0:0:0:0:1','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(55,208,'park-gateway-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n    timeout: 10s\r\n    lettuce:\r\n      pool:\r\n        max-active: 200\r\n        max-wait: -1ms\r\n        max-idle: 10\r\n        min-idle: 0\r\n  cloud:\r\n    gateway:\r\n      discovery:\r\n        locator:\r\n          lowerCaseServiceId: true\r\n          enabled: true\r\n      routes:\r\n\r\n        # 认证中心\r\n        - id: park-auth\r\n          uri: lb://park-auth\r\n          predicates:\r\n            - Path=/auth/**\r\n          filters:\r\n            # 验证码处理\r\n            - CacheRequestBody\r\n            - ValidateCodeFilter\r\n            - StripPrefix=1\r\n        # 系统模块\r\n        - id: park-system\r\n          uri: lb://park-system\r\n          predicates:\r\n            - Path=/system/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 文件服务\r\n        - id: park-file\r\n          uri: lb://park-file\r\n          predicates:\r\n            - Path=/file/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 微信小程序认证中心\r\n        - id: park-wxauth\r\n          uri: lb://park-wxauth\r\n          predicates:\r\n            - Path=/wxauth/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 微信小程序\r\n        - id: park-wx\r\n          uri: lb://park-wx\r\n          predicates:\r\n            - Path=/wx/**\r\n          filters:\r\n            - StripPrefix=1\r\n        # 道闸\r\n        - id: park-gate\r\n          uri: lb://park-gate\r\n          predicates:\r\n            - Path=/gate/**\r\n          filters:\r\n            - StripPrefix=1\r\n\r\n# 管理端点配置\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,routes,gateway\r\n  endpoint:\r\n    health:\r\n      show-details: always\r\n\r\n# 安全配置\r\nsecurity:\r\n  # 验证码\r\n  captcha:\r\n    enabled: true\r\n    type: math\r\n  # 防止XSS攻击\r\n  xss:\r\n    enabled: true\r\n    excludeUrls:\r\n      - /system/notice\r\n\r\n  ignore:\r\n    whites:\r\n      - /auth/logout\r\n      - /auth/login\r\n      - /code\r\n      - /wxauth/code\r\n      - /wxauth/login/code\r\n      - /wxauth/login/wx\r\n      - /wx/advertConfig/list\r\n      - /wx/parking/order/payCallback\r\n      - /wx/parking/order/channelPayQuery\r\n      - /wx/warehouse/list\r\n      - /wx/package/payCallback\r\n      - /gate/sizhuo/pull\r\n      - /gate/sizhuo/push/CarIn\r\n      - /gate/sizhuo/push/CarOut\r\n','a82319e43bd98c8271aac455e6960604','2025-07-23 14:08:45','2025-07-23 14:08:45','nacos','*************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(56,209,'park-auth-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','a6aa3744d62e64221b4244f6eb0d9996','2025-07-23 15:00:14','2025-07-23 15:00:14','nacos','***************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(57,210,'park-system-test.yml','DEFAULT_GROUP','','# spring配置\r\nspring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n  datasource:\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: ************************************************************************************************************************************************    username: lgjy_admin\r\n    password: LgJy@Park#2024!\r\n    druid:\r\n      initial-size: 5\r\n      min-idle: 5\r\n      max-active: 20\r\n      max-wait: 60000\r\n      validation-query: SELECT 1 FROM DUAL\r\n      test-while-idle: true\r\n\r\n# mybatis配置\r\nmybatis:\r\n    # 搜索指定包别名\r\n    typeAliasesPackage: com.lgjy.system\r\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','b5b6df542bfb91ec0f5aa3e5aaf772ee','2025-07-23 15:00:53','2025-07-23 15:00:53','nacos','***************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(59,211,'park-wxauth-test.yml','DEFAULT_GROUP','','spring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n    \r\n# 短信验证\r\nmsg-server:\r\n  url: http://sms-api.luosimao.com\r\n  api: api\r\n  api-key: ************************************\r\n  sign: 【临港捷运】\r\n  template: \"您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。\"\r\n\r\n# 小程序微信API\r\nwx-api:\r\n  # 小程序基础参数\r\n  url: https://api.weixin.qq.com\r\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\r\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\r\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\r\n  appid: wxdcd31ee3e79190cc\r\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\r\n  grantType: authorization_code\r\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','6b8befd81d27e69848a851e2d816ade4','2025-07-23 15:01:44','2025-07-23 15:01:44','nacos','***************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(60,212,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\r\nspring:\r\n  redis:\r\n    host: park-redis\r\n    port: 6379\r\n    password: LgJy@Redis#2024!\r\n    database: 2\r\n  datasource:\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: ************************************************************************************************************************************************    username: lgjy_admin\r\n    password: LgJy@Park#2024!\r\n    druid:\r\n      initial-size: 5\r\n      min-idle: 5\r\n      max-active: 20\r\n      max-wait: 60000\r\n      validation-query: SELECT 1 FROM DUAL\r\n      test-while-idle: true\r\n\r\n# mybatis配置\r\nmybatis:\r\n    typeAliasesPackage: com.lgjy.wx\r\n    mapperLocations: classpath:mapper/**/*.xml\r\n    configuration:\r\n      mapUnderscoreToCamelCase: true\r\n\r\napplet-api:\r\n    url: https://api.weixin.qq.com\r\n    appid: wxdcd31ee3e79190cc\r\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\r\n    grantType: authorization_code\r\n\r\n# 银联API\r\nunion-pay-api:\r\n    sign: 37Y1\r\n    url: https://api-mop.chinaums.com\r\n    appId: wxdcd31ee3e79190cc\r\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\r\n    notifyUrlOrder: https://test-parking.example.com/wx/parking/order/payCallback\r\n    notifyUrlVip: https://test-parking.example.com/wx/package/payCallback\r\n    notifyUrlOrderAlipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\r\n    timeout: 5\r\n    # 发票\r\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\r\n    invoiceMsgSrc: LINGANG_JT\r\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\r\n    # 发票回调\r\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\r\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','5891b52d244c6e5f91cfb56ed0b798d8','2025-07-23 15:02:14','2025-07-23 15:02:15','nacos','***************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(60,213,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **********************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://test-parking.example.com/wx/parking/order/payCallback\n    notifyUrlVip: https://test-parking.example.com/wx/package/payCallback\n    notifyUrlOrderAlipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','3ecea6485af1c8cced235e243fbce477','2025-07-23 15:37:24','2025-07-23 15:37:24','nacos','************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(60,214,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://test-parking.example.com/wx/parking/order/payCallback\n    notifyUrlVip: https://test-parking.example.com/wx/package/payCallback\n    notifyUrlOrderAlipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','b271463149f91a626b8b7be0ec4ab009','2025-07-23 15:52:51','2025-07-23 15:52:52','nacos','***************','U','a670e801-046b-4366-85ba-7643de52359f','','formal',NULL,NULL),(0,215,'park-wxauth-test.yml','DEFAULT_GROUP','','# 短信服务配置\nmsg-server:\n  url: ${MSG_SERVER_URL:http://localhost:8080}\n  api: ${MSG_SERVER_API:default_api}\n  api-key: ${MSG_SERVER_API_KEY:default_key}\n  sign: ${MSG_SERVER_SIGN:default_sign}\n  template: ${MSG_SERVER_TEMPLATE:default_template}\n\n# 微信认证相关配置\nwx:\n  app-id: ${WX_APP_ID:default_app_id}\n  app-secret: ${WX_APP_SECRET:default_app_secret}\n','d0d687ed44ea35810f3065510768b6e4','2025-07-24 13:19:59','2025-07-24 13:20:00','nacos','**********','I','','','formal',NULL,NULL),(65,216,'park-wxauth-test.yml','DEFAULT_GROUP','','# 短信服务配置\nmsg-server:\n  url: ${MSG_SERVER_URL:http://localhost:8080}\n  api: ${MSG_SERVER_API:default_api}\n  api-key: ${MSG_SERVER_API_KEY:default_key}\n  sign: ${MSG_SERVER_SIGN:default_sign}\n  template: ${MSG_SERVER_TEMPLATE:default_template}\n\n# 微信认证相关配置\nwx:\n  app-id: ${WX_APP_ID:default_app_id}\n  app-secret: ${WX_APP_SECRET:default_app_secret}\n','d0d687ed44ea35810f3065510768b6e4','2025-07-24 13:28:33','2025-07-24 13:28:33','nacos','**********','D','','','formal',NULL,NULL),(0,217,'park-wxauth-test.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    \n# 短信服务\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【停车】\n  template: \"您的验证码是{0}，请在{1}分钟内输入。\"\n\n# 微信小程序API\nwx-api:\n  # 微信服务器地址\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always\n','8b9ed109168613d5b0e9031fd01331bd','2025-07-24 13:35:19','2025-07-24 13:35:20','nacos','**********','I','','','formal',NULL,NULL),(66,218,'park-wxauth-test.yml','DEFAULT_GROUP','','spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    \n# 短信服务\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【停车】\n  template: \"您的验证码是{0}，请在{1}分钟内输入。\"\n\n# 微信小程序API\nwx-api:\n  # 微信服务器地址\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always\n','8b9ed109168613d5b0e9031fd01331bd','2025-07-24 13:43:25','2025-07-24 13:43:25','nacos','**********','U','','','formal',NULL,NULL),(0,219,'park-file-test.yml','DEFAULT_GROUP','','# 文件服务配置\nfile:\n  domain: http://127.0.0.1:9202\n  path: /app/upload\n  prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://127.0.0.1\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList:\n    - 127.0.0.1:22122\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','8d17e171682aba5f05952d15e82ec6d4','2025-07-24 14:11:26','2025-07-24 14:11:27','nacos','**********','I','','','formal',NULL,NULL),(0,220,'park-file-test.yml','DEFAULT_GROUP','','# 文件服务 - 适配Docker环境\nfile:\n  domain: ${FILE_DOMAIN:http://127.0.0.1:9202}\n  path: ${FILE_PATH:/app/upload}\n  prefix: ${FILE_PREFIX:/statics}\n\n# FastDFS配置\nfdfs:\n  domain: ${FDFS_DOMAIN:http://************}\n  soTimeout: ${FDFS_SO_TIMEOUT:3000}\n  connectTimeout: ${FDFS_CONNECT_TIMEOUT:2000}\n  trackerList: ${FDFS_TRACKER_LIST:************:22122}\n\n# Minio配置\nminio:\n  url: ${MINIO_URL:http://************:9000}\n  accessKey: ${MINIO_ACCESS_KEY:minioadmin}\n  secretKey: ${MINIO_SECRET_KEY:minioadmin}\n  bucketName: ${MINIO_BUCKET_NAME:test}\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: ${MAX_FILE_SIZE:10MB}\n      max-request-size: ${MAX_REQUEST_SIZE:20MB}\n      enabled: true\n      resolve-lazily: false\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','77c024b57e002c204b3433b596e41135','2025-07-24 14:16:17','2025-07-24 14:16:17','nacos','**********','I','','','formal',NULL,NULL),(0,221,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true','bd148870d9f371780b337d2da365dfc6','2025-07-24 14:24:01','2025-07-24 14:24:02','nacos','**********','I','','','formal',NULL,NULL),(70,222,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true','bd148870d9f371780b337d2da365dfc6','2025-07-24 14:27:02','2025-07-24 14:27:03','nacos','**********','U','','','formal',NULL,NULL),(70,223,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true','bd148870d9f371780b337d2da365dfc6','2025-07-24 14:38:52','2025-07-24 14:38:52','nacos','**********','U','','','formal',NULL,NULL),(0,224,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT+8&allowPublicKeyRetrieval=true\n    username: ${DB_USERNAME:admin}\n    password: ${DB_PASSWORD:K7PmdL9Rf2Q}\n    druid:\n      initial-size: ${DB_INITIAL_SIZE:5}\n      min-idle: ${DB_MIN_IDLE:5}\n      max-active: ${DB_MAX_ACTIVE:20}\n      max-wait: ${DB_MAX_WAIT:60000}\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n  typeAliasesPackage: com.lgjy.wx\n  mapperLocations: classpath:mapper/**/*.xml\n  configuration:\n    mapUnderscoreToCamelCase: true\n\n# 小程序API配置\napplet-api:\n  url: ${APPLET_API_URL:https://api.weixin.qq.com}\n  appid: ${APPLET_APPID:wxdcd31ee3e79190cc}\n  secret: ${APPLET_SECRET:2e18a85cfa56e071fa00e96aaa9012cc}\n  grantType: ${APPLET_GRANT_TYPE:authorization_code}\n\n# 银联支付API\nunion-pay-api:\n  sign: ${UNION_PAY_SIGN:37Y1}\n  url: ${UNION_PAY_URL:https://api-mop.chinaums.com}\n  appId: ${UNION_PAY_APP_ID:wxdcd31ee3e79190cc}\n  appKey: ${UNION_PAY_APP_KEY:2e18a85cfa56e071fa00e96aaa9012cc}\n  notifyUrlOrder: ${UNION_PAY_NOTIFY_URL_ORDER:https://test-parking.example.com/wx/parking/order/payCallback}\n  notifyUrlVip: ${UNION_PAY_NOTIFY_URL_VIP:https://test-parking.example.com/wx/package/payCallback}\n  notifyUrlOrderAlipay: ${UNION_PAY_NOTIFY_URL_ORDER_ALIPAY:https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay}\n  timeout: ${UNION_PAY_TIMEOUT:5}\n  # 发票配置\n  invoiceUrl: ${UNION_PAY_INVOICE_URL:https://fapiao.chinaums.com/fapiao-api/}\n  invoiceMsgSrc: ${UNION_PAY_INVOICE_MSG_SRC:LINGANG_JT}\n  invoiceKey: ${UNION_PAY_INVOICE_KEY:12cd2d10f2204107bf87df57aa7a4c4e}\n  # 发票回调地址\n  notifyUrlOrderInvoice: ${UNION_PAY_NOTIFY_URL_ORDER_INVOICE:https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback}\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','d88616a204d55afe66b387a34f45d0c7','2025-07-24 14:44:14','2025-07-24 14:44:15','nacos','**********','I','','','formal',NULL,NULL),(73,225,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT+8&allowPublicKeyRetrieval=true\n    username: ${DB_USERNAME:admin}\n    password: ${DB_PASSWORD:K7PmdL9Rf2Q}\n    druid:\n      initial-size: ${DB_INITIAL_SIZE:5}\n      min-idle: ${DB_MIN_IDLE:5}\n      max-active: ${DB_MAX_ACTIVE:20}\n      max-wait: ${DB_MAX_WAIT:60000}\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n  typeAliasesPackage: com.lgjy.wx\n  mapperLocations: classpath:mapper/**/*.xml\n  configuration:\n    mapUnderscoreToCamelCase: true\n\n# 小程序API配置\napplet-api:\n  url: ${APPLET_API_URL:https://api.weixin.qq.com}\n  appid: ${APPLET_APPID:wxdcd31ee3e79190cc}\n  secret: ${APPLET_SECRET:2e18a85cfa56e071fa00e96aaa9012cc}\n  grantType: ${APPLET_GRANT_TYPE:authorization_code}\n\n# 银联支付API\nunion-pay-api:\n  sign: ${UNION_PAY_SIGN:37Y1}\n  url: ${UNION_PAY_URL:https://api-mop.chinaums.com}\n  appId: ${UNION_PAY_APP_ID:wxdcd31ee3e79190cc}\n  appKey: ${UNION_PAY_APP_KEY:2e18a85cfa56e071fa00e96aaa9012cc}\n  notifyUrlOrder: ${UNION_PAY_NOTIFY_URL_ORDER:https://test-parking.example.com/wx/parking/order/payCallback}\n  notifyUrlVip: ${UNION_PAY_NOTIFY_URL_VIP:https://test-parking.example.com/wx/package/payCallback}\n  notifyUrlOrderAlipay: ${UNION_PAY_NOTIFY_URL_ORDER_ALIPAY:https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay}\n  timeout: ${UNION_PAY_TIMEOUT:5}\n  # 发票配置\n  invoiceUrl: ${UNION_PAY_INVOICE_URL:https://fapiao.chinaums.com/fapiao-api/}\n  invoiceMsgSrc: ${UNION_PAY_INVOICE_MSG_SRC:LINGANG_JT}\n  invoiceKey: ${UNION_PAY_INVOICE_KEY:12cd2d10f2204107bf87df57aa7a4c4e}\n  # 发票回调地址\n  notifyUrlOrderInvoice: ${UNION_PAY_NOTIFY_URL_ORDER_INVOICE:https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback}\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','d88616a204d55afe66b387a34f45d0c7','2025-07-24 14:46:36','2025-07-24 14:46:36','nacos','**********','U','','','formal',NULL,NULL),(73,226,'park-wx-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT+8&allowPublicKeyRetrieval=true\n    username: ${DB_USERNAME:admin}\n    password: ${DB_PASSWORD:K7PmdL9Rf2Q}\n    druid:\n      initial-size: ${DB_INITIAL_SIZE:5}\n      min-idle: ${DB_MIN_IDLE:5}\n      max-active: ${DB_MAX_ACTIVE:20}\n      max-wait: ${DB_MAX_WAIT:60000}\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n  typeAliasesPackage: com.lgjy.wx\n  mapperLocations: classpath:mapper/**/*.xml\n  configuration:\n    mapUnderscoreToCamelCase: true\n\n# 小程序API配置\napplet-api:\n  url: ${APPLET_API_URL:https://api.weixin.qq.com}\n  appid: ${APPLET_APPID:wxdcd31ee3e79190cc}\n  secret: ${APPLET_SECRET:2e18a85cfa56e071fa00e96aaa9012cc}\n  grantType: ${APPLET_GRANT_TYPE:authorization_code}\n\n# 银联支付API\nunion-pay-api:\n  sign: ${UNION_PAY_SIGN:37Y1}\n  url: ${UNION_PAY_URL:https://api-mop.chinaums.com}\n  appId: ${UNION_PAY_APP_ID:wxdcd31ee3e79190cc}\n  appKey: ${UNION_PAY_APP_KEY:2e18a85cfa56e071fa00e96aaa9012cc}\n  notifyUrlOrder: ${UNION_PAY_NOTIFY_URL_ORDER:https://test-parking.example.com/wx/parking/order/payCallback}\n  notifyUrlVip: ${UNION_PAY_NOTIFY_URL_VIP:https://test-parking.example.com/wx/package/payCallback}\n  notifyUrlOrderAlipay: ${UNION_PAY_NOTIFY_URL_ORDER_ALIPAY:https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay}\n  timeout: ${UNION_PAY_TIMEOUT:5}\n  # 发票配置\n  invoiceUrl: ${UNION_PAY_INVOICE_URL:https://fapiao.chinaums.com/fapiao-api/}\n  invoiceMsgSrc: ${UNION_PAY_INVOICE_MSG_SRC:LINGANG_JT}\n  invoiceKey: ${UNION_PAY_INVOICE_KEY:12cd2d10f2204107bf87df57aa7a4c4e}\n  # 发票回调地址\n  notifyUrlOrderInvoice: ${UNION_PAY_NOTIFY_URL_ORDER_INVOICE:https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback}\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','d88616a204d55afe66b387a34f45d0c7','2025-07-24 14:49:35','2025-07-24 14:49:35','nacos','**********','U','','','formal',NULL,NULL),(0,227,'park-gate-test.yml','DEFAULT_GROUP','','# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true\n    username: ${DB_USERNAME:admin}\n    password: ${DB_PASSWORD:K7PmdL9Rf2Q}\n    druid:\n      initial-size: ${DB_INITIAL_SIZE:5}\n      min-idle: ${DB_MIN_IDLE:5}\n      max-active: ${DB_MAX_ACTIVE:20}\n      max-wait: ${DB_MAX_WAIT:60000}\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n  # 搜索指定包别名\n  typeAliasesPackage: com.lgjy.gate\n  # 配置mapper的扫描，找到所有的mapper.xml映射文件\n  mapperLocations: classpath:mapper/**/*.xml\n  # 开启驼峰命名自动映射\n  configuration:\n    mapUnderscoreToCamelCase: true\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always','349c5ff4dc41c670b0d8f21a8d1a5025','2025-07-24 15:18:06','2025-07-24 15:18:07','nacos','**********','I','','','formal',NULL,NULL);
/*!40000 ALTER TABLE `his_config_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'role',
  `resource` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'resource',
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'action',
  UNIQUE KEY `uk_role_permission` (`role`,`resource`,`action`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'username',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'role',
  UNIQUE KEY `idx_user_role` (`username`,`role`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES ('nacos','ROLE_ADMIN');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tenant_capacity`
--

DROP TABLE IF EXISTS `tenant_capacity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenant_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='租户容量信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenant_capacity`
--

LOCK TABLES `tenant_capacity` WRITE;
/*!40000 ALTER TABLE `tenant_capacity` DISABLE KEYS */;
/*!40000 ALTER TABLE `tenant_capacity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tenant_info`
--

DROP TABLE IF EXISTS `tenant_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenant_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_tenant_info_kptenantid` (`kp`,`tenant_id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='tenant_info';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenant_info`
--

LOCK TABLES `tenant_info` WRITE;
/*!40000 ALTER TABLE `tenant_info` DISABLE KEYS */;
INSERT INTO `tenant_info` VALUES (1,'1','a670e801-046b-4366-85ba-7643de52359f','test','用于在测试环境下配置并运行的测试配置文件的内容','nacos',1753017438364,1753017662054);
/*!40000 ALTER TABLE `tenant_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'username',
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'password',
  `enabled` tinyint(1) NOT NULL COMMENT 'enabled',
  PRIMARY KEY (`username`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('nacos','$2a$10$74rWF3zsYxEulzXgJCEkQ.NH5TxvIdPtU7NHjHcKGVqUoa3mDeheW',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'nacos'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-26  4:06:25
