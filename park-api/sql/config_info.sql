-- Create the config_info table
CREATE TABLE `config_info` (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                               `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
                               `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'group_id',
                               `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
                               `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'md5',
                               `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                               `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT 'source user',
                               `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'source ip',
                               `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'app_name',
                               `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT '' COMMENT '租户字段',
                               `c_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'configuration description',
                               `c_use` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT 'configuration usage',
                               `effect` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '配置生效的描述',
                               `type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '配置的类型',
                               `c_schema` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '配置的模式',
                               `encrypted_data_key` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '密钥',
                               PRIMARY KEY (`id`) USING BTREE,
                               UNIQUE INDEX `uk_configinfo_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'config_info' ROW_FORMAT = DYNAMIC;

-- Insert configuration data
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES
                                                                                                                                                                                                                                  (2, 'park-gateway-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut', 'e6f0cead56e94162e99231d0c3d18d79', '2020-05-14 14:17:55', '2025-07-13 10:32:12', NULL, '0:0:0:0:0:0:0:1', NULL, '', '网关模块', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (3, 'park-auth-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password:\n    database: 2', '756a03974b815ff3febec9744c633cf4', '2020-11-20 00:00:00', '2025-07-13 13:38:03', NULL, '0:0:0:0:0:0:0:1', NULL, '', '认证中心', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (5, 'park-system-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml', '1efaabbb1af2a55d8484c9232efec24a', '2020-11-20 00:00:00', '2025-07-13 14:13:30', NULL, '0:0:0:0:0:0:0:1', NULL, '', '系统模块', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (8, 'park-file-dev.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    domain: http://127.0.0.1:9300\n    path: D:/lgjy/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）', '95bb71a3e1301faa5d04c1e6685936cb', '2020-11-20 00:00:00', '2025-07-13 10:32:35', NULL, '0:0:0:0:0:0:0:1', NULL, '', NULL, NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (10, 'park-wxauth-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信验证配置\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: api\n  api-key: ************************************\n  sign: 【捷运停车充电】\n  template: "您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。"\n\n# 小程序微信API配置\nwx-api:\n  # 小程序基础参数\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', 'a85b912b55c71764159f7f1487c59c5a', '2025-06-07 11:34:11', '2025-07-25 15:34:52', NULL, '0:0:0:0:0:0:0:1', NULL, '', '微信小程序认证中心', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (11, 'park-wx-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    typeAliasesPackage: com.lgjy.wx\n    mapperLocations: classpath:mapper/**/*.xml\n    configuration:\n      mapUnderscoreToCamelCase: true\n\napplet-api:\n    url: https://api.weixin.qq.com\n    appid: wxdcd31ee3e79190cc\n    secret: 2e18a85cfa56e071fa00e96aaa9012cc\n    grantType: authorization_code\n\n# 银联API\nunion-pay-api:\n    sign: 37Y1\n    url: https://api-mop.chinaums.com\n    appId: wxdcd31ee3e79190cc\n    appKey: 2e18a85cfa56e071fa00e96aaa9012cc\n    notifyUrlOrder: https://f6fbab68fe4a.ngrok-free.app/wx/parking/order/payCallback\n    notifyUrlVip: https://f6fbab68fe4a.ngrok-free.app/wx/package/payCallback\n    notifyUrlOrderAlipay: https://875c-39-173-159-200.ngrok-free.app/wx/parking/order/notifyUrlOrderAlipay\n    timeout: 5\n    # 发票\n    invoiceUrl: https://fapiao.chinaums.com/fapiao-api/\n    invoiceMsgSrc: LINGANG_JT\n    invoiceKey: 12cd2d10f2204107bf87df57aa7a4c4e\n    # 发票回调\n    notifyUrlOrderInvoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback', '10d2a8b81d651cbab122c40b15714342', '2025-06-07 18:11:24', '2025-07-16 01:57:31', NULL, '0:0:0:0:0:0:0:1', NULL, '', NULL, NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (21, 'park-gate-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n    database: 2\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: **************************************************************************************************************************************************    username: root\n    password: 1234\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.lgjy.gate\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n    # 开启驼峰命名自动映射\n    configuration:\n      mapUnderscoreToCamelCase: true', 'e5c4a07fc4ddfc06f70c489f3cda79bc', '2025-06-10 21:01:06', '2025-07-13 14:01:56', NULL, '0:0:0:0:0:0:0:1', NULL, '', 'lgjy-gate模块数据源配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (24, 'sizhuo-config.yml', 'DEFAULT_GROUP', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路\n      channelRules:\n        entryDisabledChannels: ["3"]  \n        exitDisabledChannels: ["4"]', 'c8e6072bf8e82c585544fb23dc829893', '2025-07-11 10:32:41', '2025-07-13 14:01:24', NULL, '0:0:0:0:0:0:0:1', NULL, '', NULL, NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (26, 'common-dev.yml', 'DEFAULT_GROUP', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false', '9b888a075373e2b0decd67ff85fa7ee7', '2025-07-12 09:52:04', '2025-07-13 14:00:47', NULL, '0:0:0:0:0:0:0:1', NULL, '', '通用配置文件-所有微服务共享', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (55, 'park-gateway-test.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        max-active: 200\n        max-wait: -1ms\n        max-idle: 10\n        min-idle: 0\n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n\n        # 认证中心\n        - id: park-auth\n          uri: lb://park-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestBody\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 系统模块\n        - id: park-system\n          uri: lb://park-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: park-file\n          uri: lb://park-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序认证中心\n        - id: park-wxauth\n          uri: lb://park-wxauth\n          predicates:\n            - Path=/wxauth/**\n          filters:\n            - StripPrefix=1\n        # 微信小程序\n        - id: park-wx\n          uri: lb://park-wx\n          predicates:\n            - Path=/wx/**\n          filters:\n            - StripPrefix=1\n        # 道闸\n        - id: park-gate\n          uri: lb://park-gate\n          predicates:\n            - Path=/gate/**\n          filters:\n            - StripPrefix=1\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,routes,gateway\n  endpoint:\n    health:\n      show-details: always\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: true\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /code\n      - /wxauth/code\n      - /wxauth/login/code\n      - /wxauth/login/wx\n      - /wx/advertConfig/list\n      - /wx/parking/order/payCallback\n      - /wx/parking/order/channelPayQuery\n      - /wx/warehouse/list\n      - /wx/package/payCallback\n      - /gate/sizhuo/pull\n      - /gate/sizhuo/push/CarIn\n      - /gate/sizhuo/push/CarOut', '8b23ffc4dfa5742ef5574ac95451399c', '2025-07-20 21:18:45', '2025-07-25 13:49:07', 'nacos', '*************', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '这是测试环境的网关配置文件', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (56, 'park-auth-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    driverClassName: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接数\n      initialSize: 5\n      # 最小连接池数量\n      minIdle: 10\n      # 最大连接池数量\n      maxActive: 20\n      # 配置获取连接等待超时的时间\n      maxWait: 60000\n      # 配置连接超时时间\n      connectTimeout: 30000\n      # 配置网络超时时间\n      socketTimeout: 60000\n      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒\n      timeBetweenEvictionRunsMillis: 60000\n      # 配置一个连接在池中最小生存的时间，单位是毫秒\n      minEvictableIdleTimeMillis: 300000\n      # 配置一个连接在池中最大生存的时间，单位是毫秒\n      maxEvictableIdleTimeMillis: 900000\n      # 配置检测连接是否有效\n      validationQuery: SELECT 1 FROM DUAL\n      testWhileIdle: true\n      testOnBorrow: false\n      testOnReturn: false\n      webStatFilter:\n        enabled: true\n      statViewServlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '65cd5a683b96f5a56c1a645c97ef6424', '2025-07-20 21:21:52', '2025-07-25 13:41:47', 'nacos', '***************', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '认证中心测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (57, 'park-system-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接数\n      initial-size: 5\n      # 最小连接池数量\n      min-idle: 10\n      # 最大连接池数量\n      max-active: 20\n      # 配置获取连接等待超时的时间\n      max-wait: 60000\n      # 配置连接超时时间\n      connect-timeout: 30000\n      # 配置网络超时时间\n      socket-timeout: 60000\n      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒\n      time-between-eviction-runs-millis: 60000\n      # 配置一个连接在池中最小生存的时间，单位是毫秒\n      min-evictable-idle-time-millis: 300000\n      # 配置一个连接在池中最大生存的时间，单位是毫秒\n      max-evictable-idle-time-millis: 900000\n      # 配置检测连接是否有效\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 搜索指定包别名\n  type-aliases-package: com.lgjy.system\n  # 配置mapper的扫描，找到所有的mapper.xml映射文件\n  mapper-locations: classpath:mapper/**/*.xml\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '26bb77068b62aeb9769ad43fa9a2d335', '2025-07-20 21:22:45', '2025-07-25 13:58:00', 'nacos', '***************', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '系统模块测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (58, 'park-file-test.yml', 'DEFAULT_GROUP', '# 本地文件上传 - 修正为Docker容器路径\nfile:\n    domain: http://127.0.0.1:9300\n    path: /app/upload\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test\n\nspring:\n  servlet:\n    multipart:\n      max-file-size: 10MB       # 单个文件最大10MB\n      max-request-size: 20MB    # 整个请求最大20MB\n      enabled: true             # 启用文件上传功能\n      resolve-lazily: false     # 是否延迟解析（大文件建议true）\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '7defcbf1c60debbdb3f1d0f6e68a0918', '2025-07-20 21:26:38', '2025-07-20 21:36:42', NULL, '0:0:0:0:0:0:0:1', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '文件服务测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (62, 'common-test.yml', 'DEFAULT_GROUP', '# ===========================================\n# 通用配置文件 - 所有微服务共享\n# ===========================================\n\n# 日志配置\nlogging:\n  level:\n    com.lgjy: INFO\n  file:\n    name: logs/${spring.application.name}/app.log\n    max-size: 100MB\n    max-history: 30\n\n# Sentinel配置\nspring:\n  cloud:\n    sentinel:\n      log:\n        dir: logs/sentinel\n        switch-pid: false\n\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '5ca228a0dff86cfd85fa97b51116a91b', '2025-07-20 21:30:57', '2025-07-20 21:31:15', NULL, '0:0:0:0:0:0:0:1', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '通用配置文件-所有微服务共享测试环境', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (64, 'sizhuo-config-test.yml', 'DEFAULT_GROUP', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路测试\n      channelRules:\n        entryDisabledChannels: ["3"]  \n        exitDisabledChannels: ["4"]', 'f0344d688316f6aacb0206b53bb7e799', '2025-07-20 21:32:34', '2025-07-20 21:32:34', NULL, '0:0:0:0:0:0:0:1', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '思卓道闸测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (77, 'park-wx-test.yml', 'DEFAULT_GROUP', '# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: admin\n    password: K7PmdL9Rf2Q\n    druid:\n      # 初始连接数\n      initial-size: 5\n      # 最小连接池数量\n      min-idle: 10\n      # 最大连接池数量\n      max-active: 20\n      # 配置获取连接等待超时的时间\n      max-wait: 60000\n      # 配置连接超时时间\n      connect-timeout: 30000\n      # 配置网络超时时间\n      socket-timeout: 60000\n      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒\n      time-between-eviction-runs-millis: 60000\n      # 配置一个连接在池中最小生存的时间，单位是毫秒\n      min-evictable-idle-time-millis: 300000\n      # 配置一个连接在池中最大生存的时间，单位是毫秒\n      max-evictable-idle-time-millis: 900000\n      # 配置检测连接是否有效\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      web-stat-filter:\n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n          enabled: true\n          # 慢SQL记录\n          log-slow-sql: true\n          slow-sql-millis: 1000\n          merge-sql: true\n        wall:\n          config:\n            multi-statement-allow: true\n\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n\n# MyBatis配置\nmybatis:\n  # 搜索指定包别名\n  type-aliases-package: com.lgjy.wx\n  # 配置mapper的扫描，找到所有的mapper.xml映射文件\n  mapper-locations: classpath:mapper/**/*.xml\n  configuration:\n    map-underscore-to-camel-case: true\n\n# 小程序API配置\napplet-api:\n  url: https://api.weixin.qq.com\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grant-type: authorization_code\n\n# 银联支付API配置\nunion-pay-api:\n  sign: 37Y1\n  url: https://api-mop.chinaums.com\n  app-id: wxdcd31ee3e79190cc\n  app-key: 2e18a85cfa56e071fa00e96aaa9012cc\n  notify-url-order: https://test-parking.example.com/wx/parking/order/payCallback\n  notify-url-vip: https://test-parking.example.com/wx/package/payCallback\n  notify-url-order-alipay: https://test-parking.example.com/wx/parking/order/notifyUrlOrderAlipay\n  timeout: 5\n  # 发票配置\n  invoice-url: https://fapiao.chinaums.com/fapiao-api/\n  invoice-msg-src: LINGANG_JT\n  invoice-key: 12cd2d10f2204107bf87df57aa7a4c4e\n  # 发票回调地址\n  notify-url-order-invoice: https://test-parking.icv-ip.com/bigdata/merchant/invoiceRecord/invoiceCallback\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '617b95903a82bad4ae59e648068d3d4d', '2025-07-25 13:18:27', '2025-07-25 16:04:02', 'nacos', '127.0.0.1', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '微信小程序测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (78, 'park-wxauth-test.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: park-redis\n    port: 6379\n    password: qR6bW9kFzT3Zv\n    database: 2\n    timeout: 10s\n    lettuce:\n      pool:\n        # 连接池最大连接数\n        max-active: 200\n        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-wait: -1ms\n        # 连接池中的最大空闲连接\n        max-idle: 10\n        # 连接池中的最小空闲连接\n        min-idle: 0\n    \n# 短信服务配置\nmsg-server:\n  url: http://sms-api.luosimao.com\n  api: test-api-user\n  api-key: ************************************\n  sign: 【捷运停车充电】\n  template: "您正在进行短信验证，验证码为：{0}，有效期：{1}分钟，请勿泄露给他人。"\n\n# 微信小程序API配置\nwx-api:\n  url: https://api.weixin.qq.com\n  phoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber\n  accessUrl: https://api.weixin.qq.com/cgi-bin/token\n  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info\n  appid: wxdcd31ee3e79190cc\n  secret: 2e18a85cfa56e071fa00e96aaa9012cc\n  grantType: authorization_code\n\n# 健康检查配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '10004511d160ad7b9ad19e43d28a2d52', '2025-07-25 13:18:44', '2025-07-25 15:35:03', 'nacos', '127.0.0.1', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '微信认证测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (79, 'park-gate-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: ${REDIS_HOST:park-redis}\n    port: ${REDIS_PORT:6379}\n    password: ${REDIS_PASSWORD:qR6bW9kFzT3Zv}\n    database: ${REDIS_DATABASE:2}\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:parknew}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true\n    username: ${DB_USERNAME:admin}\n    password: ${DB_PASSWORD:K7PmdL9Rf2Q}\n    druid:\n      initial-size: ${DB_INITIAL_SIZE:5}\n      min-idle: ${DB_MIN_IDLE:5}\n      max-active: ${DB_MAX_ACTIVE:20}\n      max-wait: ${DB_MAX_WAIT:60000}\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n\n# mybatis配置\nmybatis:\n  # 搜索指定包别名\n  typeAliasesPackage: com.lgjy.gate\n  # 配置mapper的扫描，找到所有的mapper.xml映射文件\n  mapperLocations: classpath:mapper/**/*.xml\n  # 开启驼峰命名自动映射\n  configuration:\n    mapUnderscoreToCamelCase: true\n\n# 管理端点配置\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics\n  endpoint:\n    health:\n      show-details: always', '349c5ff4dc41c670b0d8f21a8d1a5025', '2025-07-25 13:18:54', '2025-07-25 13:18:54', 'nacos', '127.0.0.1', NULL, 'a670e801-046b-4366-85ba-7643de52359f', '道闸服务测试环境配置', NULL, NULL, 'yaml', NULL, ''),
                                                                                                                                                                                                                                  (80, 'sizhuo-config-dev.yml', 'DEFAULT_GROUP', 'sizhuo:\n  brandId: sizhuo\n  brandName: 思卓\n  requestUrl: http://dev-qr.it-wy.cn:83\n  areas:\n    - physicalId: 20240926133058945          \n      logicalId: 728424271419936768               \n      name: 江山路开发\n      channelRules:\n        entryDisabledChannels: ["3"]  \n        exitDisabledChannels: ["4"]', 'd4fe24705cbb6bd59caea6635493cefa', '2025-07-25 13:19:28', '2025-07-25 13:19:28', 'nacos', '127.0.0.1', NULL, '', '思卓设备开发环境配置', NULL, NULL, 'yaml', NULL, '');