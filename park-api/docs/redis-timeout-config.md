# Redis订单超时配置说明

## 功能概述

基于Redis键过期事件实现15分钟订单超时重新支付功能。当用户下单后15分钟内未支付，系统自动将订单状态从"待支付(1)"更新为"支付中(2)"，允许用户重新支付。

## Redis配置要求

### 1. 开启键过期事件通知

Redis默认不开启键过期事件通知，需要手动配置：

```bash
# 方式1：通过redis-cli命令配置（重启后失效）
redis-cli config set notify-keyspace-events Ex

# 方式2：在redis.conf配置文件中添加（永久生效）
notify-keyspace-events Ex
```

### 2. 配置说明

- `E`：开启键事件通知
- `x`：开启过期事件通知
- `Ex`：同时开启键事件和过期事件通知

### 3. 验证配置

```bash
# 查看当前配置
redis-cli config get notify-keyspace-events

# 应该返回：
# 1) "notify-keyspace-events"
# 2) "Ex"
```

## 应用配置

### 1. Spring Boot配置

在`application.yml`或Nacos配置中确保Redis连接正常：

```yaml
spring:
  redis:
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
```

### 2. 启用定时任务

确保在主启动类上添加`@EnableScheduling`注解：

```java
@SpringBootApplication
@EnableScheduling  // 启用定时任务
public class LgjyWxApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjyWxApplication.class, args);
    }
}
```

## 功能流程

### 1. 订单创建流程

```
用户下单 → 保存订单记录 → 设置Redis超时键(15分钟TTL) → 发起支付
```

### 2. 超时处理流程

```
Redis键过期 → 触发过期事件 → 事件监听器处理 → 更新订单状态为支付中(2)
```

### 3. 支付成功流程

```
支付回调 → 更新订单状态为已支付(5) → 清理Redis超时键 → 处理业务逻辑
```

## Redis键设计

### 超时控制键

- **键名格式**：`order:timeout:{tradeId}`
- **值**：`{tradeId}`
- **TTL**：15分钟
- **用途**：控制订单超时时间，过期时触发事件

## 备用机制

### 1. 定时任务备用

- **执行频率**：每5分钟
- **处理逻辑**：查询15分钟前创建且状态为待支付的订单
- **补偿机制**：检查Redis键是否存在，如不存在则手动处理超时

### 2. 内部补偿机制

系统内部提供补偿处理方法，用于定时任务：

```java
// 备用定时任务会自动调用内部方法处理超时订单
// 无需手动干预，系统自动保障可靠性
```

## 监控和日志

### 1. 关键日志

- 订单创建时设置Redis键
- Redis键过期事件触发
- 订单状态更新成功/失败
- 支付成功时清理Redis键

### 2. 监控指标

- 超时订单数量
- Redis事件处理成功率
- 备用任务处理数量

## 注意事项

### 1. Redis事件可靠性

- Redis键过期事件不是100%可靠的
- 在高负载情况下可能会丢失事件
- 因此需要定时任务作为备用方案

### 2. 性能考虑

- 大量键过期可能影响Redis性能
- 建议监控Redis的内存使用情况
- 合理设置键的TTL时间

### 3. 集群环境

- 如果使用Redis集群，需要确保事件监听配置正确
- 建议在单个Redis实例上测试功能

## 故障排查

### 1. 事件不触发

检查Redis配置：
```bash
redis-cli config get notify-keyspace-events
```

### 2. 订单状态未更新

检查应用日志：
```
grep "订单超时" application.log
```

### 3. Redis连接问题

检查Redis连接状态：
```bash
redis-cli ping
```

## 功能验证

### 1. 基本功能测试

1. 创建订单但不支付
2. 等待15分钟
3. 检查数据库中订单状态是否从1变为2
4. 验证是否可以重新支付

### 2. 系统稳定性测试

1. 创建多个订单
2. 观察Redis性能和内存使用
3. 检查应用日志中的事件处理记录

### 3. 容错性测试

1. 模拟Redis服务重启
2. 验证备用定时任务是否正常工作
3. 检查订单状态更新的完整性
