package com.lgjy.common.seata.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Seata 自动配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "seata.enabled", havingValue = "true", matchIfMissing = false)
public class SeataAutoConfiguration {
    
    // 这里可以添加Seata相关的配置
    // 目前保持空实现，避免JAR为空的警告
    
}
