package com.lgjy.common.security.service;

import com.lgjy.common.core.constant.CacheConstants;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.utils.JwtUtils;
import com.lgjy.common.core.utils.uuid.IdUtils;
import com.lgjy.common.redis.service.RedisService;
import com.lgjy.system.api.model.LoginWxUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 小程序 token验证处理
 *
 * <AUTHOR>
 */
@Component
public class WxTokenService {

    private static final Logger log = LoggerFactory.getLogger(WxTokenService.class);

    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND * 60 * 24;

    private final static long TOKEN_EXPIRE_TIME = CacheConstants.WX_EXPIRATION;

    private final static String ACCESS_WX_TOKEN = CacheConstants.LOGIN_WX_TOKEN_KEY;

    private final static Long TOKEN_REFRESH_THRESHOLD_MINUTES = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 用户登录时创建令牌
     */
    public Map<String, Object> createToken(LoginWxUser loginWxUser)
    {
        String token = IdUtils.fastUUID();
        Long userId = loginWxUser.getWxUser().getId();
        String userName = loginWxUser.getWxUser().getUserName();
        loginWxUser.setToken(token);
        loginWxUser.setUserid(userId);
        loginWxUser.setUsername(userName);
        refreshToken(loginWxUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, userId);
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, userName);

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("token", JwtUtils.createToken(claimsMap));
        rspMap.put("wxUser", loginWxUser.getWxUser());
        return rspMap;
    }

    /**
     * 用户退出登录时，删除令牌
     * @param token
     */
    public void logout(String token) {
        String userKey = JwtUtils.getUserKey(token);
        redisService.deleteObject(getTokenKey(userKey));
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginWxUser 登录信息
     */
    public void refreshToken(LoginWxUser loginWxUser)
    {
        loginWxUser.setLoginTime(System.currentTimeMillis());
        loginWxUser.setExpireTime(loginWxUser.getLoginTime() + TOKEN_EXPIRE_TIME * MILLIS_MINUTE);
        // 根据uuid将loginWxUser缓存
        String userKey = getTokenKey(loginWxUser.getToken());
        redisService.setCacheObject(userKey, loginWxUser, TOKEN_EXPIRE_TIME, TimeUnit.DAYS);
    }

    private String getTokenKey(String token)
    {
        return ACCESS_WX_TOKEN + token;
    }

}
