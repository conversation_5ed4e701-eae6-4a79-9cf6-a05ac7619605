package com.lgjy.common.security.utils;

import java.util.Collection;
import java.util.List;
import com.alibaba.fastjson2.JSONArray;
import com.lgjy.common.core.constant.CacheConstants;
import com.lgjy.common.core.utils.SpringUtils;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.redis.service.RedisService;
import com.lgjy.system.api.domain.SysDictData;

/**
 * 字典工具类
 * 
 * <AUTHOR>
 */
public class DictUtils {
    /**
     * 设置字典缓存
     * 
     * @param key       参数键
     * @param dictDatas 字典数据列表
     */
    public static void setDictCache(String key, List<SysDictData> dictDatas) {
        SpringUtils.getBean(RedisService.class).setCacheObject(getCacheKey(key), dictDatas);
    }

    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<SysDictData> getDictCache(String key) {
        try {
            JSONArray arrayCache = SpringUtils.getBean(RedisService.class).getCacheObject(getCacheKey(key));
            if (StringUtils.isNotNull(arrayCache)) {
                return arrayCache.toList(SysDictData.class);
            }
        } catch (Exception e) {
            // 如果缓存数据格式有问题，清理该缓存并返回null，让系统重新从数据库加载
            System.err.println("字典缓存数据格式错误，清理缓存: " + key + ", 错误信息: " + e.getMessage());
            removeDictCache(key);
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     * 
     * @param key 字典键
     */
    public static void removeDictCache(String key) {
        SpringUtils.getBean(RedisService.class).deleteObject(getCacheKey(key));
    }

    /**
     * 清空字典缓存
     */
    public static void clearDictCache() {
        Collection<String> keys = SpringUtils.getBean(RedisService.class).keys(CacheConstants.SYS_DICT_KEY + "*");
        SpringUtils.getBean(RedisService.class).deleteObject(keys);
    }

    /**
     * 设置cache key
     * 
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey) {
        return CacheConstants.SYS_DICT_KEY + configKey;
    }
}
