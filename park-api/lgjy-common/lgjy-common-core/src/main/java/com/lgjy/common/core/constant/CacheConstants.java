package com.lgjy.common.core.constant;

/**
 * 缓存常量信息
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存有效期，默认15天
     */
    public final static long WX_EXPIRATION = 15;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 小程序权限缓存前缀
     */
    public final static String LOGIN_WX_TOKEN_KEY = "login_wx_tokens:";

    /**
     * 后台登录验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 手机号登录验证码 wx redis key
     */
    public static final String PHONE_CAPTCHA_CODE_KEY = "phone_captcha_codes:";

    /**
     * 手机号登录验证码过期时间 5分钟 wx redis key
     */
    public static final long PHONE_CAPTCHA_CODE_EXPIRATION = 5;

    /**
     * 手机号登录验证码允许重发时间 4分钟 240秒 wx redis key
     */
    public static final long PHONE_CAPTCHA_CODE_RESEND = 240;

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";
}
