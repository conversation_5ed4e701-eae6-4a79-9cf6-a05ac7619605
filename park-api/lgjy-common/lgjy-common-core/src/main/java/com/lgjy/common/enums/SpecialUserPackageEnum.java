package com.lgjy.common.enums;

import java.math.BigDecimal;
import java.util.*;

public enum SpecialUserPackageEnum {
    // 枚举常量定义（第一行）
    SPECIAL_USER_PACKAGE_GROUP_FREE(1L, 1, 3, "年度套餐", BigDecimal.ZERO),
    SPECIAL_USER_PACKAGE_GROUP_SALE(2L, 2, 3, "年度套餐", new BigDecimal("600")),
    SPECIAL_USER_PACKAGE_GROUP_OTHER_YEAR(4L, 3, 3, "年度套餐", new BigDecimal("1800")),
    SPECIAL_USER_PACKAGE_COMMON_YEAR(6L, 4, 3, "年度套餐", new BigDecimal("1800"));

    // 枚举类属性
    private final Long id;
    private final Integer code;
    private final Integer type;
    private final String name;
    private final BigDecimal price;

    // 构造方法必须为private
    private SpecialUserPackageEnum(Long id, Integer code, Integer type, String name, BigDecimal price) {
        this.id = id;
        this.code = code;
        this.type = type;
        this.name = name;
        this.price = price;
    }

    // Getter方法
    public Long getId() { return id; }
    public Integer getCode() { return code; }
    public Integer getType() { return type; }
    public String getName() { return name; }
    public BigDecimal getPrice() { return price; }

    // 根据id获取枚举实例
    public static SpecialUserPackageEnum getEnum(Long id) {
        if (id == null) return null;
        for (SpecialUserPackageEnum item : values()) {
            if (item.id.equals(id)) {
                return item;
            }
        }
        return null;
    }

    // 根据code获取枚举列表
    public static List<Map<String, Object>> getEnumList(Integer code) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (SpecialUserPackageEnum item : values()) {
            if (item.code.equals(code)) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", item.id);
                map.put("name", item.name);
                map.put("price", item.price);
                result.add(map);
            }
        }
        return result;
    }
}
