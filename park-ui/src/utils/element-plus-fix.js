/**
 * Element Plus 插槽警告修复
 * 解决 "Slot 'default' invoked outside of the render function" 警告
 */

// 保存原始的 console.warn 方法
const originalWarn = console.warn

// 需要过滤的警告消息
const FILTERED_WARNINGS = [
  'Slot "default" invoked outside of the render function',
  'Slot \'default\' invoked outside of the render function',
  'Slot "content" invoked outside of the render function',
  'Slot \'content\' invoked outside of the render function',
  'ResizeObserver loop completed with undelivered notifications',
  'Could not establish connection. Receiving end does not exist',
  'Unchecked runtime.lastError',
  'A listener indicated an asynchronous response by returning true',
  'message channel closed before a response was received',
  'Added non-passive event listener',
  'Consider marking event handler as \'passive\'',
  '[Violation]Added non-passive event listener',
  'chrome-extension://',
  'moz-extension://',
  'Extension context invalidated',
  '[el-radio] [API] label act as value is about to be deprecated',
  'label act as value is about to be deprecated',
  '[el-button] [API] type "text" is about to be deprecated',
  'type "text" is about to be deprecated'
]

/**
 * 自定义 console.warn 方法，过滤特定警告
 */
console.warn = function (...args) {
  const message = args.join(' ')

  // 检查是否是需要过滤的警告
  const shouldFilter = FILTERED_WARNINGS.some(warning =>
    message.includes(warning)
  )

  // 如果不是需要过滤的警告，则正常输出
  if (!shouldFilter) {
    originalWarn.apply(console, args)
  }
}

/**
 * Vue 警告处理器
 */
export const vueWarnHandler = (msg, instance, trace) => {
  // 过滤 Element Plus 相关的插槽警告
  const shouldFilter = FILTERED_WARNINGS.some(warning =>
    msg.includes(warning)
  )

  if (shouldFilter) {
    return
  }

  // 过滤 Element Plus 折叠过渡相关的警告
  if (msg.includes('collapse-transition')) {
    return
  }

  // 其他警告正常显示
  console.warn(`[Vue warn]: ${msg}`, trace)
}

/**
 * 恢复原始的 console.warn 方法
 */
export const restoreConsoleWarn = () => {
  console.warn = originalWarn
}

export default {
  vueWarnHandler,
  restoreConsoleWarn
}
