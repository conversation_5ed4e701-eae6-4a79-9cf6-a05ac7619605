/**
 * Element Plus 组件配置优化
 * 解决组件中的被动事件监听器问题
 */

/**
 * 配置 Element Plus 组件的被动事件监听器
 */
export function configureElementPlusPassiveEvents() {
  // 等待 DOM 加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyElementPlusConfig);
  } else {
    applyElementPlusConfig();
  }
}

/**
 * 应用 Element Plus 配置
 */
function applyElementPlusConfig() {
  // 使用 MutationObserver 监听 DOM 变化，为新添加的 Element Plus 组件配置被动事件
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            configureElementPassiveEvents(node);
          }
        });
      }
    });
  });

  // 开始观察
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 为现有元素配置被动事件
  configureElementPassiveEvents(document.body);
  
  console.log('Element Plus 被动事件配置已应用');
}

/**
 * 为指定元素及其子元素配置被动事件监听器
 */
function configureElementPassiveEvents(element) {
  // Element Plus 组件选择器
  const elementPlusSelectors = [
    '.el-scrollbar',
    '.el-table__body-wrapper',
    '.el-select-dropdown',
    '.el-popper',
    '.el-tooltip__popper',
    '.el-dropdown-menu',
    '.el-menu',
    '.el-carousel',
    '.el-image-viewer__wrapper',
    '.el-dialog__wrapper',
    '.el-drawer__wrapper'
  ];

  elementPlusSelectors.forEach(selector => {
    const elements = element.querySelectorAll ? element.querySelectorAll(selector) : [];
    elements.forEach(el => {
      // 为这些元素添加被动事件监听器配置
      addPassiveEventListenersToElement(el);
    });
  });

  // 如果当前元素本身就是 Element Plus 组件
  if (element.classList) {
    elementPlusSelectors.forEach(selector => {
      if (element.matches && element.matches(selector.substring(1))) {
        addPassiveEventListenersToElement(element);
      }
    });
  }
}

/**
 * 为单个元素添加被动事件监听器
 */
function addPassiveEventListenersToElement(element) {
  const passiveEvents = ['touchstart', 'touchmove', 'wheel', 'mousewheel', 'scroll'];
  
  // 保存原始的 addEventListener 方法
  const originalAddEventListener = element.addEventListener;
  
  // 重写元素的 addEventListener 方法
  element.addEventListener = function(type, listener, options) {
    if (passiveEvents.includes(type)) {
      if (typeof options === 'boolean') {
        options = { capture: options, passive: true };
      } else if (typeof options === 'object' && options !== null) {
        options = { ...options, passive: true };
      } else {
        options = { passive: true };
      }
    }
    
    return originalAddEventListener.call(this, type, listener, options);
  };
}

/**
 * 创建被动事件监听器的包装函数
 */
export function createPassiveEventListener(handler, options = {}) {
  return {
    handler,
    options: { ...options, passive: true }
  };
}

/**
 * 安全地添加被动事件监听器
 */
export function addSafeEventListener(element, type, listener, options = {}) {
  const passiveEvents = ['touchstart', 'touchmove', 'touchend', 'wheel', 'mousewheel', 'scroll'];
  
  if (passiveEvents.includes(type)) {
    options = { ...options, passive: true };
  }
  
  try {
    element.addEventListener(type, listener, options);
  } catch (error) {
    // 如果被动模式失败，尝试不使用被动模式
    if (options.passive) {
      const fallbackOptions = { ...options, passive: false };
      element.addEventListener(type, listener, fallbackOptions);
    } else {
      throw error;
    }
  }
}

export default {
  configureElementPlusPassiveEvents,
  createPassiveEventListener,
  addSafeEventListener
};
