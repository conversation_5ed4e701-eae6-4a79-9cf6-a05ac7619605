/**
 * 全局错误处理工具
 * 用于处理浏览器扩展和异步操作相关的错误
 */

/**
 * 检查是否为浏览器扩展相关错误
 * @param {Error|string} error 错误对象或错误消息
 * @returns {boolean} 是否为扩展相关错误
 */
export function isExtensionError(error) {
  const errorMessage = typeof error === 'string' ? error : error?.message || '';

  const extensionErrorPatterns = [
    'message channel closed',
    'Extension context invalidated',
    'Could not establish connection',
    'Receiving end does not exist',
    'runtime.lastError',
    'Unchecked runtime.lastError',
    'The message port closed before a response was received',
    'A listener indicated an asynchronous response by returning true',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'ResizeObserver loop completed with undelivered notifications',
    'Failed to load resource: net::ERR_CONNECTION_RESET',
    'Added non-passive event listener to a scroll-blocking',
    'Consider marking event handler as \'passive\'',
    '[Violation]Added non-passive event listener',
    'WebExtension context not found',
    'content script',
    'background script',
    'extension'
  ];

  return extensionErrorPatterns.some(pattern =>
    errorMessage.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * 全局错误处理器
 * @param {ErrorEvent} event 错误事件
 * @returns {boolean} 是否阻止默认行为
 */
export function handleGlobalError(event) {
  if (isExtensionError(event.message)) {
    console.warn('忽略浏览器扩展错误:', event.message);
    event.preventDefault();
    return false;
  }

  // 记录其他错误
  console.error('全局错误:', event.error || event.message);
  return true;
}

/**
 * 未捕获Promise拒绝处理器
 * @param {PromiseRejectionEvent} event Promise拒绝事件
 * @returns {boolean} 是否阻止默认行为
 */
export function handleUnhandledRejection(event) {
  if (isExtensionError(event.reason)) {
    console.warn('忽略浏览器扩展Promise拒绝:', event.reason);
    event.preventDefault();
    return false;
  }

  // 记录其他Promise拒绝
  console.error('未捕获的Promise拒绝:', event.reason);
  return true;
}

/**
 * 安装全局错误处理器
 */
export function installGlobalErrorHandlers() {
  // 处理JavaScript错误
  window.addEventListener('error', handleGlobalError);

  // 处理未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', handleUnhandledRejection);

  console.log('全局错误处理器已安装');
}

/**
 * 卸载全局错误处理器
 */
export function uninstallGlobalErrorHandlers() {
  window.removeEventListener('error', handleGlobalError);
  window.removeEventListener('unhandledrejection', handleUnhandledRejection);

  console.log('全局错误处理器已卸载');
}

/**
 * 安全的异步函数包装器
 * @param {Function} asyncFn 异步函数
 * @param {string} context 上下文描述
 * @returns {Function} 包装后的函数
 */
export function safeAsync(asyncFn, context = '异步操作') {
  return async function (...args) {
    try {
      return await asyncFn.apply(this, args);
    } catch (error) {
      if (isExtensionError(error)) {
        console.warn(`${context}中忽略扩展错误:`, error.message);
        return null;
      }

      console.error(`${context}失败:`, error);
      throw error;
    }
  };
}

/**
 * 安全的Promise包装器
 * @param {Promise} promise Promise对象
 * @param {string} context 上下文描述
 * @returns {Promise} 包装后的Promise
 */
export function safePromise(promise, context = 'Promise操作') {
  return promise.catch(error => {
    if (isExtensionError(error)) {
      console.warn(`${context}中忽略扩展错误:`, error.message);
      return null;
    }

    console.error(`${context}失败:`, error);
    throw error;
  });
}

/**
 * 创建带有错误处理的Vue组件混入
 */
export const errorHandlerMixin = {
  mounted() {
    this._handleGlobalError = (event) => {
      if (isExtensionError(event.message)) {
        console.warn('组件中忽略扩展错误:', event.message);
        event.preventDefault();
        return false;
      }
    };

    this._handleUnhandledRejection = (event) => {
      if (isExtensionError(event.reason)) {
        console.warn('组件中忽略扩展Promise拒绝:', event.reason);
        event.preventDefault();
        return false;
      }
    };

    window.addEventListener('error', this._handleGlobalError);
    window.addEventListener('unhandledrejection', this._handleUnhandledRejection);
  },

  beforeUnmount() {
    if (this._handleGlobalError) {
      window.removeEventListener('error', this._handleGlobalError);
    }
    if (this._handleUnhandledRejection) {
      window.removeEventListener('unhandledrejection', this._handleUnhandledRejection);
    }
  }
};

export default {
  isExtensionError,
  handleGlobalError,
  handleUnhandledRejection,
  installGlobalErrorHandlers,
  uninstallGlobalErrorHandlers,
  safeAsync,
  safePromise,
  errorHandlerMixin
};
