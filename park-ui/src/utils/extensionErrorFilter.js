/**
 * 浏览器扩展错误过滤工具
 * 用于过滤和处理浏览器扩展产生的错误，避免影响用户体验
 */

/**
 * 检查是否为浏览器扩展错误
 * @param {Error|Object|String} error - 错误对象或错误消息
 * @returns {boolean} 是否为扩展错误
 */
export function isExtensionError(error) {
  if (!error) return false;
  
  // 获取错误消息
  let message = '';
  if (typeof error === 'string') {
    message = error;
  } else if (error.message) {
    message = error.message;
  } else if (error.toString) {
    message = error.toString();
  }
  
  // 浏览器扩展错误特征
  const extensionErrorPatterns = [
    // Chrome扩展错误
    'A listener indicated an asynchronous response by returning true',
    'message channel closed before a response was received',
    'Extension context invalidated',
    'Could not establish connection',
    'Receiving end does not exist',
    'runtime.lastError',
    'chrome-extension://',
    'Unchecked runtime.lastError',
    
    // Firefox扩展错误
    'moz-extension://',
    'WebExtension context not found',
    
    // 通用扩展错误
    'Extension',
    'extension',
    'content script',
    'background script'
  ];
  
  return extensionErrorPatterns.some(pattern =>
    message.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * 安装全局扩展错误过滤器
 */
export function installExtensionErrorFilter() {
  // 过滤未捕获的Promise错误
  const handleUnhandledRejection = (event) => {
    const error = event.reason;
    if (isExtensionError(error)) {
      event.preventDefault();
      console.debug('已过滤浏览器扩展错误:', error);
      return;
    }
  };
  
  // 过滤全局错误
  const handleGlobalError = (event) => {
    const error = event.error || event.reason || event.message;
    if (isExtensionError(error)) {
      event.preventDefault();
      console.debug('已过滤浏览器扩展错误:', error);
      return;
    }
  };
  
  // 重写console.error以过滤扩展错误
  const originalConsoleError = console.error;
  const filteredConsoleError = function(...args) {
    const message = args.join(' ');
    if (isExtensionError(message)) {
      console.debug('已过滤控制台扩展错误:', message);
      return;
    }
    originalConsoleError.apply(console, args);
  };
  
  // 安装事件监听器
  window.addEventListener('unhandledrejection', handleUnhandledRejection);
  window.addEventListener('error', handleGlobalError);
  console.error = filteredConsoleError;
  
  // 返回清理函数
  return function cleanup() {
    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    window.removeEventListener('error', handleGlobalError);
    console.error = originalConsoleError;
  };
}

/**
 * 包装异步函数，自动过滤扩展错误
 * @param {Function} asyncFn - 异步函数
 * @returns {Function} 包装后的函数
 */
export function wrapAsyncFunction(asyncFn) {
  return async function(...args) {
    try {
      return await asyncFn.apply(this, args);
    } catch (error) {
      if (isExtensionError(error)) {
        console.debug('已过滤异步函数中的扩展错误:', error);
        return;
      }
      throw error;
    }
  };
}

/**
 * 包装Promise，自动过滤扩展错误
 * @param {Promise} promise - Promise对象
 * @returns {Promise} 包装后的Promise
 */
export function wrapPromise(promise) {
  return promise.catch(error => {
    if (isExtensionError(error)) {
      console.debug('已过滤Promise中的扩展错误:', error);
      return;
    }
    throw error;
  });
}

// 默认导出
export default {
  isExtensionError,
  installExtensionErrorFilter,
  wrapAsyncFunction,
  wrapPromise
};
