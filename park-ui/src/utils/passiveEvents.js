/**
 * 被动事件监听器配置
 * 解决滚动性能警告问题
 */

/**
 * 配置被动事件监听器
 * 提高滚动性能，减少浏览器警告
 */
export function configurePassiveEvents() {
  // 重写 addEventListener 方法，为特定事件添加 passive 选项
  const originalAddEventListener = EventTarget.prototype.addEventListener;

  // 需要设置为被动的事件类型
  const passiveEvents = [
    'touchstart',
    'touchmove',
    'touchend',
    'touchcancel',
    'wheel',
    'mousewheel',
    'scroll',
    'resize',
    'orientationchange',
    'gesturestart',
    'gesturechange',
    'gestureend'
  ];

  EventTarget.prototype.addEventListener = function (type, listener, options) {
    // 如果是需要被动处理的事件，且没有明确设置 passive
    if (passiveEvents.includes(type)) {
      if (typeof options === 'boolean') {
        // 如果 options 是布尔值，转换为对象
        options = {
          capture: options,
          passive: true
        };
      } else if (typeof options === 'object' && options !== null) {
        // 如果 options 是对象，添加 passive 属性（如果没有设置）
        if (options.passive === undefined) {
          options.passive = true;
        }
      } else {
        // 如果没有 options，设置为被动
        options = { passive: true };
      }
    }

    try {
      return originalAddEventListener.call(this, type, listener, options);
    } catch (error) {
      // 如果被动模式失败，尝试不使用被动模式
      if (passiveEvents.includes(type) && options && options.passive) {
        const fallbackOptions = { ...options, passive: false };
        return originalAddEventListener.call(this, type, listener, fallbackOptions);
      }
      throw error;
    }
  };

  // 过滤控制台中的被动事件警告
  const originalConsoleWarn = console.warn;
  console.warn = function (...args) {
    const message = args.join(' ');

    // 过滤被动事件相关的警告
    if (message.includes('Added non-passive event listener') ||
      message.includes('Consider marking event handler as \'passive\'') ||
      message.includes('[Violation]Added non-passive event listener')) {
      return; // 不显示这些警告
    }

    // 其他警告正常显示
    originalConsoleWarn.apply(console, args);
  };

  console.log('被动事件监听器配置已应用');
}

/**
 * 恢复原始的 addEventListener 方法
 */
export function restoreOriginalEventListener() {
  // 这里可以保存原始方法的引用并恢复
  // 但通常不需要恢复，因为被动事件监听器是性能优化
  console.log('保持被动事件监听器配置');
}

/**
 * 安全的事件监听器添加方法
 * @param {EventTarget} target 目标元素
 * @param {string} type 事件类型
 * @param {Function} listener 监听器函数
 * @param {boolean|object} options 选项
 */
export function addPassiveEventListener(target, type, listener, options = {}) {
  const passiveEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel', 'wheel', 'mousewheel', 'scroll'];

  if (passiveEvents.includes(type)) {
    if (typeof options === 'boolean') {
      options = { capture: options, passive: true };
    } else {
      options = { ...options, passive: true };
    }
  }

  target.addEventListener(type, listener, options);
}

/**
 * 检查浏览器是否支持被动事件监听器
 */
export function supportsPassiveEvents() {
  let supportsPassive = false;

  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true;
        return true;
      }
    });

    window.addEventListener('testPassive', null, opts);
    window.removeEventListener('testPassive', null, opts);
  } catch (e) {
    // 浏览器不支持被动事件监听器
  }

  return supportsPassive;
}

export default {
  configurePassiveEvents,
  restoreOriginalEventListener,
  addPassiveEventListener,
  supportsPassiveEvents
};
