<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <!-- VIP卡片背景 -->
  <rect x="128" y="320" width="768" height="384" rx="32" fill="currentColor"/>
  
  <!-- 卡片光泽效果 -->
  <rect x="128" y="320" width="768" height="96" rx="32" fill="white" opacity="0.1"/>
  
  <!-- 皇冠图标 -->
  <g transform="translate(200, 380)">
    <!-- 皇冠主体 -->
    <path d="M0 80 L20 20 L40 60 L60 0 L80 60 L100 20 L120 80 Z" fill="#FFD700"/>
    <!-- 皇冠底座 -->
    <rect x="0" y="80" width="120" height="20" rx="10" fill="#FFD700"/>
    <!-- 宝石装饰 -->
    <circle cx="30" cy="50" r="8" fill="#FF6B6B"/>
    <circle cx="60" cy="30" r="10" fill="#4ECDC4"/>
    <circle cx="90" cy="50" r="8" fill="#FF6B6B"/>
  </g>
  
  <!-- VIP文字 -->
  <text x="512" y="520" font-family="Arial, sans-serif" font-size="80" font-weight="bold" fill="white" text-anchor="middle">VIP</text>
  
  <!-- 装饰性星星 -->
  <g fill="white" opacity="0.6">
    <path d="M680 400 L685 410 L695 410 L687 418 L690 428 L680 422 L670 428 L673 418 L665 410 L675 410 Z"/>
    <path d="M720 450 L723 456 L729 456 L724 461 L726 467 L720 464 L714 467 L716 461 L711 456 L717 456 Z"/>
    <path d="M750 420 L753 426 L759 426 L754 431 L756 437 L750 434 L744 437 L746 431 L741 426 L747 426 Z"/>
  </g>
  
  <!-- 卡片边框装饰 -->
  <rect x="128" y="320" width="768" height="384" rx="32" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>
