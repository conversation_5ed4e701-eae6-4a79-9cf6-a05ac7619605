<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <!-- 平台基座 -->
  <rect x="128" y="640" width="768" height="128" rx="16" fill="currentColor"/>
  
  <!-- 支柱 -->
  <rect x="200" y="400" width="80" height="240" fill="currentColor"/>
  <rect x="372" y="320" width="80" height="320" fill="currentColor"/>
  <rect x="544" y="280" width="80" height="360" fill="currentColor"/>
  <rect x="716" y="360" width="80" height="280" fill="currentColor"/>
  
  <!-- 平台顶部 -->
  <rect x="160" y="360" width="640" height="40" rx="8" fill="currentColor"/>
  
  <!-- 连接线/网络 -->
  <g stroke="currentColor" stroke-width="3" fill="none" opacity="0.6">
    <line x1="240" y1="380" x2="412" y2="340"/>
    <line x1="412" y1="340" x2="584" y2="300"/>
    <line x1="584" y1="300" x2="756" y2="380"/>
    <line x1="240" y1="380" x2="584" y2="300"/>
    <line x1="412" y1="340" x2="756" y2="380"/>
  </g>
  
  <!-- 节点圆点 -->
  <circle cx="240" cy="380" r="12" fill="white"/>
  <circle cx="412" cy="340" r="12" fill="white"/>
  <circle cx="584" cy="300" r="12" fill="white"/>
  <circle cx="756" cy="380" r="12" fill="white"/>
  
  <!-- 数据流动效果 -->
  <g fill="white" opacity="0.8">
    <circle cx="300" cy="365" r="4"/>
    <circle cx="480" cy="325" r="4"/>
    <circle cx="650" cy="335" r="4"/>
  </g>
  
  <!-- 顶部云朵图标 -->
  <g transform="translate(512, 200)">
    <path d="M-80 0 C-80 -44 -44 -80 0 -80 C22 -80 42 -68 54 -50 C66 -58 80 -60 94 -54 C108 -48 116 -32 116 -16 C116 0 100 16 84 16 L-64 16 C-72 16 -80 8 -80 0 Z" fill="currentColor" opacity="0.3"/>
  </g>
</svg>
