<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="协议标题" prop="agreementTitle">
        <el-input
          v-model="queryParams.agreementTitle"
          placeholder="请输入协议标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="协议类型" prop="agreementType">
        <el-select v-model="queryParams.agreementType" placeholder="请选择协议类型" clearable>
          <el-option
            v-for="dict in agreement_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:agreement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:agreement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:agreement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:agreement:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Refresh"
          @click="handleCleanContent"
          v-hasPermi="['system:agreement:edit']"
        >清理格式</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="agreementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="协议ID" align="center" prop="id" />
      <el-table-column label="协议标题" align="center" prop="agreementTitle" :show-overflow-tooltip="true" />
      <el-table-column label="协议类型" align="center" prop="agreementType">
        <template #default="scope">
          <dict-tag :options="agreement_type" :value="scope.row.agreementType"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['system:agreement:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:agreement:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:agreement:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改系统协议对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="90%"
      top="5vh"
      append-to-body
      class="edit-agreement-dialog"
      :style="{ maxWidth: '1200px', height: '85vh', maxHeight: '85vh' }"
      :close-on-click-modal="false"
    >
      <div class="edit-dialog-container">
        <!-- 固定的表单区域 -->
        <div class="form-header">
          <el-form ref="agreementRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="协议类型" prop="agreementType">
              <el-select v-model="form.agreementType" placeholder="请选择协议类型">
                <el-option
                  v-for="dict in agreement_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="协议标题" prop="agreementTitle">
              <el-input v-model="form.agreementTitle" placeholder="请输入协议标题" />
            </el-form-item>
          </el-form>
        </div>

        <!-- 可滚动的编辑器区域 -->
        <div class="editor-container">
          <div class="editor-label">协议内容</div>
          <div class="editor-wrapper">
            <editor
              v-model="form.agreementContent"
              :min-height="200"
              :height="null"
              :options="editorOptions"
              style="height: 100%; overflow: visible;"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 协议详情对话框 -->
    <el-dialog
      title="协议详情"
      v-model="viewOpen"
      width="90%"
      top="5vh"
      append-to-body
      destroy-on-close
      class="agreement-dialog"
      :style="{ maxWidth: '1200px', height: '85vh', maxHeight: '85vh' }"
      :close-on-click-modal="false"
    >
      <div class="agreement-detail-container">
        <el-descriptions :column="2" border class="agreement-info">
          <el-descriptions-item label="协议标题">{{ viewForm.agreementTitle }}</el-descriptions-item>
          <el-descriptions-item label="协议类型">
            <dict-tag :options="agreement_type" :value="viewForm.agreementType"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">协议内容</el-divider>

        <div class="agreement-content-wrapper">
          <div
            class="agreement-content"
            style="height: 50vh; overflow-y: auto; padding: 20px; border: 1px solid #dcdfe6; border-radius: 4px; background-color: #fafafa; line-height: 1.8; font-size: 14px; white-space: pre-wrap; word-wrap: break-word;"
            v-html="formatAgreementContent(viewForm.agreementContent)"
          ></div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Agreement">
import { listAgreement, getAgreement, delAgreement, addAgreement, updateAgreement } from "@/api/system/agreement";

const { proxy } = getCurrentInstance();
const { agreement_type } = proxy.useDict('agreement_type');

const agreementList = ref([]);
const open = ref(false);
const viewOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  viewForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    agreementTitle: null,
    agreementType: null,
    deleteFlag: 0
  },
  rules: {
    agreementType: [
      { required: true, message: "协议类型不能为空", trigger: "change" }
    ],
    agreementTitle: [
      { required: true, message: "协议标题不能为空", trigger: "blur" }
    ],
    agreementContent: [
      { required: true, message: "协议内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, viewForm, rules } = toRefs(data);



// 富文本编辑器配置
const editorOptions = ref({
  placeholder: '请输入协议内容...',
  scroll: true,
  maxLength: 50000,
  MENU_CONF: {
    // 配置字体
    fontSize: {
      fontSizeList: ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px']
    },
    // 配置字体族
    fontFamily: {
      fontFamilyList: [
        '黑体',
        '仿宋',
        '楷体',
        '标楷体',
        '华文仿宋',
        '华文楷体',
        '宋体',
        'Arial',
        'Tahoma',
        'Verdana'
      ]
    }
  }
});

/** 查询系统协议列表 */
function getList() {
  loading.value = true;
  listAgreement(queryParams.value).then(response => {
    agreementList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    agreementType: null,
    agreementTitle: null,
    agreementContent: null,
    deleteFlag: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("agreementRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加系统协议";
  // 延迟设置编辑器滚动
  nextTick(() => {
    fixEditorScroll();
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getAgreement(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改系统协议";
    // 延迟设置编辑器滚动
    nextTick(() => {
      fixEditorScroll();
    });
  });
}

/** 查看按钮操作 */
function handleView(row) {
  viewForm.value = {};
  const _id = row.id;
  getAgreement(_id).then(response => {
    viewForm.value = response.data;
    viewOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["agreementRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAgreement(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('更新协议失败:', error);
          proxy.$modal.msgError("更新失败：" + (error.response?.data?.msg || error.message));
        });
      } else {
        addAgreement(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('新增协议失败:', error);
          proxy.$modal.msgError("新增失败：" + (error.response?.data?.msg || error.message));
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除系统协议编号为"' + _ids + '"的数据项？').then(function() {
    return delAgreement(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/agreement/export', {
    ...queryParams.value
  }, `agreement_${new Date().getTime()}.xlsx`)
}

/** 格式化协议内容 */
function formatAgreementContent(content) {
  if (!content) return '';

  // 保留格式的清理
  let formatted = content
    // 移除完全空的p标签
    .replace(/<p[^>]*>\s*<\/p>/gi, '')
    // 移除只包含&nbsp;的p标签
    .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi, '')
    // 给p标签添加适当的样式，保持段落间距
    .replace(/<p([^>]*)>/gi, '<p$1 style="margin: 8px 0; line-height: 1.6;">')
    // 保留文本对齐样式
    .replace(/text-align:\s*center/gi, 'text-align: center !important')
    .replace(/text-align:\s*left/gi, 'text-align: left !important')
    .replace(/text-align:\s*right/gi, 'text-align: right !important')
    // 减少过多的&nbsp;但保留一些空格
    .replace(/&nbsp;{4,}/g, '&nbsp;&nbsp;&nbsp;')
    // 保留强调标签
    .replace(/<strong([^>]*)>/gi, '<strong$1 style="font-weight: bold;">')
    // 清理过多的连续空白，但不要太激进
    .replace(/\s{3,}/g, ' ');

  return formatted;
}

/** 修复编辑器滚动问题 */
function fixEditorScroll() {
  setTimeout(() => {
    // 针对 Quill 编辑器的修复
    const editorWrapper = document.querySelector('.editor-wrapper');
    if (editorWrapper) {
      // 设置编辑器容器
      const editor = editorWrapper.querySelector('.editor');
      if (editor) {
        editor.style.height = '100%';
        editor.style.display = 'flex';
        editor.style.flexDirection = 'column';
      }

      // 设置 Quill 容器
      const quillContainer = editorWrapper.querySelector('.ql-container');
      if (quillContainer) {
        quillContainer.style.flex = '1';
        quillContainer.style.overflowY = 'auto';
        quillContainer.style.height = 'auto';
        quillContainer.style.maxHeight = 'none';
      }

      // 设置编辑区域
      const qlEditor = editorWrapper.querySelector('.ql-editor');
      if (qlEditor) {
        qlEditor.style.overflowY = 'auto';
        qlEditor.style.maxHeight = 'none';
        qlEditor.style.height = 'auto';
        qlEditor.style.minHeight = '200px';
      }

      // 设置工具栏
      const toolbar = editorWrapper.querySelector('.ql-toolbar');
      if (toolbar) {
        toolbar.style.position = 'sticky';
        toolbar.style.top = '0';
        toolbar.style.zIndex = '100';
        toolbar.style.backgroundColor = '#fff';
        toolbar.style.flexShrink = '0';
      }
    }
  }, 500);
}

/** 清理所有协议内容格式 */
function handleCleanContent() {
  proxy.$modal.confirm('此操作将清理所有协议的格式问题（移除多余空行），是否继续？').then(function() {
    loading.value = true;

    // 获取所有协议数据
    listAgreement({ pageNum: 1, pageSize: 1000, deleteFlag: 0 }).then(response => {
      const agreements = response.rows;
      let updatePromises = [];

      agreements.forEach(agreement => {
        if (agreement.agreementContent) {
          const cleanedContent = formatAgreementContent(agreement.agreementContent);
          if (cleanedContent !== agreement.agreementContent) {
            agreement.agreementContent = cleanedContent;
            updatePromises.push(updateAgreement(agreement));
          }
        }
      });

      if (updatePromises.length > 0) {
        Promise.all(updatePromises).then(() => {
          proxy.$modal.msgSuccess(`成功清理了 ${updatePromises.length} 个协议的格式`);
          getList();
        }).catch(() => {
          proxy.$modal.msgError("清理格式时发生错误");
        }).finally(() => {
          loading.value = false;
        });
      } else {
        proxy.$modal.msgInfo("没有需要清理的内容");
        loading.value = false;
      }
    }).catch(() => {
      loading.value = false;
    });
  }).catch(() => {});
}



getList();

// 监听弹窗打开状态，修复编辑器滚动
watch(open, (newVal) => {
  if (newVal) {
    nextTick(() => {
      fixEditorScroll();
      // 多次尝试修复，确保生效
      setTimeout(() => fixEditorScroll(), 1000);
      setTimeout(() => fixEditorScroll(), 2000);
    });
  }
});
</script>

<style>
/* 全局样式，确保对话框高度限制生效 */
.agreement-dialog .el-dialog {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  height: 90vh !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.agreement-dialog .el-dialog__body {
  flex: 1 !important;
  padding: 20px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
  height: 100% !important;
}

/* 编辑弹窗样式 */
.edit-agreement-dialog .el-dialog {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  height: 85vh !important;
  max-height: 85vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.edit-agreement-dialog .el-dialog__body {
  flex: 1 !important;
  padding: 20px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  height: calc(85vh - 120px) !important;
}

/* 编辑对话框容器 */
.edit-dialog-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 固定的表单头部 */
.form-header {
  flex-shrink: 0;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
  height: auto;
}

/* 编辑器容器 */
.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  height: calc(100% - 140px);
}

.editor-label {
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 700;
  flex-shrink: 0;
}

.editor-wrapper {
  flex: 1;
  height: calc(100% - 40px);
  overflow: hidden;
  position: relative;
}

.editor-wrapper :deep(*) {
  box-sizing: border-box;
}

/* Quill 编辑器样式优化 */
.editor-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-wrapper :deep(.editor) {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.editor-wrapper :deep(.quill-editor) {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.editor-wrapper :deep(.ql-container) {
  flex: 1 !important;
  overflow-y: auto !important;
  border-bottom: 1px solid #ccc !important;
}

.editor-wrapper :deep(.ql-editor) {
  overflow-y: auto !important;
  max-height: none !important;
  height: auto !important;
  min-height: 200px !important;
}

.editor-wrapper :deep(.ql-toolbar) {
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  background: #fff !important;
  border-bottom: 1px solid #ccc !important;
  flex-shrink: 0 !important;
}

/* 额外的滚动修复 */
.editor-wrapper :deep(.w-e-text-container .w-e-scroll) {
  overflow-y: auto !important;
  height: auto !important;
  max-height: none !important;
}
</style>

<style scoped>
/* 协议详情对话框样式 */
.agreement-dialog :deep(.el-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  height: 90vh !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.agreement-dialog :deep(.el-dialog__body) {
  flex: 1 !important;
  padding: 20px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
  height: 100% !important;
}

.agreement-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.agreement-info {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.agreement-content-wrapper {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.agreement-content {
  overflow-y: auto;
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  line-height: 1.6;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
  height: 60vh;
  max-height: 60vh;
}

/* 优化协议内容显示 */
.agreement-content :deep(p) {
  margin: 8px 0 !important;
  line-height: 1.6 !important;
  text-indent: 0 !important;
}

.agreement-content :deep(p:empty) {
  display: none !important;
}

.agreement-content :deep(strong) {
  font-weight: bold !important;
}

/* 强制文本对齐样式生效 */
.agreement-content :deep(p[style*="text-align: center"]) {
  text-align: center !important;
}

.agreement-content :deep(p[style*="text-align: left"]) {
  text-align: left !important;
}

.agreement-content :deep(p[style*="text-align: right"]) {
  text-align: right !important;
}

.agreement-content :deep(h1),
.agreement-content :deep(h2),
.agreement-content :deep(h3),
.agreement-content :deep(h4),
.agreement-content :deep(h5),
.agreement-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: bold;
}

.agreement-content :deep(ul),
.agreement-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.agreement-content :deep(li) {
  margin: 4px 0;
}

/* 文本对齐样式 */
.agreement-content :deep(.text-left) {
  text-align: left !important;
}

.agreement-content :deep(.text-center) {
  text-align: center !important;
}

.agreement-content :deep(.text-right) {
  text-align: right !important;
}

.agreement-content :deep(.text-justify) {
  text-align: justify !important;
}

/* 滚动条样式优化 */
.agreement-content::-webkit-scrollbar {
  width: 8px;
}

.agreement-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.agreement-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.agreement-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agreement-dialog :deep(.el-dialog) {
    margin: 0 !important;
    height: 100vh;
    width: 100% !important;
    border-radius: 0;
  }

  .agreement-content {
    padding: 15px;
    font-size: 13px;
  }
}
</style>
