<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入运营商公司名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="primaryContactName">
        <el-input
          v-model="queryParams.primaryContactName"
          placeholder="请输入主要联系人姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="primaryContactPhone">
        <el-input
          v-model="queryParams.primaryContactPhone"
          placeholder="请输入主要联系人手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>



    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['platform:operator:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['platform:operator:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['platform:operator:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['platform:operator:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="operatorList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="运营商ID" align="center" prop="id" /> -->
      <el-table-column
        label="公司名称"
        align="center"
        prop="companyName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="主要联系人"
        align="center"
        prop="primaryContactName"
      />
      <el-table-column
        label="联系手机号"
        align="center"
        prop="primaryContactPhone"
      />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        width="500"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:operator:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['platform:operator:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改运营商信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="operatorRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="公司名称" prop="companyName">
          <el-input
            v-model="form.companyName"
            placeholder="请输入运营商公司名称"
          />
        </el-form-item>
        <el-form-item label="主要联系人" prop="primaryContactName">
          <el-input
            v-model="form.primaryContactName"
            placeholder="请输入主要联系人姓名"
          />
        </el-form-item>
        <el-form-item label="联系手机号" prop="primaryContactPhone">
          <el-input
            v-model="form.primaryContactPhone"
            placeholder="请输入主要联系人手机号"
            maxlength="11"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Operator">
import {
  listOperator,
  getOperator,
  delOperator,
  addOperator,
  updateOperator,
} from "@/api/platform/operator";
import CustomPagination from "@/components/CustomPagination/index.vue";

const { proxy } = getCurrentInstance();

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const operatorList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    companyName: null,
    primaryContactName: null,
    primaryContactPhone: null,
  },
  rules: {
    companyName: [
      { required: true, message: "运营商公司名称不能为空", trigger: "blur" },
    ],
    primaryContactName: [
      { required: true, message: "主要联系人姓名不能为空", trigger: "blur" },
    ],
    primaryContactPhone: [
      { required: true, message: "主要联系人手机号不能为空", trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);



/** 查询运营商信息列表 */
function getList() {
  loading.value = true;
  listOperator(queryParams.value).then((response) => {
    operatorList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    companyName: null,
    primaryContactName: null,
    primaryContactPhone: null,
    remark: null,
  };
  proxy.resetForm("operatorRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    companyName: null,
    primaryContactName: null,
    primaryContactPhone: null,
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加运营商信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getOperator(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改运营商信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["operatorRef"].validate((valid) => {
    if (valid) {
      // 处理创建人和更新人字段
      const submitData = { ...form.value };

      try {
        const userStore = proxy.$store.state.user;
        if (userStore && userStore.id) {
          // 新增时设置创建人
          if (!form.value.id) {
            submitData.createdBy = userStore.id;
          }
          // 修改时设置更新人
          submitData.updatedBy = userStore.id;
        }
      } catch (error) {
        console.warn('获取用户ID失败:', error);
      }

      if (form.value.id != null) {
        updateOperator(submitData).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addOperator(submitData).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除运营商信息编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delOperator(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/platform/operator/export",
    {
      ...queryParams.value,
    },
    `operator_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>

<style scoped>
/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

/* 查询表单样式 */
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}

/* 按钮样式 */
.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}
</style>
