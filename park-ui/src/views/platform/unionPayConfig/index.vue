<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="场库" prop="warehouseId">
        <el-cascader
          v-model="queryParams.warehouseId"
          :options="warehouseCascaderOptions"
          :props="{
            value: 'value',
            label: 'label',
            children: 'children',
            emitPath: false,
            checkStrictly: true,
            expandTrigger: 'hover'
          }"
          placeholder="请选择场库"
          style="width: 200px"
          clearable
          filterable
          :show-all-levels="false"
        />
      </el-form-item>
      <el-form-item label="支付类型" prop="payType">
        <el-select v-model="queryParams.payType" placeholder="请选择支付类型" clearable style="width: 200px">
          <el-option v-for="dict in pay_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="商户号" prop="mid">
        <el-input v-model="queryParams.mid" placeholder="请输入商户号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="终端号" prop="tid">
        <el-input v-model="queryParams.tid" placeholder="请输入终端号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['platform:unionPayConfig:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['platform:unionPayConfig:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['platform:unionPayConfig:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['platform:unionPayConfig:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="unionPayConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="银联配置ID" align="center" prop="id" v-if="false" />
      <el-table-column label="场库名称" align="center" prop="warehouseName" />
      <el-table-column label="商户号" align="center" prop="mid" />
      <el-table-column label="终端号" align="center" prop="tid" />
            <el-table-column label="支付类型" align="center" prop="payType">
        <template #default="scope">
          <dict-tag :options="pay_type" :value="scope.row.payType" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:unionPayConfig:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['platform:unionPayConfig:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <CustomPagination :total="total" v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改银联配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="unionPayConfigRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="场库" prop="warehouseId">
          <el-cascader
            v-model="form.warehouseId"
            :options="warehouseCascaderOptions"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
              emitPath: false,
              checkStrictly: true,
              expandTrigger: 'hover'
            }"
            placeholder="请选择场库或停车场"
            style="width: 100%"
            clearable
            filterable
            :show-all-levels="false"
          />
        </el-form-item>
        <el-form-item label="支付类型" prop="payType">
          <el-select v-model="form.payType" placeholder="请选择支付类型" style="width: 100%">
            <el-option v-for="dict in pay_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
        <el-form-item label="商户号" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入商户号" />
        </el-form-item>
        <el-form-item label="终端号" prop="tid">
          <el-input v-model="form.tid" placeholder="请输入终端号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UnionPayConfig">
import {
  listUnionPayConfig,
  getUnionPayConfig,
  delUnionPayConfig,
  addUnionPayConfig,
  updateUnionPayConfig,
  exportUnionPayConfig,
  checkTidUnique
} from "@/api/platform/unionPayConfig";
import { optionSelectWarehouse } from "@/api/platform/warehouse";
import { getWarehouseOptions } from "@/api/vip/member";
import CustomPagination from "@/components/CustomPagination/index.vue";

const { proxy } = getCurrentInstance();
const { pay_type } = proxy.useDict("pay_type");

const unionPayConfigList = ref([]);
const warehouseOptions = ref([]);
const warehouseCascaderOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    warehouseId: null,
    payType: null,
    mid: null,
    tid: null
  },
  rules: {
    warehouseId: [
      { required: true, message: "场库不能为空", trigger: "change" }
    ],
    payType: [
      { required: true, message: "支付类型不能为空", trigger: "change" }
    ],
    mid: [
      { required: true, message: "商户号不能为空", trigger: "blur" },
      { min: 1, max: 32, message: "商户号长度必须介于 1 和 32 之间", trigger: "blur" }
    ],
    tid: [
      { required: true, message: "终端号不能为空", trigger: "blur" },
      { min: 1, max: 32, message: "终端号长度必须介于 1 和 32 之间", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value) {
            checkTidUnique(value, form.id).then(response => {
              if (!response.data) {
                callback(new Error("终端号已存在"));
              } else {
                callback();
              }
            });
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询银联配置列表 */
function getList() {
  loading.value = true;
  listUnionPayConfig(queryParams.value).then(response => {
    unionPayConfigList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    warehouseId: null,
    payType: null,
    mid: null,
    tid: null,
    remark: null
  };
  proxy.resetForm("unionPayConfigRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  loadWarehouseOptions();
  open.value = true;
  title.value = "添加银联配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  loadWarehouseOptions();
  const _id = row.id || ids.value;
  getUnionPayConfig(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改银联配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["unionPayConfigRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateUnionPayConfig(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addUnionPayConfig(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除银联配置编号为"' + _ids + '"的数据项？').then(function () {
    return delUnionPayConfig(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/platform/unionPayConfig/export", {
    ...queryParams.value
  }, `unionPayConfig_${new Date().getTime()}.xlsx`);
}

/** 获取场库选项 */
function loadWarehouseOptions() {
  getWarehouseOptions().then((response) => {
    warehouseOptions.value = response.data;
    // 同时构建层级选项（用于cascader）
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(response.data);
  });
}

/** 构建级联选择器选项 */
function buildWarehouseCascaderOptions(warehouses) {
  if (!warehouses || warehouses.length === 0) {
    return [];
  }

  // 分离主场库和子场库
  const mainWarehouses = warehouses.filter(w => w.parentId === "0");
  const subWarehouses = warehouses.filter(w => w.parentId !== "0");

  // 构建级联结构
  return mainWarehouses.map(mainWarehouse => {
    const children = subWarehouses
      .filter(sub => sub.parentId === mainWarehouse.id)
      .map(sub => ({
        value: sub.id,
        label: sub.warehouseName,
        isLeaf: true
      }));

    return {
      value: mainWarehouse.id,
      label: mainWarehouse.warehouseName,
      children: children.length > 0 ? children : undefined
    };
  });
}

onMounted(() => {
  loadWarehouseOptions();
  getList();
});
</script>
