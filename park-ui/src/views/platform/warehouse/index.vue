<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
            <el-form-item label="运营商" prop="operatorId">
        <el-select
          v-model="queryParams.operatorId"
          placeholder="请选择运营商"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="operator in operatorOptions"
            :key="operator.id"
            :label="operator.companyName"
            :value="operator.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场库名称" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入场库名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in warehouse_status"
            v-if="warehouse_status && warehouse_status.length > 0"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
          <el-option v-if="!warehouse_status || warehouse_status.length === 0" disabled value="">字典数据加载中...</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['platform:warehouse:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['platform:warehouse:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['platform:warehouse:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['platform:warehouse:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="warehouseList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="运营商"
        align="center"
        prop="operatorName"
        :show-overflow-tooltip="true"
        width="300"
      />
      <el-table-column
        label="场库名称"
        align="center"
        prop="warehouseName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="详细地址"
        align="center"
        prop="address"
        :show-overflow-tooltip="true"
        width="300"
      />
      <!-- <el-table-column
        label="租赁物业编号"
        align="center"
        prop="leasePropertyNo"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        label="租赁地址"
        align="center"
        prop="leaseAddress"
        :show-overflow-tooltip="true"
        width="200"
      />
      <el-table-column
        label="租赁详细地址"
        align="center"
        prop="leaseDetailAddress"
        :show-overflow-tooltip="true"
        width="250"
      /> -->
      <el-table-column label="总车位" align="center" prop="totalParking" />
      <!-- <el-table-column
        label="类型"
        align="center"
        width="100"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.parentId == 0 || scope.row.parentId === null || scope.row.parentId === undefined" type="primary">主场库</el-tag>
          <el-tag v-else type="success">子场库</el-tag>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="轮播图"
        align="center"
        width="220"
      >
        <template #default="scope">
          <div v-if="getCarouselImages(scope.row.carouselImages).length > 0" class="table-carousel-container">
            <div class="carousel-wrapper">
              <el-carousel
                v-if="getCarouselImages(scope.row.carouselImages).length > 1"
                height="90px"
                :interval="4000"
                indicator-position="none"
                arrow="hover"
                class="table-carousel"
              >
                <el-carousel-item
                  v-for="(image, index) in getCarouselImages(scope.row.carouselImages)"
                  :key="index"
                >
                  <el-image
                    :src="image"
                    :preview-src-list="getCarouselImages(scope.row.carouselImages)"
                    fit="cover"
                    class="carousel-image-item"
                    :preview-teleported="true"
                  />
                </el-carousel-item>
              </el-carousel>
              <el-image
                v-else
                :src="getCarouselImages(scope.row.carouselImages)[0]"
                :preview-src-list="getCarouselImages(scope.row.carouselImages)"
                fit="cover"
                class="single-image"
                :preview-teleported="true"
              />
              <div class="image-badge">
                <el-icon class="badge-icon"><Picture /></el-icon>
                <span class="badge-text">{{ getCarouselImages(scope.row.carouselImages).length }}</span>
              </div>
              <div v-if="getCarouselImages(scope.row.carouselImages).length > 1" class="custom-indicators">
                <span
                  v-for="n in getCarouselImages(scope.row.carouselImages).length"
                  :key="n"
                  class="indicator-dot"
                ></span>
              </div>
            </div>
          </div>
          <div v-else class="no-image-container">
            <el-icon class="no-image-icon"><Picture /></el-icon>
            <span class="no-image-text">暂无图片</span>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        width="120"
      >
        <template #default="scope">
          <dict-tag
            :options="warehouse_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="200"
      >
        <template #default="scope">

          <!-- <el-button
            link
            type="primary"
            icon="View"
            @click="handleViewChildWarehouses(scope.row)"
            >子场库</el-button
          > -->
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:warehouse:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['platform:warehouse:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改场库信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="warehouseRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="场库名称" prop="warehouseName">
              <el-input
                v-model="form.warehouseName"
                placeholder="请输入场库名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="warehouseType">
              <el-input
                v-model="warehouseTypeDisplay"
                readonly
                placeholder="类型"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属运营商" prop="operatorId">
              <el-select
                v-model="form.operatorId"
                placeholder="请选择运营商"
                style="width: 100%"
                popper-class="operator-select-dropdown"
              >
                <el-option
                  v-for="operator in operatorOptions"
                  :key="operator.id"
                  :label="operator.companyName"
                  :value="operator.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option
                  v-for="dict in warehouse_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.warehouseType === 'child'">
          <el-col :span="12">
            <el-form-item label="所属场库" prop="parentId">
              <el-select
                v-model="form.parentId"
                placeholder="请选择所属场库"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="warehouse in parentWarehouseOptions"
                  :key="warehouse.id"
                  :label="warehouse.warehouseName"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="form.projectName"
                placeholder="请输入项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="智能类型" prop="smartsType">
              <el-select
                v-model="form.smartsType"
                placeholder="请选择智能类型"
              >
                <el-option label="普通停车场" :value="0" />
                <el-option label="智能停车场" :value="1" />
                <el-option label="无人值守" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="总停车位" prop="totalParking">
              <el-input-number
                v-model="form.totalParking"
                :min="0"
                placeholder="总停车位数"
              />
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="省份" prop="provinceCode">
              <el-select
                v-model="form.provinceCode"
                placeholder="请选择省份"
                clearable
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="province in provinceOptions"
                  :key="province.areaCode"
                  :label="province.areaName"
                  :value="province.areaCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市" prop="cityCode">
              <el-select
                v-model="form.cityCode"
                placeholder="请选择城市"
                clearable
                @change="handleCityChange"
                :disabled="!form.provinceCode"
              >
                <el-option
                  v-for="city in cityOptions"
                  :key="city.areaCode"
                  :label="city.areaName"
                  :value="city.areaCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区县" prop="areaCode">
              <el-select
                v-model="form.areaCode"
                placeholder="请选择区县"
                clearable
                :disabled="!form.cityCode"
              >
                <el-option
                  v-for="area in areaOptions"
                  :key="area.areaCode"
                  :label="area.areaName"
                  :value="area.areaCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>

        <!-- 租赁信息 -->
        <!-- <el-divider content-position="left">租赁信息</el-divider> -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="租赁物业编号" prop="leasePropertyNo">
              <el-input v-model="form.leasePropertyNo" placeholder="请输入租赁物业编号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="租赁地址" prop="leaseAddress">
              <el-input v-model="form.leaseAddress" placeholder="请输入租赁地址" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="租赁详细地址" prop="leaseDetailAddress">
              <el-input v-model="form.leaseDetailAddress" placeholder="请输入租赁详细地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度（-180到180）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="form.latitude" placeholder="请输入纬度（-90到90）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        </el-row>
        <el-form-item label="轮播图片" prop="carouselImages">
          <!-- 图片上传组件 -->
          <image-upload
            v-model="carouselImagesForUpload"
            :limit="6"
            :file-size="5"
            :file-type="['png', 'jpg', 'jpeg', 'gif', 'webp']"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 子场库管理弹窗 -->
    <el-dialog
      :title="`${currentComplex.warehouseName} - 子场库管理`"
      v-model="childWarehouseDialogOpen"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="child-warehouse-management">
        <!-- 操作按钮 -->
        <div class="mb-4">
          <el-button
            type="primary"
            icon="Plus"
            @click="handleAddChildWarehouse"
            v-hasPermi="['platform:warehouse:add']"
          >
            新增子场库
          </el-button>
        </div>

        <!-- 子场库列表 -->
        <el-table v-loading="childWarehouseLoading" :data="childWarehouseList" border>
          <!-- <el-table-column
            label="子场库ID"
            align="center"
            prop="id"
            width="80"
          /> -->
          <el-table-column
            label="子场库名称"
            align="center"
            prop="warehouseName"
            :show-overflow-tooltip="true"
          />


          <el-table-column label="总车位" align="center" prop="totalParking" />
          <el-table-column
            label="状态"
            align="center"
            prop="status"
            width="100"
          >
            <template #default="scope">
              <dict-tag
                :options="parking_lot_status"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdateChildWarehouse(scope.row)"
                v-hasPermi="['platform:warehouse:edit']"
              >
                修改
              </el-button>
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDeleteChildWarehouse(scope.row)"
                v-hasPermi="['platform:warehouse:remove']"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 子场库编辑弹窗 -->
    <el-dialog
      :title="childWarehouseTitle"
      v-model="childWarehouseFormOpen"
      width="1000px"
      append-to-body
    >
      <el-form
        ref="childWarehouseRef"
        :model="childWarehouseForm"
        :rules="childWarehouseRules"
        label-width="120px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="子场库名称" prop="warehouseName">
              <el-input
                v-model="childWarehouseForm.warehouseName"
                placeholder="请输入子场库名称"
              />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属运营商" prop="operatorId">
              <el-select
                v-model="childWarehouseForm.operatorId"
                placeholder="请选择运营商"
                style="width: 100%"
              >
                <el-option
                  v-for="operator in operatorOptions"
                  :key="operator.id"
                  :label="operator.companyName"
                  :value="operator.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="总车位数" prop="totalParking">
              <el-input-number
                v-model="childWarehouseForm.totalParking"
                :min="0"
                placeholder="总车位数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="childWarehouseForm.status"
                placeholder="请选择状态"
              >
                <el-option
                  v-for="dict in parking_lot_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="childWarehouseForm.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitChildWarehouseForm"
            >确 定</el-button
          >
          <el-button @click="cancelChildWarehouseForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="Warehouse">
import { Plus, Picture, Warning, Collection } from '@element-plus/icons-vue';
import { nextTick } from 'vue';
import { getToken } from '@/utils/auth';
import {
  listWarehouse,
  listWarehouseTree,
  getWarehouse,
  delWarehouse,
  addWarehouse,
  updateWarehouse,
} from "@/api/platform/warehouse";
import { optionSelectOperator } from "@/api/platform/operator";
import {
  getProvinces,
  getCities,
  getDistricts
} from "@/api/system/area";
import CustomPagination from "@/components/CustomPagination/index.vue";
// 导入全局错误处理工具
import { isExtensionError } from '@/utils/errorHandler';

const { proxy } = getCurrentInstance();
const { warehouse_status, parking_lot_status } = proxy.useDict("warehouse_status", "parking_lot_status");

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const warehouseList = ref([]);
const operatorOptions = ref([]);
const parentWarehouseOptions = ref([]);
const warehouseTreeOptions = ref([]);
const provinceOptions = ref([]);
const cityOptions = ref([]);
const areaOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 子场库相关数据
const childWarehouseDialogOpen = ref(false);
const childWarehouseFormOpen = ref(false);
const childWarehouseLoading = ref(false);
const childWarehouseList = ref([]);
const childWarehouseTitle = ref("");
const currentComplex = ref({});



// 编辑状态控制
const isEditingMainWarehouseWithChildren = ref(false);



const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: null,
    warehouseName: null,
    operatorId: null,
    status: null,
  },
  rules: {
    warehouseName: [
      { required: true, message: "场库名称不能为空", trigger: "blur" },
    ],
    operatorId: [
      { required: true, message: "所属运营商不能为空", trigger: "change" },
    ],
    warehouseType: [
      { required: true, message: "类型不能为空", trigger: "change" },
    ],
    parentId: [
      {
        validator: (rule, value, callback) => {
          // 只有当选择子场库时才需要验证所属场库
          if (form.value.warehouseType === "child") {
            if (!value || value === 0) {
              callback(new Error('选择子场库时，所属场库不能为空'));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "change"
      },
    ],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
    totalParking: [
      { required: true, message: "总停车位数不能为空", trigger: "blur" },
      { type: "number", min: 1, message: "总停车位数必须大于0", trigger: "blur" },
    ],

    longitude: [
      {
        pattern: /^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$|^-?180(\.0+)?$/,
        message: "经度范围应在-180到180之间",
        trigger: "blur",
      },
    ],
    latitude: [
      {
        pattern: /^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/,
        message: "纬度范围应在-90到90之间",
        trigger: "blur",
      },
    ],
  },
  // 子场库表单数据
  childWarehouseForm: {},
  childWarehouseRules: {
    warehouseName: [
      { required: true, message: "子场库名称不能为空", trigger: "blur" },
    ],

    operatorId: [
      { required: true, message: "所属运营商不能为空", trigger: "change" },
    ],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
  },
});

const { queryParams, form, rules, childWarehouseForm, childWarehouseRules } =
  toRefs(data);

/** 类型显示文本 */
const warehouseTypeDisplay = computed(() => {
  return form.value.warehouseType === 'main' ? '主场库' : '子场库';
});

/** 轮播图上传组件的数据格式转换 */
const carouselImagesForUpload = computed({
  get() {
    if (!form.value.carouselImages) return '';

    try {
      // 如果是JSON字符串，解析并转换为逗号分隔的字符串
      if (typeof form.value.carouselImages === 'string' && form.value.carouselImages.startsWith('[')) {
        let images = JSON.parse(form.value.carouselImages);

        // 处理双重JSON编码的情况
        if (Array.isArray(images) && images.length > 0 && typeof images[0] === 'string') {
          if (images[0].startsWith('[') && images[0].endsWith(']')) {
            try {
              images = JSON.parse(images[0]);
            } catch (e) {
              console.warn('解析内层JSON失败，使用原始数据:', e);
            }
          }
        }

        // 确保images是数组
        if (!Array.isArray(images)) {
          images = [images];
        }

        // 过滤有效的图片URL
        const validImages = images.filter(url =>
          url && typeof url === 'string' && url.trim() &&
          url !== 'undefined' && url !== 'null' &&
          !url.includes('[') && !url.includes(']')
        );

        // image-upload组件期望完整URL，它会自动去掉baseUrl前缀
        // 所以我们直接返回完整URL的逗号分隔字符串
        return validImages.join(',');
      }

      // 如果已经是逗号分隔的字符串，直接返回
      return form.value.carouselImages || '';
    } catch (e) {
      console.error("轮播图数据格式转换失败:", e);
      return '';
    }
  },
  set(value) {
    // image-upload组件返回逗号分隔的字符串，保存到form中
    form.value.carouselImages = value || null;
  }
});



/** 获取轮播图列表 */
function getCarouselImages(carouselImagesStr) {
  if (!carouselImagesStr) return [];

  try {
    let images = JSON.parse(carouselImagesStr);

    // 处理双重JSON编码的情况
    if (Array.isArray(images) && images.length > 0 && typeof images[0] === 'string') {
      // 检查第一个元素是否是JSON字符串
      if (images[0].startsWith('[') && images[0].endsWith(']')) {
        try {
          images = JSON.parse(images[0]);
        } catch (e) {
          console.warn('解析内层JSON失败，使用原始数据:', e);
        }
      }
    }

    // 确保images是数组
    if (!Array.isArray(images)) {
      images = [images];
    }

    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    return images.map(url => {
      // 验证URL是否有效
      if (!url || typeof url !== 'string') {
        console.warn('无效的图片URL:', url);
        return '';
      }

      // 如果已经是完整URL，直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // 清理URL，移除多余的引号和特殊字符
      const cleanUrl = url.replace(/["%\[\]]/g, '').trim();

      // 如果是相对路径，添加baseUrl
      if (cleanUrl.startsWith('/statics/')) {
        return baseUrl + cleanUrl;
      }

      // 如果是纯文件名，添加完整路径
      if (cleanUrl && !cleanUrl.startsWith('/')) {
        return baseUrl + "/statics/" + cleanUrl;
      }

      return baseUrl + cleanUrl;
    }).filter(url => url && url !== baseUrl); // 过滤掉空URL和无效URL
  } catch (e) {
    console.error("解析轮播图数据失败:", e);
    return [];
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 查询场库信息列表 */
function getList() {
  loading.value = true;
  // 只查询主场库（parentId=0）
  const params = {
    ...queryParams.value,
    parentId: 0
  };
  listWarehouse(params)
    .then((response) => {
      warehouseList.value = response.rows || [];
      total.value = response.total || 0;
      loading.value = false;
    })
    .catch((error) => {
      console.error("获取场库列表失败:", error);
      loading.value = false;
      if (!isExtensionError(error)) {
        proxy.$modal.msgError("获取数据失败，请刷新页面重试");
      }
    });
}

/** 查询场库树形选项 */
function getWarehouseTreeOptions() {
  listWarehouseTree({}).then((response) => {
    warehouseTreeOptions.value = response.data.filter(item => item.parentId === 0);
  });
}

/** 查询运营商下拉列表 */
function getOperatorOptions() {
  optionSelectOperator().then((response) => {
    operatorOptions.value = response.data;
  });
}

/** 获取省份列表 */
function getProvinceOptions() {
  getProvinces().then((response) => {
    provinceOptions.value = response.data;
  }).catch((error) => {
    console.error("获取省份列表失败:", error);
  });
}

/** 省份改变事件 */
function handleProvinceChange(provinceCode) {
  // 清空城市和区县
  form.value.cityCode = null;
  form.value.areaCode = null;
  cityOptions.value = [];
  areaOptions.value = [];

  if (provinceCode) {
    getCities(provinceCode).then((response) => {
      cityOptions.value = response.data;
    }).catch((error) => {
      console.error("获取城市列表失败:", error);
    });
  }
}

/** 城市改变事件 */
function handleCityChange(cityCode) {
  // 清空区县
  form.value.areaCode = null;
  areaOptions.value = [];

  if (cityCode) {
    getDistricts(cityCode).then((response) => {
      areaOptions.value = response.data;
    }).catch((error) => {
      console.error("获取区县列表失败:", error);
    });
  }
}

/** 获取父级场库选项 */
function getParentWarehouseOptions() {
  // 查询所有场库类型的记录作为父级选项
  listWarehouse({ parentId: 0, status: 1 }).then((response) => {
    parentWarehouseOptions.value = response.rows || [];
  }).catch((error) => {
    console.error("获取父级场库列表失败:", error);
  });
}



// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectName: null,
    warehouseName: null,
    operatorId: null,
    warehouseType: "main", // 默认为主场库
    parentId: 0, // 默认为顶级
    smartsType: 0,
    totalParking: 0,
    provinceCode: null,
    cityCode: null,
    areaCode: null,
    address: null,
    longitude: null,
    latitude: null,
    carouselImages: null,
    status: 1,
    remark: null,
    leasePropertyNo: null,
    leaseAddress: null,
    leaseDetailAddress: null,
  };
  // 清空省市区选项
  cityOptions.value = [];
  areaOptions.value = [];
  parentWarehouseOptions.value = [];
  isEditingMainWarehouseWithChildren.value = false;
  proxy.resetForm("warehouseRef");
}



// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加场库信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getWarehouse(_id)
    .then((response) => {
      // 检查返回的数据是否有效
      if (!response.data) {
        console.error("获取场库详情失败: 返回数据为空");
        proxy.$modal.msgError("获取场库信息失败，数据为空");
        return;
      }

      form.value = response.data;

      // 将parentId转换为warehouseType字段用于表单显示
      form.value.warehouseType = (form.value.parentId == 0 || form.value.parentId === null || form.value.parentId === undefined) ? "main" : "child";

      // 检查是否为有子场库的主场库
      if (form.value.parentId === 0) {
        // 检查是否有子场库，如果有则禁用类型修改
        // 这里可以调用API检查，暂时先设置为false
        isEditingMainWarehouseWithChildren.value = false;
      }

      // 如果是子场库，需要加载父级场库选项
      if (form.value.warehouseType === "child") {
        getParentWarehouseOptions();
      }

      // 如果有省份代码，加载对应的城市列表
      if (form.value && form.value.provinceCode) {
        getCities(form.value.provinceCode).then((cityResponse) => {
          cityOptions.value = cityResponse.data;

          // 如果有城市代码，加载对应的区县列表
          if (form.value.cityCode) {
            getDistricts(form.value.cityCode).then((districtResponse) => {
              areaOptions.value = districtResponse.data;
            });
          }
        });
      }

      open.value = true;
      title.value = "修改场库信息";
    })
    .catch((error) => {
      console.error("获取场库详情失败:", error);
      if (!isExtensionError(error)) {
        proxy.$modal.msgError("获取场库信息失败，请稍后重试");
      }
    });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["warehouseRef"].validate((valid) => {
    if (valid) {
      // 处理轮播图数据格式转换
      const submitData = { ...form.value };

      // 处理类型字段：将warehouseType转换为parentId逻辑
      if (submitData.warehouseType === "main") {
        // 主场库：设置为顶级
        submitData.parentId = 0;
      } else if (submitData.warehouseType === "child") {
        // 子场库：验证parentId是否有效
        if (!submitData.parentId || submitData.parentId === 0) {
          proxy.$modal.msgError("选择子场库时，必须选择所属场库");
          return;
        }
      }

      // 移除临时字段
      delete submitData.warehouseType;

      // 自动设置创建人和更新人为当前登录用户ID
      try {
        const userStore = proxy.$store.state.user;
        if (userStore && userStore.id) {
          // 新增时设置创建人
          if (!form.value.id) {
            submitData.createBy = userStore.id;
          }
          // 修改时设置更新人
          submitData.updateBy = userStore.id;
        }
      } catch (error) {
        console.warn('获取用户ID失败:', error);
      }

      // 将image-upload组件返回的逗号分隔字符串转换为JSON数组
      if (submitData.carouselImages) {
        try {
          // 先检查是否已经是有效的JSON格式
          let isValidJson = false;
          try {
            const parsed = JSON.parse(submitData.carouselImages);
            if (Array.isArray(parsed)) {
              isValidJson = true;
            }
          } catch (e) {
            // 不是有效JSON，继续处理
          }

          // 如果已经是有效的JSON数组格式，直接使用
          if (isValidJson) {
            // 不需要再次处理，保持原有格式
          }
          // 如果是逗号分隔的字符串，转换为数组
          else if (typeof submitData.carouselImages === 'string' && submitData.carouselImages.includes(',')) {
            const imageArray = submitData.carouselImages.split(',')
              .map(url => url.trim())
              .filter(url => url && url !== 'undefined' && url !== 'null');
            submitData.carouselImages = imageArray.length > 0 ? JSON.stringify(imageArray) : null;
          }
          // 如果是单个图片字符串，转换为包含一个元素的JSON数组
          else if (typeof submitData.carouselImages === 'string' && submitData.carouselImages.trim()) {
            const cleanUrl = submitData.carouselImages.trim();
            if (cleanUrl !== 'undefined' && cleanUrl !== 'null') {
              submitData.carouselImages = JSON.stringify([cleanUrl]);
            } else {
              submitData.carouselImages = null;
            }
          }
          // 如果是空字符串或null，设置为null
          else if (!submitData.carouselImages || submitData.carouselImages.trim() === '') {
            submitData.carouselImages = null;
          }
        } catch (e) {
          console.error("轮播图数据格式转换失败:", e);
          submitData.carouselImages = null;
        }
      } else {
        submitData.carouselImages = null;
      }

      if (form.value.id != null) {
        updateWarehouse(submitData)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          })
          .catch((error) => {
            console.error("修改场库失败:", error);
            // 过滤浏览器扩展错误
            if (!isExtensionError(error)) {
              proxy.$modal.msgError("修改失败，请稍后重试");
            }
          });
      } else {
        addWarehouse(submitData)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          })
          .catch((error) => {
            console.error("新增场库失败:", error);
            // 过滤浏览器扩展错误
            if (!isExtensionError(error)) {
              proxy.$modal.msgError("新增失败，请稍后重试");
            }
          });
      }
    }
  });
}







/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除场库信息编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delWarehouse(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/platform/warehouse/export",
    {
      ...queryParams.value,
    },
    `warehouse_${new Date().getTime()}.xlsx`
  );
}



// ==================== 子场库管理相关方法 ====================

/** 查看子场库按钮操作 */
function handleViewChildWarehouses(row) {
  currentComplex.value = row;
  childWarehouseDialogOpen.value = true;
  getChildWarehouseList(row.id);
}

/** 获取子场库列表 */
function getChildWarehouseList(parentId) {
  childWarehouseLoading.value = true;
  const queryParams = {
    parentId: parentId,
    pageNum: 1,
    pageSize: 1000, // 获取所有子场库
  };

  listWarehouse(queryParams)
    .then((response) => {
      childWarehouseList.value = response.rows || [];
      childWarehouseLoading.value = false;
    })
    .catch(() => {
      childWarehouseList.value = [];
      childWarehouseLoading.value = false;
    });
}

/** 重置子场库表单 */
function resetChildWarehouseForm() {
  childWarehouseForm.value = {
    id: null,
    warehouseName: null,
    parentId: currentComplex.value.id,
    operatorId: currentComplex.value.operatorId,
    totalParking: 0,
    status: 1,
    remark: null,
  };
  proxy.resetForm("childWarehouseRef");
}

/** 新增子场库按钮操作 */
function handleAddChildWarehouse() {
  resetChildWarehouseForm();
  childWarehouseFormOpen.value = true;
  childWarehouseTitle.value = "新增子场库";
}

/** 修改子场库按钮操作 */
function handleUpdateChildWarehouse(row) {
  resetChildWarehouseForm();
  const _id = row.id;
  getWarehouse(_id).then((response) => {
    childWarehouseForm.value = response.data;
    childWarehouseFormOpen.value = true;
    childWarehouseTitle.value = "修改子场库";
  });
}

/** 提交子场库表单 */
function submitChildWarehouseForm() {
  proxy.$refs["childWarehouseRef"].validate((valid) => {
    if (valid) {
      if (childWarehouseForm.value.id != null) {
        updateWarehouse(childWarehouseForm.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          childWarehouseFormOpen.value = false;
          getChildWarehouseList(currentComplex.value.id);
          // 刷新场库列表以更新统计数据
          getList();
        });
      } else {
        addWarehouse(childWarehouseForm.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          childWarehouseFormOpen.value = false;
          getChildWarehouseList(currentComplex.value.id);
          // 刷新场库列表以更新统计数据
          getList();
        });
      }
    }
  });
}

/** 取消子场库表单 */
function cancelChildWarehouseForm() {
  childWarehouseFormOpen.value = false;
  resetChildWarehouseForm();
}

/** 删除子场库按钮操作 */
function handleDeleteChildWarehouse(row) {
  const _id = row.id;
  proxy.$modal
    .confirm(`是否确认删除子场库"${row.warehouseName}"？`)
    .then(function () {
      return delWarehouse(_id);
    })
    .then(() => {
      getChildWarehouseList(currentComplex.value.id);
      // 刷新场库列表以更新统计数据
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
getList();
getOperatorOptions();
getProvinceOptions();
getParentWarehouseOptions();
getWarehouseTreeOptions();
</script>

<style scoped>
.parking-lot-management {
  .mb-4 {
    margin-bottom: 16px;
  }
}

.clear-all-btn:hover {
  color: #66b1ff;
}

/* 表格轮播图样式 */
.table-carousel-container {
  width: 200px;
  margin: 0 auto;
  padding: 8px 0;
}

.carousel-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2px;
}

.carousel-wrapper:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px);
}

.table-carousel {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.carousel-image-item {
  width: 100%;
  height: 90px;
  cursor: pointer;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-image-item:hover {
  transform: scale(1.05);
}

.single-image {
  width: 100%;
  height: 90px;
  border-radius: 6px;
  cursor: pointer;
  object-fit: cover;
  transition: transform 0.3s ease;
  background: white;
}

.single-image:hover {
  transform: scale(1.05);
}

.image-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 14px;
}

.badge-text {
  line-height: 1;
}

.custom-indicators {
  position: absolute;
  bottom: 6px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.indicator-dot:first-child {
  background: white;
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
}

.no-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  color: #6b7280;
  transition: all 0.3s ease;
}

.no-image-container:hover {
  border-color: #9ca3af;
  background: linear-gradient(135deg, #f9fafb 0%, #d1d5db 100%);
}

.no-image-icon {
  font-size: 24px;
  margin-bottom: 4px;
  opacity: 0.6;
}

.no-image-text {
  font-size: 12px;
  font-weight: 500;
}

/* 表格轮播图的指示器样式 */
:deep(.table-carousel .el-carousel__indicators) {
  bottom: -20px;
}

:deep(.table-carousel .el-carousel__indicator) {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
}

:deep(.table-carousel .el-carousel__indicator.is-active) {
  background-color: #409eff;
}

/* 表格轮播图的箭头样式 */
:deep(.table-carousel .el-carousel__arrow) {
  width: 20px;
  height: 20px;
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.table-carousel .el-carousel__arrow:hover) {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>

<style>
/* 运营商下拉框样式 */
.operator-select-dropdown {
  min-width: 300px !important;
}

.operator-select-dropdown .el-select-dropdown__item {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 0 20px !important;
  line-height: 34px !important;
  height: 34px !important;
}


</style>
