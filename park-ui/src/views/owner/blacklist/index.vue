<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="场库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择场库"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="warehouse in allWarehouseOptions"
            :key="warehouse.id"
            :label="warehouse.warehouseName"
            :value="warehouse.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['owner:blacklist:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['owner:blacklist:edit']"
        >修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['owner:blacklist:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['owner:blacklist:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="blacklistList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
       <el-table-column label="运营商" align="center" prop="operatorName" />
      <el-table-column label="场库名称" align="center" prop="warehouseName" />
      <el-table-column label="车牌号" align="center" prop="plateNo">
        <template #default="scope">
          <el-tag
            :type="getPlateNoTagType(scope.row.plateNo)"
            :color="getPlateNoColor(scope.row.plateNo)"
            effect="plain"
          >
            {{ scope.row.plateNo }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="beginTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过期时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="证据图片" align="center" prop="imgUrl" width="100">
        <template #default="scope">
          <el-image
            v-if="scope.row.imgUrl"
            style="width: 50px; height: 50px"
            :src="scope.row.imgUrl"
            :preview-src-list="[scope.row.imgUrl]"
            fit="cover"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <!-- <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['owner:blacklist:edit']">修改</el-button> -->
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['owner:blacklist:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改黑名单管理对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="blacklistRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="运营商" prop="selectedOperatorId">
          <el-select
            v-model="form.selectedOperatorId"
            placeholder="请选择运营商"
            clearable
            style="width: 100%"
            @change="handleOperatorChange"
          >
            <el-option
              v-for="operator in operatorOptions"
              :key="operator.id"
              :label="operator.companyName"
              :value="operator.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="场库" prop="warehouseId">
          <el-select
            v-model="form.warehouseId"
            placeholder="请选择场库"
            clearable
            style="width: 100%"
            :disabled="!form.selectedOperatorId"
          >
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.id"
              :label="warehouse.warehouseName"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号" prop="plateNo">
          <el-input v-model="form.plateNo" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="开始时间" prop="beginTime">
          <el-input
            v-model="form.beginTimeDisplay"
            placeholder="开始时间"
            disabled
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledEndDate"
            style="width: 100%"
          />
          <div v-if="form.beginTimeDisplay && form.endTime" class="time-display">
            时间：{{ form.beginTimeDisplay }} 00:00:00 - {{ form.endTime }} 23:59:59
          </div>
        </el-form-item>
        <el-form-item label="证据图片" prop="imgUrl">
          <image-upload
            v-model="form.imgUrl"
            :limit="1"
            :action="'/file/upload/path'"
            :data="{ path: 'black' }"
            :file-size="5"
            :file-type="['jpg', 'jpeg', 'png']"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Blacklist">
import { listBlacklist, delBlacklist, addBlacklist } from "@/api/owner/blacklist";
import { optionSelectOperator } from "@/api/platform/operator";
import { optionSelectWarehouseByOperator, optionSelectWarehouse } from "@/api/platform/warehouse";
import ImageUpload from "@/components/ImageUpload/index.vue";

const { proxy } = getCurrentInstance();

const blacklistList = ref([]);
const operatorOptions = ref([]);
const warehouseOptions = ref([]);
const allWarehouseOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNo: null,
    warehouseId: null
  },
  rules: {
    warehouseId: [
      { required: true, message: "场库不能为空", trigger: "change" }
    ],
    plateNo: [
      { required: true, message: "车牌号不能为空", trigger: "blur" }
    ],
    endTime: [
      { required: true, message: "结束时间不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询黑名单管理列表 */
function getList() {
  loading.value = true;
  listBlacklist(queryParams.value).then(response => {
    blacklistList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取运营商选项 */
function loadOperatorOptions() {
  optionSelectOperator().then((response) => {
    operatorOptions.value = response.data;
  });
}

/** 获取所有场库选项（用于搜索） */
function loadAllWarehouseOptions() {
  optionSelectWarehouse().then((response) => {
    allWarehouseOptions.value = response.data;
  });
}

/** 运营商变化时获取对应场库 */
function handleOperatorChange(operatorId) {
  form.value.warehouseId = null; // 清空场库选择
  warehouseOptions.value = []; // 清空场库选项

  if (operatorId) {
    optionSelectWarehouseByOperator(operatorId).then((response) => {
      warehouseOptions.value = response.data;
    });
  }
}

/** 编辑时根据场库ID加载对应的运营商和场库选项 */
// async function loadWarehouseOptionsForEdit(warehouseId) {
//   try {
//     // 遍历所有运营商，找到包含该场库的运营商
//     for (const operator of operatorOptions.value) {
//       const response = await optionSelectWarehouseByOperator(operator.id);
//       const warehouses = response.data;

//       // 检查该运营商下是否包含目标场库
//       const targetWarehouse = warehouses.find(w => w.id === warehouseId);
//       if (targetWarehouse) {
//         // 找到了对应的运营商
//         form.value.selectedOperatorId = operator.id;
//         warehouseOptions.value = warehouses;
//         break;
//       }
//     }
//   } catch (error) {
//     console.error('加载场库选项失败:', error);
//   }
// }

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  const today = new Date();
  const beginTimeStr = today.getFullYear() + '-' +
    String(today.getMonth() + 1).padStart(2, '0') + '-' +
    String(today.getDate()).padStart(2, '0') + ' 00:00:00';
  const beginDateStr = today.getFullYear() + '-' +
    String(today.getMonth() + 1).padStart(2, '0') + '-' +
    String(today.getDate()).padStart(2, '0');

  form.value = {
    id: null,
    selectedOperatorId: null, // 用于选择运营商，不提交到后端
    plateNo: null,
    warehouseId: null,
    beginTime: beginTimeStr,
    beginTimeDisplay: beginDateStr, // 只显示日期部分
    endTime: null,
    imgUrl: null
  };
  warehouseOptions.value = []; // 清空场库选项
  proxy.resetForm("blacklistRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    plateNo: null,
    warehouseId: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  loadOperatorOptions();
  open.value = true;
  title.value = "添加黑名单管理";
}

/** 修改按钮操作 */
// function handleUpdate(row) {
//   reset();
//   const _id = row.id || ids.value;

//   // 先加载运营商选项，然后获取黑名单数据
//   Promise.all([
//     optionSelectOperator(),
//     getBlacklist(_id)
//   ]).then(([operatorResponse, blacklistResponse]) => {
//     operatorOptions.value = operatorResponse.data;

//     // 先打开对话框
//     open.value = true;
//     title.value = "修改黑名单管理";

//     // 使用 nextTick 确保组件已渲染，然后设置表单数据
//     nextTick(() => {
//       form.value = blacklistResponse.data;
//       form.value.selectedOperatorId = null; // 初始化运营商选择

//       // 处理日期时间格式，只显示日期部分
//       if (form.value.endTime) {
//         form.value.endTime = form.value.endTime.split(' ')[0];
//       }

//       // 根据场库ID查找对应的运营商，并加载场库选项
//       if (form.value.warehouseId) {
//         // 这里需要通过场库ID反查运营商ID，然后加载场库选项
//         // 可以遍历所有运营商，找到包含该场库的运营商
//         loadWarehouseOptionsForEdit(form.value.warehouseId);
//       }
//     });
//   });
// }

/** 提交按钮 */
function submitForm() {
  proxy.$refs["blacklistRef"].validate(valid => {
    if (valid) {
      let submitData = { ...form.value };

      // 移除前端辅助字段，不提交到后端
      delete submitData.selectedOperatorId;
      delete submitData.beginTimeDisplay;

      // 确保开始时间为当天00:00:00
      const today = new Date();
      const beginTimeStr = today.getFullYear() + '-' +
        String(today.getMonth() + 1).padStart(2, '0') + '-' +
        String(today.getDate()).padStart(2, '0') + ' 00:00:00';
      submitData.beginTime = beginTimeStr;

      // 结束时间固定拼接为23:59:59
      if (submitData.endTime) {
        submitData.endTime = submitData.endTime + ' 23:59:59';
      }

      if (submitData.id != null) {
        updateBlacklist(submitData).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBlacklist(submitData).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  if (!row || !row.id) {
    proxy.$modal.msgError("请选择要删除的记录");
    return;
  }

  proxy.$modal.confirm('是否确认删除车牌号为"' + row.plateNo + '"的黑名单记录？').then(function() {
    return delBlacklist(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/owner/blacklist/export', {
    ...queryParams.value
  }, `blacklist_${new Date().getTime()}.xlsx`)
}

/** 禁用过期时间选择（不能选择今天之前的日期） */
function disabledEndDate(time) {
  // 获取今天的日期（设置为00:00:00以便比较）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 禁用今天之前的所有日期
  return time.getTime() < today.getTime();
}

/** 获取车牌号标签类型 */
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

/** 获取车牌号颜色 */
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

onMounted(() => {
  getList();
  loadOperatorOptions();
  loadAllWarehouseOptions();
});
</script>

<style scoped>
.time-display {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}
</style>
