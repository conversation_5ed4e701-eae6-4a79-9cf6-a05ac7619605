<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="订单号" prop="tradeId">
        <el-input
          v-model="queryParams.tradeId"
          placeholder="请输入订单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="退款类型" prop="refundType">
        <el-select v-model="queryParams.refundType" placeholder="请选择退款类型" clearable style="width: 150px">
          <el-option label="临停订单" value="1" />
          <el-option label="VIP会员" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['refund:refund:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :show-search="showSearch" @update:show-search="showSearch = $event" @query-table="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="refundList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="退款ID" align="center" prop="id" min-width="80" />
      <el-table-column label="订单号" align="center" prop="tradeId" min-width="180" show-overflow-tooltip />
      <el-table-column label="退款类型" align="center" prop="refundType" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.refundType === 1" type="primary">临停订单</el-tag>
          <el-tag v-else-if="scope.row.refundType === 2" type="success">VIP会员</el-tag>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="原订单金额" align="center" prop="originalAmount" min-width="120">
        <template #default="scope">
          <span class="amount-text">¥{{ scope.row.originalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退款金额" align="center" prop="refundAmount" min-width="120">
        <template #default="scope">
          <span class="refund-amount-text">¥{{ scope.row.refundAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退款原因" align="center" prop="refundReason" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.refundReason || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" min-width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="160" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['refund:refund:query']"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看退款详情对话框 -->
    <el-dialog title="退款详情" v-model="open" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="退款ID">{{ form.id }}</el-descriptions-item>
        <el-descriptions-item label="订单号">{{ form.tradeId }}</el-descriptions-item>
        <el-descriptions-item label="退款类型">
          <el-tag v-if="form.refundType === 1" type="primary">临停订单</el-tag>
          <el-tag v-else-if="form.refundType === 2" type="success">VIP会员</el-tag>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item label="原订单金额">
          <span class="amount-text">¥{{ form.originalAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="退款金额">
          <span class="refund-amount-text">¥{{ form.refundAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="退款原因" :span="2">
          {{ form.refundReason || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建者">{{ form.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="更新者" v-if="form.updateBy">{{ form.updateBy }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" v-if="form.updateTime">
          {{ parseTime(form.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Refund">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import { parseTime } from "@/utils/ruoyi";
import { listRefund, getRefund, exportRefund } from "@/api/refund/refund";

const { proxy } = getCurrentInstance();

const refundList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tradeId: null,
    refundType: null
  }
});

const { queryParams, form } = toRefs(data);



// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm("refundRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  const id = row.id;
  getRefund(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "查看退款详情";
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('refund/export', {
    ...queryParams.value
  }, `退款记录_${new Date().getTime()}.xlsx`)
}

/** 分页事件处理 */
function getList(pagination) {
  if (pagination) {
    queryParams.value.pageNum = pagination.page;
    queryParams.value.pageSize = pagination.limit;
  }
  loading.value = true;
  listRefund(queryParams.value).then(response => {
    refundList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.amount-text {
  color: #409EFF;
  font-weight: bold;
}

.refund-amount-text {
  color: #E6A23C;
  font-weight: bold;
}
</style>
