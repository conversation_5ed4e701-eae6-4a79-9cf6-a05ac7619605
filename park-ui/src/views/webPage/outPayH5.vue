<template>
    <div class="home">
        <div class="home_bg">
            <div class="cell">
                <div class="scan_cell">
                    <div class="cell_header">
                        <div class="scan_cell_title">临停缴费</div>
                        <div class="scan_cell_img"></div>
                    </div>
                    <div class="cell_input">
                        <div class="cell_title">{{ carInfo?.plateNo || '--' }}</div>
                        <div class="cell_item">
                            <div class="">停车地点</div>
                            <div class="">{{ carInfo?.warehouseName || '--' }}</div>
                        </div>
                        <div class="cell_item">
                            <div class="">入场时间</div>
                            <div class="">{{ carInfo?.beginParkingTime || '--' }}</div>
                        </div>
                        <div class="cell_item">
                            <div class="">停车时长</div>
                            <div class="">{{ carInfo?.parkingDurationLabel || '--' }}</div>
                        </div>
                        <div class="cell_item">
                            <div class="">应缴金额</div>
                            <div class="">{{ carInfo?.paymentAmount || '--' }}</div>
                        </div>
                        <div class="cell_input_title">选择支付方式</div>
                        <div class="cell_input_radio">
                            <el-radio-group v-model="chooseRadio" @change="handleChange">
                                <el-radio label="1" border>微信</el-radio>
                                <el-radio label="2" border>支付宝</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="submit-btn" @click="handleClick">{{ isDisabled ? '正在跳转' : '确认支付' }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRoute } from 'vue-router'
import { channelPayQuery, paymentTemporaryAlipay } from '@/api/webPage'
const route = useRoute()
const { proxy } = getCurrentInstance()
// 订单信息
const carInfo = ref(null)
// 支付方式
const chooseRadio = ref('1')
// 按钮是否禁用
const isDisabled = ref(false)
// 支付方式切换
function handleChange(e) {
    chooseRadio.value = e
}
// 确认支付
function handleClick() {
    if (!carInfo.value) {
        proxy.$message.error('当前订单不存在~')
        return
    }
    if (!carInfo.value?.paymentAmount) {
        proxy.$message.error('当前应缴金额为0~')
        return
    }
    if (isDisabled.value) {
        return
    }
    isDisabled.value = true
    if (chooseRadio.value === '1') {
        window.open(
            'weixin://dl/business/?appid=wxdcd31ee3e79190cc&path=pages/carStop&query=stopNo%3D' +
            route.query.gateNo +
            'wId' +
            route.query.warehouseId,
            '_blank'
        )
        isDisabled.value = false
    } else {
        let params = {
            gateNo: route.query.gateNo,
            warehouseId: route.query.warehouseId
        }
        paymentTemporaryAlipay(params).then(res => {
            if (res.data) {
                location.href = res.data
                setTimeout(() => {
                    isDisabled.value = false
                }, 5000)
            }
        })
    }
}
// 出场扫码到当前页面，根据gateNo和warehouseId查询订单信息
onMounted(() => {
    if (route.query.gateNo && route.query.warehouseId) {
        let params = {
            gateNo: route.query.gateNo,
            warehouseId: route.query.warehouseId
        }
        console.log(params)
        channelPayQuery(params).then(res => {
            carInfo.value = res.data || {}
        })
    }
})
</script>
<style lang="scss" scope>
.home {
    height: 100vh;
    overflow: hidden;
    width: 100vw;

    .home_bg {
        height: 100%;
        background-image: url('/src/assets/vip/bg.png');
        background-size: 100%;
        background-repeat: no-repeat;
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .cell {
        width: 90%;
        position: absolute;
        z-index: 2;

        .scan_cell {
            width: 100%;
            border-radius: 20px;
            background-color: #dcecff;
            padding: 0 16px 20px;

            .cell_header {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .scan_cell_title {
                font-size: 28px;
                font-weight: bold;
                line-height: 29px;
                background: linear-gradient(180deg, #205ad3 0%, #2150b3 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .scan_cell_img {
                background-image: url('/src/assets/vip/car.png');
                background-size: 100%;
                background-repeat: no-repeat;
                width: 137px;
                height: 97px;
            }
        }

        .cell_input {
            background-image: url('/src/assets/vip/block.png');
            background-size: cover;
            background-repeat: no-repeat;
            width: 100%;
            margin-top: -16px;
            padding: 16px 20px 20px;

            .cell_title {
                font-size: 18px;
                font-weight: bold;
                color: #212121;
                margin-bottom: 10px;
            }

            .cell_item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
                margin-bottom: 10px;
                color: #666;
            }

            .cell_input_title {
                font-size: 14px;
                font-weight: bold;
                color: #212121;
                margin-bottom: 10px;
            }

            .cell_input_radio {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .plateNo {
                display: flex;
                justify-content: space-around;
                align-items: center;
                padding-bottom: 10px;
            }
        }

        .submit-btn {
            width: 260px;
            height: 48px;
            line-height: 48px;
            background: linear-gradient(360deg, #ffc234 0%, #fffae3 100%);
            box-shadow: inset 0px -2px 1px 0px #f0a535, inset 0px -4px 4px 0px rgba(255, 255, 255, 0.25),
                0px 4px 15px 0px rgba(209, 202, 185, 0.8);
            border-radius: 200px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: #de5d00;
            margin: 20px auto 0;
        }
    }
}
</style>




<!-- <script setup lang="ts">
import { paymentTemporaryAlipay, channelPayQuery } from '@/api/webPage'
import { useRoute } from 'vue-router'
const route = useRoute()
// let gateNo: any = route.query.gateNo
let checkRadio = ref('1')
const chooseRadio = ref('1')
function handleChange(e: any) {
    checkRadio.value = e
}
const isDisabled = ref(false)
function handleClick() {
    if (!carInfo.value) {
        return ElMessage.error('当前订单不存在~')
    }
    if (!carInfo.value?.paymentAmount) {
        return ElMessage.error('当前应缴金额为0~')
    }
    if (isDisabled.value) {
        return
    }
    isDisabled.value = true
    if (checkRadio.value === '1') {
        // https://blog.csdn.net/weixin_48464215/article/details/135839743
        window.open(
            'weixin://dl/business/?appid=wxf5a8941d0d5617c1&path=pagesStore/carStop&query=stopNo%3D' +
            route.query.gateNo +
            'wId' +
            route.query.warehouseId,
            '_blank'
        )
        isDisabled.value = false
    } else {
        let params = {
            gateNo: route.query.gateNo,
            warehouseId: route.query.warehouseId
        }
        paymentTemporaryAlipay(params).then(res => {
            if (res.data.alipay) {
                location.href = res.data.alipay
                setTimeout(() => {
                    isDisabled.value = false
                }, 5000)
            }
        })
    }
}
let carInfo = ref()
onMounted(() => {
    if (route.query.gateNo) {
        let params = {
            gateNo: route.query.gateNo,
            warehouseId: route.query.warehouseId
        }
        channelPayQuery(params).then(res => {
            carInfo.value = res.data || {}
        })
    }
})
</script> -->
