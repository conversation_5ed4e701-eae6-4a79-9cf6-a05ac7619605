<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>异常订单详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="异常订单编号">
          {{ orderDetail.orderId }}
        </el-descriptions-item>
        <el-descriptions-item label="异常记录ID">
          {{ orderDetail.id }}
        </el-descriptions-item>
        <el-descriptions-item label="车牌号">
          <el-tag
            :type="getPlateNoTagType(orderDetail.plateNo)"
            :color="getPlateNoColor(orderDetail.plateNo)"
            effect="plain"
          >
            {{ orderDetail.plateNo }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="车牌类型">
          <dict-tag :options="plate_type" :value="getPlateType(orderDetail.plateNo)"/>
        </el-descriptions-item>
        <el-descriptions-item label="场库名称">
          {{ orderDetail.warehouseName }}
        </el-descriptions-item>
        <el-descriptions-item label="异常类型">
          <dict-tag :options="exception_order_type" :value="orderDetail.exceptionType"/>
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <dict-tag :options="exception_order_handle_status" :value="orderDetail.handleStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="异常金额">
          <span class="exception-amount-text">¥{{ orderDetail.exceptionAmount || '0.00' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="原始金额">
          ¥{{ orderDetail.originalAmount || '0.00' }}
        </el-descriptions-item>
        <el-descriptions-item label="实际金额">
          ¥{{ orderDetail.actualAmount || '0.00' }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <dict-tag :options="priority_level" :value="orderDetail.priorityLevel"/>
        </el-descriptions-item>
        <el-descriptions-item label="来源类型">
          <dict-tag :options="source_type" :value="orderDetail.sourceType"/>
        </el-descriptions-item>
        <el-descriptions-item label="开始停车时间">
          {{ parseTime(orderDetail.beginParkingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="结束停车时间">
          {{ parseTime(orderDetail.endParkingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="停车时长">
          {{ orderDetail.parkingDuration }}分钟
        </el-descriptions-item>
        <el-descriptions-item label="异常发生时间">
          {{ parseTime(orderDetail.exceptionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人">
          {{ orderDetail.contactUser || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ orderDetail.contactPhone || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理人">
          {{ orderDetail.handleByName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理时间">
          {{ parseTime(orderDetail.handleTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="补偿金额">
          <span v-if="orderDetail.compensationAmount > 0" class="compensation-amount">
            ¥{{ orderDetail.compensationAmount }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否已补偿">
          <dict-tag :options="sys_yes_no" :value="orderDetail.isCompensated"/>
        </el-descriptions-item>
        <el-descriptions-item label="是否需要跟进">
          <dict-tag :options="sys_yes_no" :value="orderDetail.followUpRequired"/>
        </el-descriptions-item>
        <el-descriptions-item label="异常描述" :span="2">
          {{ orderDetail.exceptionDesc }}
        </el-descriptions-item>
        <el-descriptions-item label="处理备注" :span="2">
          {{ orderDetail.handleRemark || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理结果" :span="2">
          {{ orderDetail.resolutionResult || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 操作按钮 -->
      <div class="operation-buttons" v-if="orderDetail.handleStatus !== 2 && orderDetail.handleStatus !== 3">
        <el-button type="primary" @click="handleOrder">处理订单</el-button>
        <el-button type="warning" @click="ignoreOrder">忽略订单</el-button>
      </div>
    </el-card>

    <!-- 处理对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px" append-to-body>
      <el-form ref="handleFormRef" :model="handleForm" :rules="handleRules" label-width="100px">
        <el-form-item label="处理状态" prop="handleStatus">
          <el-select v-model="handleForm.handleStatus" placeholder="请选择处理状态">
            <el-option label="处理中" :value="1" />
            <el-option label="已处理" :value="2" />
            <el-option label="已忽略" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理备注" prop="handleRemark">
          <el-input v-model="handleForm.handleRemark" type="textarea" :rows="4" placeholder="请输入处理备注" />
        </el-form-item>
        <el-form-item label="处理结果" prop="resolutionResult" v-if="handleForm.handleStatus === 2">
          <el-input v-model="handleForm.resolutionResult" type="textarea" :rows="3" placeholder="请输入处理结果" />
        </el-form-item>
        <el-form-item label="补偿金额" prop="compensationAmount" v-if="handleForm.handleStatus === 2">
          <el-input-number v-model="handleForm.compensationAmount" :precision="2" :min="0" placeholder="补偿金额" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitHandle">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ExceptionOrderDetail">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getExceptionOrder, handleExceptionOrder } from "@/api/order/exceptionOrder";
import { parseTime } from "@/utils/ruoyi";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const { exception_order_type, exception_order_handle_status, priority_level, source_type, sys_yes_no, plate_type } = proxy.useDict("exception_order_type", "exception_order_handle_status", "priority_level", "source_type", "sys_yes_no", "plate_type");

const orderDetail = ref({});
const dialogVisible = ref(false);
const dialogTitle = ref("");

const handleForm = reactive({
  id: null,
  handleStatus: null,
  handleRemark: '',
  resolutionResult: '',
  compensationAmount: 0
});

const handleRules = {
  handleStatus: [
    { required: true, message: "处理状态不能为空", trigger: "change" }
  ],
  handleRemark: [
    { required: true, message: "处理备注不能为空", trigger: "blur" }
  ]
};

/** 获取订单详情 */
function getOrderDetail() {
  const id = route.params.id;
  getExceptionOrder(id).then(response => {
    orderDetail.value = response.data;
  });
}

/** 返回 */
function goBack() {
  router.go(-1);
}

/** 处理订单 */
function handleOrder() {
  dialogTitle.value = "处理异常订单";
  handleForm.id = orderDetail.value.id;
  handleForm.handleStatus = 1;
  handleForm.handleRemark = '';
  handleForm.resolutionResult = '';
  handleForm.compensationAmount = 0;
  dialogVisible.value = true;
}

/** 忽略订单 */
function ignoreOrder() {
  dialogTitle.value = "忽略异常订单";
  handleForm.id = orderDetail.value.id;
  handleForm.handleStatus = 3;
  handleForm.handleRemark = '';
  handleForm.resolutionResult = '';
  handleForm.compensationAmount = 0;
  dialogVisible.value = true;
}

/** 提交处理 */
function submitHandle() {
  proxy.$refs["handleFormRef"].validate((valid) => {
    if (valid) {
      handleExceptionOrder(handleForm).then(response => {
        proxy.$modal.msgSuccess("处理成功");
        dialogVisible.value = false;
        getOrderDetail();
      });
    }
  });
}





// 获取车牌号标签类型
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

// 获取车牌号颜色
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

// 获取车牌类型
function getPlateType(plateNo) {
  if (!plateNo) return 1;
  // 8位为新能源车牌(2)，7位为普通车牌(1)
  return plateNo.length === 8 ? 2 : 1;
}

onMounted(() => {
  getOrderDetail();
});
</script>

<style scoped>
.exception-amount-text {
  color: #f56c6c;
  font-weight: bold;
}

.compensation-amount {
  color: #67c23a;
  font-weight: bold;
}

.operation-buttons {
  margin-top: 20px;
  text-align: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
