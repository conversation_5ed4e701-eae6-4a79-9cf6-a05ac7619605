import request from '@/utils/request'

// 获取首页统计数据
export function getDashboardStatistics(period = 'week', warehouseId = null) {
  return request({
    url: '/system/dashboard/statistics',
    method: 'get',
    params: {
      period,
      warehouseId
    }
  })
}

// 获取全系统统计数据（不受场库选择影响）
export function getSystemStatistics(period = 'week') {
  return request({
    url: '/system/dashboard/system-statistics',
    method: 'get',
    params: {
      period
    }
  })
}

// 获取收入趋势数据
export function getRevenueTrend(period = 'week', warehouseId = null) {
  return request({
    url: '/system/dashboard/revenue-trend',
    method: 'get',
    params: {
      period,
      warehouseId
    }
  })
}

// 获取用户增长趋势数据
export function getUserTrend(period = 'week', warehouseId = null) {
  return request({
    url: '/system/dashboard/user-trend',
    method: 'get',
    params: {
      period,
      warehouseId
    }
  })
}

// 获取场库列表
export function getWarehouseList() {
  return request({
    url: '/system/dashboard/warehouses',
    method: 'get'
  })
}

// 获取场库选项（包含层级关系）
export function getWarehouseOptions() {
  return request({
    url: '/system/dashboard/warehouses',
    method: 'get'
  })
}

// 获取指定场库的详细统计
export function getWarehouseStats(warehouseId) {
  return request({
    url: `/system/dashboard/warehouse/${warehouseId}`,
    method: 'get'
  })
}

// 刷新统计数据缓存
export function refreshStatisticsCache() {
  return request({
    url: '/system/dashboard/refresh-cache',
    method: 'post'
  })
}
