import request from '@/utils/request'

// 查询发票记录管理列表
export function listInvoiceRecord(query) {
  return request({
    url: '/system/operation/invoiceRecord/list',
    method: 'get',
    params: query
  })
}

// 查询发票记录管理详细
export function getInvoiceRecord(id) {
  return request({
    url: '/system/operation/invoiceRecord/' + id,
    method: 'get'
  })
}

// 新增发票记录管理
export function addInvoiceRecord(data) {
  return request({
    url: '/system/operation/invoiceRecord',
    method: 'post',
    data: data
  })
}

// 修改发票记录管理
export function updateInvoiceRecord(data) {
  return request({
    url: '/system/operation/invoiceRecord',
    method: 'put',
    data: data
  })
}

// 删除发票记录管理
export function delInvoiceRecord(id) {
  return request({
    url: '/system/operation/invoiceRecord/' + id,
    method: 'delete'
  })
}

// 发票重开
export function reopenInvoice(id) {
  return request({
    url: '/system/operation/invoiceRecord/reopen/' + id,
    method: 'post'
  })
}

// 发票冲红
export function reverseInvoice(id) {
  return request({
    url: '/system/operation/invoiceRecord/reverse/' + id,
    method: 'post'
  })
}
