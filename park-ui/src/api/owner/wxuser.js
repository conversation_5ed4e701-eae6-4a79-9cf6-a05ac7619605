import request from '@/utils/request'

// 查询小程序用户列表
export function listWxUser(query) {
  return request({
    url: '/system/owner/wxuser/list',
    method: 'get',
    params: query
  })
}

// 查询小程序用户详细
export function getWxUser(id) {
  return request({
    url: '/system/owner/wxuser/' + id,
    method: 'get'
  })
}

// 新增小程序用户
export function addWxUser(data) {
  return request({
    url: '/system/owner/wxuser',
    method: 'post',
    data: data
  })
}

// 修改小程序用户
export function updateWxUser(data) {
  return request({
    url: '/system/owner/wxuser',
    method: 'put',
    data: data
  })
}

// 删除小程序用户
export function delWxUser(id) {
  return request({
    url: '/system/owner/wxuser/' + id,
    method: 'delete'
  })
}

// 修改用户状态
export function changeWxUserStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/owner/wxuser/changeStatus',
    method: 'put',
    data: data
  })
}

// 批量启用用户
export function batchEnableWxUser(ids) {
  return request({
    url: '/system/owner/wxuser/batchEnable/' + ids,
    method: 'put'
  })
}

// 批量停用用户
export function batchDisableWxUser(ids) {
  return request({
    url: '/system/owner/wxuser/batchDisable/' + ids,
    method: 'put'
  })
}

// 查询用户车辆信息
export function getUserCars(userId) {
  return request({
    url: '/system/owner/wxuser/cars/' + userId,
    method: 'get'
  })
}

// 导出小程序用户
export function exportWxUser(query) {
  return request({
    url: '/system/owner/wxuser/export',
    method: 'post',
    data: query
  })
}
