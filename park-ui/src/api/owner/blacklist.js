import request from '@/utils/request'

// 查询黑名单管理列表
export function listBlacklist(query) {
  return request({
    url: '/system/owner/blacklist/list',
    method: 'get',
    params: query
  })
}

// 查询黑名单管理详细
export function getBlacklist(id) {
  return request({
    url: '/system/owner/blacklist/' + id,
    method: 'get'
  })
}

// 新增黑名单管理
export function addBlacklist(data) {
  return request({
    url: '/system/owner/blacklist',
    method: 'post',
    data: data
  })
}

// 修改黑名单管理
export function updateBlacklist(data) {
  return request({
    url: '/system/owner/blacklist',
    method: 'put',
    data: data
  })
}

// 删除黑名单管理
export function delBlacklist(id) {
  return request({
    url: '/system/owner/blacklist/' + id,
    method: 'delete'
  })
}

// 获取运营商选项
export function getOperatorOptions() {
  return request({
    url: '/system/platform/operator/optionSelect',
    method: 'get'
  })
}

// 根据运营商ID获取场库选项
export function getWarehouseOptionsByOperator(operatorId) {
  return request({
    url: '/system/platform/warehouse/optionSelectByOperator',
    method: 'get',
    params: { operatorId }
  })
}

// 获取场库选项
export function getWarehouseOptions() {
  return request({
    url: '/system/platform/warehouse/optionSelect',
    method: 'get'
  })
}
