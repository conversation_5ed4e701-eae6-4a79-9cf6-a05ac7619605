import request from '@/utils/request'

// 查询车辆出入场记录列表
export function listGateParkingInfo(query) {
  return request({
    url: '/system/gateParkingInfo/list',
    method: 'get',
    params: query
  })
}

// 查询车辆出入场记录详细
export function getGateParkingInfo(id) {
  return request({
    url: '/system/gateParkingInfo/' + id,
    method: 'get'
  })
}

// 新增车辆出入场记录
export function addGateParkingInfo(data) {
  return request({
    url: '/system/gateParkingInfo',
    method: 'post',
    data: data
  })
}

// 修改车辆出入场记录
export function updateGateParkingInfo(data) {
  return request({
    url: '/system/gateParkingInfo',
    method: 'put',
    data: data
  })
}

// 删除车辆出入场记录
export function delGateParkingInfo(id) {
  return request({
    url: '/system/gateParkingInfo/' + id,
    method: 'delete'
  })
}

// 根据车牌号查询车辆出入场记录
export function getGateParkingInfoByPlateNum(plateNum) {
  return request({
    url: '/system/gateParkingInfo/plateNum/' + plateNum,
    method: 'get'
  })
}

// 根据停车场ID查询车辆出入场记录
export function getGateParkingInfoByParkingId(parkingId) {
  return request({
    url: '/system/gateParkingInfo/parkingId/' + parkingId,
    method: 'get'
  })
}

// 根据状态查询车辆出入场记录
export function getGateParkingInfoByStatus(status) {
  return request({
    url: '/system/gateParkingInfo/status/' + status,
    method: 'get'
  })
}

// 统计车辆出入场记录数量
export function countGateParkingInfo(query) {
  return request({
    url: '/system/gateParkingInfo/count',
    method: 'get',
    params: query
  })
}

// 获取车辆类型选项
export function getCarTypeOptions() {
  return request({
    url: '/system/gateParkingInfo/carTypeOptions',
    method: 'get'
  })
}

// 获取支付类型选项
export function getPayTypeOptions() {
  return request({
    url: '/system/gateParkingInfo/payTypeOptions',
    method: 'get'
  })
}

// 获取通道名称选项
export function getChannelNameOptions() {
  return request({
    url: '/system/gateParkingInfo/channelNameOptions',
    method: 'get'
  })
}

// 导出车辆出入场记录
export function exportGateParkingInfo(query) {
  return request({
    url: '/system/gateParkingInfo/export',
    method: 'post',
    data: query
  })
}
