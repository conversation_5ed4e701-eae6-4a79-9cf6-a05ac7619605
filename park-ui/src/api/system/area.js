import request from '@/utils/request'

// 查询行政区域列表
export function listArea(query) {
  return request({
    url: '/system/area/list',
    method: 'get',
    params: query
  })
}

// 查询行政区域详细
export function getArea(areaCode) {
  return request({
    url: '/system/area/' + areaCode,
    method: 'get'
  })
}

// 新增行政区域
export function addArea(data) {
  return request({
    url: '/system/area',
    method: 'post',
    data: data
  })
}

// 修改行政区域
export function updateArea(data) {
  return request({
    url: '/system/area',
    method: 'put',
    data: data
  })
}

// 删除行政区域
export function delArea(areaCode) {
  return request({
    url: '/system/area/' + areaCode,
    method: 'delete'
  })
}

// 根据父级代码查询子区域列表
export function getAreaChildren(parentCode) {
  return request({
    url: '/system/area/children/' + parentCode,
    method: 'get'
  })
}

// 根据区域级别查询区域列表
export function getAreaByLevel(areaLevel) {
  return request({
    url: '/system/area/level/' + areaLevel,
    method: 'get'
  })
}

// 获取省份列表
export function getProvinces() {
  return request({
    url: '/system/area/provinces',
    method: 'get'
  })
}

// 根据省份代码获取城市列表
export function getCities(provinceCode) {
  return request({
    url: '/system/area/cities/' + provinceCode,
    method: 'get'
  })
}

// 根据城市代码获取区县列表
export function getDistricts(cityCode) {
  return request({
    url: '/system/area/districts/' + cityCode,
    method: 'get'
  })
}
