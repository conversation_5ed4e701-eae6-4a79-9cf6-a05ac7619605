import request from '@/utils/request'

// 查询广告配置信息列表
export function listAdvertConfig(query) {
  return request({
    url: '/system/advertConfig/list',
    method: 'get',
    params: query
  })
}

// 查询广告配置信息详细
export function getAdvertConfig(id) {
  return request({
    url: '/system/advertConfig/' + id,
    method: 'get'
  })
}

// 新增广告配置信息
export function addAdvertConfig(data) {
  return request({
    url: '/system/advertConfig',
    method: 'post',
    data: data
  })
}

// 修改广告配置信息
export function updateAdvertConfig(data) {
  return request({
    url: '/system/advertConfig',
    method: 'put',
    data: data
  })
}

// 删除广告配置信息
export function delAdvertConfig(id) {
  return request({
    url: '/system/advertConfig/' + id,
    method: 'delete'
  })
}
