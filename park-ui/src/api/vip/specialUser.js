import request from '@/utils/request'

// 查询特殊会员列表
export function listSpecialUser(query) {
  return request({
    url: '/system/special/user/list',
    method: 'get',
    params: query
  })
}

// 查询特殊会员详细
export function getSpecialUser(id) {
  return request({
    url: '/system/special/user/' + id,
    method: 'get'
  })
}

// 新增特殊会员
export function addSpecialUser(data) {
  return request({
    url: '/system/special/user',
    method: 'post',
    data: data
  })
}

// 修改特殊会员
export function updateSpecialUser(data) {
  return request({
    url: '/system/special/user',
    method: 'put',
    data: data
  })
}

// 删除特殊会员
export function delSpecialUser(id) {
  return request({
    url: '/system/special/user/' + id,
    method: 'delete'
  })
}



// 统计特殊会员数量按类型
export function getSpecialUsersStatisticsByType() {
  return request({
    url: '/system/special/user/statistics/type',
    method: 'get'
  })
}
