import request from '@/utils/request'

// 查询会员交易记录列表
export function listVipTransaction(query) {
  return request({
    url: '/system/vip/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询会员交易记录详细
export function getVipTransaction(id) {
  return request({
    url: '/system/vip/transaction/' + id,
    method: 'get'
  })
}

// 新增会员交易记录
export function addVipTransaction(data) {
  return request({
    url: '/system/vip/transaction',
    method: 'post',
    data: data
  })
}

// 修改会员交易记录
export function updateVipTransaction(data) {
  return request({
    url: '/system/vip/transaction',
    method: 'put',
    data: data
  })
}

// 删除会员交易记录
export function delVipTransaction(id) {
  return request({
    url: '/system/vip/transaction/' + id,
    method: 'delete'
  })
}

// 根据套餐ID获取交易记录
export function getTransactionsByPackage(packageId) {
  return request({
    url: '/system/vip/transaction/package/' + packageId,
    method: 'get'
  })
}

// 查询指定时间范围内的交易记录
export function getTransactionsByTimeRange(startTime, endTime) {
  return request({
    url: '/system/vip/transaction/timeRange',
    method: 'get',
    params: {
      startTime,
      endTime
    }
  })
}

// 处理退款
export function processRefund(data) {
  return request({
    url: '/system/vip/transaction/refund',
    method: 'post',
    data: data
  })
}

// 统计交易金额按状态
export function getAmountStatsByStatus(warehouseId) {
  return request({
    url: '/system/vip/transaction/stats/amountByStatus',
    method: 'get',
    params: {
      warehouseId
    }
  })
}

// 统计交易数量按类型
export function getCountStatsByType(warehouseId) {
  return request({
    url: '/system/vip/transaction/stats/countByType',
    method: 'get',
    params: {
      warehouseId
    }
  })
}

// 获取子场库选项
export function getChildWarehouseOptions() {
  return request({
    url: '/system/platform/warehouse/optionSelectChildWarehouse',
    method: 'get'
  })
}

// 根据父场库ID获取子场库选项
export function getChildWarehouseOptionsByParentId(parentId) {
  return request({
    url: '/system/platform/warehouse/optionSelectChildWarehouseByParent',
    method: 'get',
    params: { parentId }
  })
}
