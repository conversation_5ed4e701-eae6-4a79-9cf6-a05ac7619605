import request from '@/utils/request'

// 查询会员信息列表
export function listVipMember(query) {
  return request({
    url: '/system/vip/member/list',
    method: 'get',
    params: query
  })
}

// 查询会员信息详细
export function getVipMember(id) {
  return request({
    url: '/system/vip/member/' + id,
    method: 'get'
  })
}

// 新增会员信息
export function addVipMember(data) {
  return request({
    url: '/system/vip/member',
    method: 'post',
    data: data
  })
}

// 修改会员信息
export function updateVipMember(data) {
  return request({
    url: '/system/vip/member',
    method: 'put',
    data: data
  })
}

// 删除会员信息
export function delVipMember(id) {
  return request({
    url: '/system/vip/member/' + id,
    method: 'delete'
  })
}

// 根据手机号查询会员信息
export function getMemberByPhone(phoneNumber) {
  return request({
    url: '/system/vip/member/phone/' + phoneNumber,
    method: 'get'
  })
}

// 根据场库ID获取会员列表
export function getMembersByWarehouse(warehouseId) {
  return request({
    url: '/system/vip/member/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 获取即将到期的会员列表
export function getExpiringMembers(days = 7) {
  return request({
    url: '/system/vip/member/expiring',
    method: 'get',
    params: { days }
  })
}

// 获取子场库选项
export function getChildWarehouseOptions() {
  return request({
    url: '/system/platform/warehouse/optionSelectChildWarehouse',
    method: 'get'
  })
}

// 根据父场库ID获取子场库选项
export function getChildWarehouseOptionsByParentId(parentId) {
  return request({
    url: '/system/platform/warehouse/optionSelectChildWarehouseByParent',
    method: 'get',
    params: { parentId }
  })
}

// 获取运营商选项
export function getOperatorOptions() {
  return request({
    url: '/system/platform/operator/optionSelect',
    method: 'get'
  })
}

// 根据运营商ID获取场库选项
export function getWarehouseOptionsByOperator(operatorId) {
  return request({
    url: '/system/platform/warehouse/optionSelectByOperator',
    method: 'get',
    params: { operatorId }
  })
}



// 检查场库的子场库数量
export function checkWarehouseChildCount(warehouseId) {
  return request({
    url: '/system/platform/warehouse/checkChildWarehouseCount/' + warehouseId,
    method: 'get'
  })
}

// 获取场库选项（包含层级关系）
export function getWarehouseOptions() {
  return request({
    url: '/system/vip/member/warehouseOptions',
    method: 'get'
  })
}

