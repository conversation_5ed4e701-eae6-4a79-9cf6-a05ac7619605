import request from '@/utils/request'

// 查询会员套餐配置列表
export function listVipPackage(query) {
  return request({
    url: '/system/vip/package/list',
    method: 'get',
    params: query
  })
}

// 查询会员套餐配置详细
export function getVipPackage(id) {
  return request({
    url: '/system/vip/package/' + id,
    method: 'get'
  })
}

// 新增会员套餐配置
export function addVipPackage(data) {
  return request({
    url: '/system/vip/package',
    method: 'post',
    data: data
  })
}

// 修改会员套餐配置
export function updateVipPackage(data) {
  return request({
    url: '/system/vip/package',
    method: 'put',
    data: data
  })
}

// 删除会员套餐配置
export function delVipPackage(id) {
  return request({
    url: '/system/vip/package/' + id,
    method: 'delete'
  })
}

// 获取所有场库选项
export function getWarehouseOptions() {
  return request({
    url: '/system/vip/package/warehouseOptions',
    method: 'get'
  })
}

// 根据场库ID获取套餐列表
export function getPackagesByWarehouse(warehouseId) {
  return request({
    url: '/system/vip/package/warehouse/' + warehouseId,
    method: 'get'
  })
}


