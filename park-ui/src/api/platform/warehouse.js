import request from '@/utils/request'

// 查询场库信息列表
export function listWarehouse(query) {
  return request({
    url: '/system/platform/warehouse/list',
    method: 'get',
    params: query
  })
}

// 查询场库树形结构列表
export function listWarehouseTree(query) {
  return request({
    url: '/system/platform/warehouse/treeList',
    method: 'get',
    params: query
  })
}

// 查询场库信息详细
export function getWarehouse(id) {
  return request({
    url: '/system/platform/warehouse/' + id,
    method: 'get'
  })
}

// 新增场库信息
export function addWarehouse(data) {
  return request({
    url: '/system/platform/warehouse',
    method: 'post',
    data: data
  })
}

// 修改场库信息
export function updateWarehouse(data) {
  return request({
    url: '/system/platform/warehouse',
    method: 'put',
    data: data
  })
}

// 删除场库信息
export function delWarehouse(id) {
  return request({
    url: '/system/platform/warehouse/' + id,
    method: 'delete'
  })
}

// 导出场库信息
export function exportWarehouse(query) {
  return request({
    url: '/system/platform/warehouse/export',
    method: 'post',
    params: query
  })
}

// 获取场库下拉列表
export function optionSelectWarehouse() {
  return request({
    url: '/system/platform/warehouse/optionSelect',
    method: 'get'
  })
}

// 根据运营商ID获取场库下拉列表
export function optionSelectWarehouseByOperator(operatorId) {
  return request({
    url: '/system/platform/warehouse/optionSelectByOperator',
    method: 'get',
    params: { operatorId }
  })
}

// 获取子场库下拉列表
export function optionSelectChildWarehouse() {
  return request({
    url: '/system/platform/warehouse/optionSelectChildWarehouse',
    method: 'get'
  })
}

// 根据父场库ID获取子场库下拉列表
export function optionSelectChildWarehouseByParent(parentId) {
  return request({
    url: '/system/platform/warehouse/optionSelectChildWarehouseByParent',
    method: 'get',
    params: { parentId }
  })
}
