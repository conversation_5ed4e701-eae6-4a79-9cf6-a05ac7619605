import request from '@/utils/request'

// 查询银联配置列表
export function listUnionPayConfig(query) {
  return request({
    url: '/system/platform/unionPayConfig/list',
    method: 'get',
    params: query
  })
}

// 查询银联配置详细
export function getUnionPayConfig(id) {
  return request({
    url: '/system/platform/unionPayConfig/' + id,
    method: 'get'
  })
}

// 新增银联配置
export function addUnionPayConfig(data) {
  return request({
    url: '/system/platform/unionPayConfig',
    method: 'post',
    data: data
  })
}

// 修改银联配置
export function updateUnionPayConfig(data) {
  return request({
    url: '/system/platform/unionPayConfig',
    method: 'put',
    data: data
  })
}

// 删除银联配置
export function delUnionPayConfig(id) {
  return request({
    url: '/system/platform/unionPayConfig/' + id,
    method: 'delete'
  })
}

// 导出银联配置
export function exportUnionPayConfig(query) {
  return request({
    url: '/system/platform/unionPayConfig/export',
    method: 'post',
    params: query
  })
}

// 校验商户号是否唯一
export function checkMidUnique(mid, id) {
  return request({
    url: '/system/platform/unionPayConfig/checkMidUnique',
    method: 'get',
    params: { mid, id }
  })
}

// 校验终端号是否唯一
export function checkTidUnique(tid, id) {
  return request({
    url: '/system/platform/unionPayConfig/checkTidUnique',
    method: 'get',
    params: { tid, id }
  })
}

// 根据场库ID查询银联配置
export function getUnionPayConfigByWarehouseId(warehouseId) {
  return request({
    url: '/system/platform/unionPayConfig/getByWarehouseId/' + warehouseId,
    method: 'get'
  })
}
