import request from '@/utils/request'

// 查询运营商信息列表
export function listOperator(query) {
  return request({
    url: '/system/platform/operator/list',
    method: 'get',
    params: query
  })
}

// 查询运营商信息详细
export function getOperator(id) {
  return request({
    url: '/system/platform/operator/' + id,
    method: 'get'
  })
}

// 新增运营商信息
export function addOperator(data) {
  return request({
    url: '/system/platform/operator',
    method: 'post',
    data: data
  })
}

// 修改运营商信息
export function updateOperator(data) {
  return request({
    url: '/system/platform/operator',
    method: 'put',
    data: data
  })
}

// 删除运营商信息
export function delOperator(id) {
  return request({
    url: '/system/platform/operator/' + id,
    method: 'delete'
  })
}

// 获取运营商下拉列表
export function optionSelectOperator() {
  return request({
    url: '/system/platform/operator/optionSelect',
    method: 'get'
  })
}
