import request from '@/utils/request'

// 查询场库管理人员信息列表
export function listWarehouseManager(query) {
  return request({
    url: '/system/platform/warehouseManager/list',
    method: 'get',
    params: query
  })
}

// 查询场库管理人员信息详细
export function getWarehouseManager(id) {
  return request({
    url: '/system/platform/warehouseManager/' + id,
    method: 'get'
  })
}

// 新增场库管理人员信息
export function addWarehouseManager(data) {
  return request({
    url: '/system/platform/warehouseManager',
    method: 'post',
    data: data
  })
}

// 修改场库管理人员信息
export function updateWarehouseManager(data) {
  return request({
    url: '/system/platform/warehouseManager',
    method: 'put',
    data: data
  })
}

// 删除场库管理人员信息
export function delWarehouseManager(id) {
  return request({
    url: '/system/platform/warehouseManager/' + id,
    method: 'delete'
  })
}
